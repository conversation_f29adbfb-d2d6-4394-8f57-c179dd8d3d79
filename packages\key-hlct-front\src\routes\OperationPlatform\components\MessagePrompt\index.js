/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-11 09:39:58
 * @LastEditTime: 2023-07-11 14:41:17
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useCallback } from 'react';
import { debounce } from 'lodash';
import { Result } from 'choerodon-ui';
import intl from 'utils/intl';
import formatterCollections from 'utils/intl/formatterCollections';
import {  useOperationPlatform } from '../../contextsStore';
import { CardLayout } from '../commonComponents';
import speechSynthesis from '@/assets/operationPlatformCard/speechSynthesis.png';
import speechSynthesisnone from '@/assets/operationPlatformCard/speechSynthesisnone.png';
import styles from './index.modules.less';

const modelPrompt = 'tarzan.operationPlatform';

const DataAcquisition = () => {

  const synth = window.speechSynthesis;
  const message = new SpeechSynthesisUtterance();
  const { operationRecords, synthRead, dispatch } = useOperationPlatform();

  const voicePlaybackSuccess = useCallback(
    debounce((text) => {
      message.text = text;
      message.lang = 'zh';
      message.rate = 1; // 语速设置，数字越大越快
      synth.speak(message);
    }, 500),
    [],
  )

  const voicePlaybackFailed = useCallback(
    debounce((text) => {
      message.text = text;
      message.lang = 'zh';
      message.rate = 1; // 语速设置，数字越大越快
      synth.speak(message);
    }, 200),
    [],
  )


  useEffect(() => {
    if(!synthRead){
      return;
    }
    if (!operationRecords[operationRecords.length-1]?.messageType) {
      return;
    }
    if (operationRecords[operationRecords.length-1]?.messageType === 'SUCCESS') {
      voicePlaybackSuccess(`${intl.get(`${modelPrompt}.operation.success`).d('操作成功')}`);
    } else {
      voicePlaybackFailed(`${intl.get(`${modelPrompt}.operation.failed`).d('操作失败')}`);
    }
  }, [operationRecords, synthRead])

  const handleCahngeStatus = () => {
    dispatch({
      type: 'update',
      payload: {
        synthRead: !synthRead,
      },
    })
  }

  return (
    <CardLayout.Layout className={styles.traceabilityConfirmation}>
      <CardLayout.Header
        className='MessagePromptHead'
        title='信息提示'
        addonAfter={
          <div onClick={handleCahngeStatus}>
            {
              synthRead === true && <img src={speechSynthesis} alt='' className={styles.syntIcon} />
            }
            {
              synthRead === false && <img src={speechSynthesisnone} alt='' className={styles.syntIcon} />
            }
          </div>
        }
      />
      <CardLayout.Content className={`${styles.MessagePromptResult} MessagePromptForm`}>
        <div id={styles.MessagePromptResultContent}>
          {
            operationRecords[operationRecords.length-1]?.messageType === 'SUCCESS' && (
              <Result
                className={styles.MessagePromptResultContentSuccess}
                status='success'
                title={intl.get(`${modelPrompt}.operation.success`).d('操作成功')}
                subTitle={operationRecords[operationRecords.length-1].message}
              />
            )
          }
          {
            operationRecords[operationRecords.length-1]?.messageType === 'FAIL' && (
              <Result
                className={styles.MessagePromptResultContentError}
                status='error'
                title={intl.get(`${modelPrompt}.operation.failed`).d('操作失败')}
                subTitle={operationRecords[operationRecords.length-1].message}
              />
            )
          }
        </div>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(DataAcquisition);
