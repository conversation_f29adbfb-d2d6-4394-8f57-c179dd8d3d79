.machineMaterials {
  :global {
    .c7n-divider.c7n-divider-horizontal {
      margin: 0 0 !important;
    }

    .c7n-card-body {
      padding: 0 !important;
      background: #395470;
    }
  }
  :global {
    .c7n-pro-btn-disabled {
      background-color: rgb(191, 191, 191) !important;
      border-color: rgb(191, 191, 191) !important;

      &:hover {
        background-color: rgb(191, 191, 191) !important;
        border-color: rgb(191, 191, 191) !important;
      }
    }
  }
}

.form-box {
  background-color: rgba(42, 99, 130, 1);
  :global {
    .c7n-pro-field-label {
      color: #01e1ef !important;
    }
    .c7n-pro-field-wrapper,
    .c7n-pro-field-output,
    .c7n-pro-output {
      color: #ffffff !important;
    }
  }
}

.revoke-button {
  color: #ffffff !important;
  background-color: rgba(224, 190, 94, 1) !important;
  border-color: rgba(224, 190, 94, 1) !important;
  // outline-color: rgba(224, 190, 94, 1) !important;
}

.revoke-button {
  color: #ffffff !important;
  background-color: rgba(224, 190, 94, 1) !important;
  border-color: rgba(224, 190, 94, 1) !important;
  // outline-color: rgba(224, 190, 94, 1) !important;
}

.ok-button {
  color: #ffffff !important;
  background-color: rgba(17, 194, 207, 1) !important;
  border-color: rgba(17, 194, 207, 1) !important;
  // outline-color: rgba(17, 194, 207, 1) !important;
}

.feeding-table {
  :global {
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content table .c7n-pro-table-expanded-row .c7n-pro-table-cell-inner{
      height: auto !important;
      white-space: nowrap !important;
    }

    .c7n-pro-table-wrapper.c7n-pro-table-wrapper
                  .c7n-pro-table
                  .c7n-pro-table-content
                  .c7n-pro-table-row-current
                  + .c7n-pro-table-expanded-row
                  > .c7n-pro-table-cell,
                .c7n-pro-table-wrapper.c7n-pro-table-wrapper
                  .c7n-pro-table
                  .c7n-pro-table-content
                  .c7n-pro-table-row:hover
                  + .c7n-pro-table-expanded-row
                  > .c7n-pro-table-cell {
                  background-color: rgba(56, 112, 143, 1) !important;
                }
    .c7n-pro-table-wrapper {
      .c7n-pro-table {
        .c7n-pro-table-content {
          table {
            .c7n-pro-table-thead {
              tr {
                th.c7n-pro-table-cell {
                  background-color: rgba(42, 99, 130, 1) !important;
                  color: rgba(51, 241, 255, 0.85);
                  opacity: 0.65;
                }
              }
            }
            .c7n-pro-table-tbody {
              tr {
                // background-color: rgba(56, 112, 143, 1) !important;
                td.c7n-pro-table-cell {
                  background-color: rgba(56, 112, 143, 1);
                  background-image: none;
                  color: #fff;
                  .c7n-pro-table-cell-inner-editable {
                    background-color: transparent !important;
                    background-image: none;
                    color: #fff;
                  }
                  .c7n-pro-input-number {
                    color: #fff !important;
                  }
                  .revoke-input {
                    color: red;
                    .c7n-pro-input-number {
                      color: rgba(224, 190, 94, 1) !important;
                      font-weight: bolder;
                    }
                  }
                }
              }
              .c7n-pro-table-row-current {
                td.c7n-pro-table-cell {
                  // background-color: rgba(56, 112, 143, 0.8) !important;
                }
              }
              .feeding-table-sub.c7n-pro-table-wrapper {
                .c7n-pro-table {
                  .c7n-pro-table-content {
                    table {
                      .c7n-pro-table-thead {
                        tr {
                          th.c7n-pro-table-cell {
                            color: rgba(255, 255, 255, 1);
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
          .c7n-pro-table-editor {
            .c7n-pro-input-number-wrapper.c7n-pro-input-number-focused,
            .c7n-pro-input-wrapper.c7n-pro-input-focused {
              input {
                color: #fff !important;
              }
              color: #fff !important;
            }
          }
        }
      }
    }

    .c7n-pro-table-content table .c7n-pro-table-tfoot tr th,
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper
      .c7n-pro-table
      .c7n-pro-table-content
      table
      .c7n-pro-table-thead
      tr
      th {
      // background-color: transparent;
    }
  }
}

#WorkOrderFeeding{
  .backFlashType{
    :global{
      .c7n-pro-table-cell .c7n-pro-table-cell-inner{
        color: rgba(255, 255, 255, 0.5) !important;
      }
    }
  }
}
.row-tag-box {
  position: relative;
  overflow: hidden;
}
.row-tag-item {
  color: #fff !important;
  position: absolute;
  top: 0;
  right: -30px;
  font-size: 12px;
  height: 16px;
  line-height: 16px;
  text-align: center;
  width: 90px;
  transform: rotate(45deg);
  background-color: rgba(224, 190, 94, 1);
}

.material-value {
  margin-right: 6px;
}

.machinedPartModal {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .c7n-pro-table-wrapper.c7n-pro-table-wrapper
      .c7n-pro-table
      .c7n-pro-table-content
      .c7n-pro-table-row {
      height: 45px !important;
    }

    .icon-refresh {
      background-color: rgb(91, 136, 160) !important;
      color: white !important;
    }

    .c7n-pro-modal-content
      .c7n-pro-modal-body
      .c7n-spin-nested-loading
      .c7n-spin-container
      .c7n-pro-table-content
      .c7n-pro-table-thead
      .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      border: none !important;
      font-size: 17px !important;
    }

    .c7n-pro-modal-content
      .c7n-pro-modal-body
      .c7n-spin-nested-loading
      .c7n-spin-container
      .c7n-pro-table-content
      .c7n-pro-table-tbody
      .c7n-pro-table-cell {
      // background-color: #3c87ad !important;
      color: white !important;
      font-size: 17px !important;
    }

    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .c7n-pro-btn-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .icon {
      color: white !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
    }
  }
}

.modalForm {
  :global {
    .c7n-pro-field-label {
      color: white !important;
    }

    .c7n-pro-input-wrapper.c7n-pro-input-focused,
    .c7n-pro-input-wrapper {
      background: #50819c !important;
      color: white !important;
      input {
        background: #50819c !important;
        color: white !important;
      }
    }
    .c7n-pro-select-wrapper {
      background: #50819c !important;
      color: white !important;
    }

    .c7n-pro-select {
      color: white !important;
    }
  }
}

.qty-box {
  position: relative;
  height: 40px;
  :global {
    .qty-bg {
      padding: 10px 0;
      width: 100%;
      height: 100%;
      .qty-bg-box {
        width: 100%;
        height: 100%;
        overflow: hidden;
        border-radius: 99px;
        background-color: rgba(255, 255, 255, 0.5);
        .qty-bg-inner {
          background-color: rgba(17, 194, 207, 1);
          height: 100%;
        }
      }
    }
    .qty-front {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: 1;
    }
  }
}

.current-line {
  background-color: rgba(17, 194, 207, 0.5) !important;
  opacity: 0.5;
}

#WorkOrderFeeding{
  :global{
    .c7n-pro-table-wrapper.feeding-table-sub{
      .c7n-pro-table-content{
        .c7n-pro-table-thead.c7n-pro-table-column-resizable{
          display: none;
        }
        .c7n-pro-table-tbody{
          .c7n-pro-table-row{
            .c7n-pro-table-cell{
              border: 1px solid #fff !important;
            }
          }
        }
      }
    }
  }
}
