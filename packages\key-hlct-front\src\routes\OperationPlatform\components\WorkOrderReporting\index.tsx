/**
 * @Description: 工单报工卡片
 * @Author: <<EMAIL>>
 * @Date: 2023-07-26 14:40:22
 * @LastEditTime: 2023-08-04 16:23:26
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useMemo, useEffect, useCallback, useRef } from 'react';
import { TextField, DataSet, Lov, CheckBox, Output, Form } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import lovChoose from '@/assets/operationPlatformCard/lovChoose.svg';
import { useOperationPlatform, DispatchType } from '../../contextsStore';
import { useResizeObserver } from '../../useResizeObserver';
import { CardLayout, NumberKeyboard, ONotification, useRequest } from '../commonComponents';
import { detailDS } from './stores';
import { FetchWoDetail, ScanMaterialLot, CompleteWo } from './services';
import { QueryCardList } from '../../services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.operationPlatform.workOrderReporting';

const WorkOrderReporting = (props: any) => {
  const { dispatch, enterInfo, workOrderData, cardMode } = useOperationPlatform();
  const { cardCode, handleAddRecords } = props;

  const materialLotInput = useRef<HTMLInputElement>(null);

  const [checked, setChecked] = useState<boolean>(false);
  // const [workOrderData, setWorkOrderData] = useState<{ [kay: string]: any }>({}); // 工单数据
  const [materialLotData, setMaterialLotData] = useState<{ [kay: string]: any }>({}); // 工单数据
  const [renderProps, setRenderProps] = useState(Array<[string, any]>);
  const [formColumns, setFormColumns] = useState(1); // 工单信息列数

  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const { run: fetchWoDetail, loading: fetchWoDetailLoading } = useRequest(FetchWoDetail(), {
    manual: true,
    needPromise: true,
  });
  const { run: scanMaterialLot, loading: scanMaterialLotLoading } = useRequest(ScanMaterialLot(), {
    manual: true,
    needPromise: true,
  });
  const { run: completeWo, loading: completeWoLoading } = useRequest(CompleteWo(), {
    manual: true,
    needPromise: true,
  });
  const { run: queryCardList, loading: queryCardListLoading } = useRequest(QueryCardList(), {
    manual: true,
    needPromise: true,
  });

  useResizeObserver((element, size) => {
    // console.log('Element size:', size, element);
    if (size.width <= 480) {
      setFormColumns(1);
    } else if (size.width > 480 && size.width <= 780) {
      setFormColumns(1);
    } else if (size.width > 780 && size.width <= 1090) {
      setFormColumns(2);
    } else {
      setFormColumns(3);
    }
    // 在这里执行你需要的操作，例如重新布局、更新状态等。
  }, document.querySelector('.WorkOrderReportingForm'));

  useEffect(() => {
    detailDs.loadData([workOrderData || {}]);
  }, [workOrderData]);

  useEffect(() => {
    // 设置 WO Lov查询条件
    detailDs.current?.set('workOrderLovLimit', {
      workcellId: enterInfo?.workStationId,
      operationId: enterInfo?.selectOperation?.operationId,
    });
    // 查询卡片配置数据
    queryCardList({
      params: {
        cardCode,
      },
    }).then(res => {
      if (res && !res.failed) {
        try {
          const cardConfig = Object.entries(JSON.parse(res[0].cardConfiguration));
          setRenderProps(cardConfig);
        } catch (error) {
          ONotification.error({ message: intl.get(`${modelPrompt}.fieldConfigurationError`).d('cardConfiguration字段配置错误！') });
        }
      }
    });
  }, []);

  const handleChangeWo = useCallback(
    value => {
      setChecked(false);
      dispatch({
        type: DispatchType.update,
        payload: {
          woReportDetail: {},
          workOrderData: {},
        },
      });
      detailDs.current?.set('materialLot', null);
      if (!value) {
        return;
      }
      return fetchWoDetail({
        params: {
          workOrderId: value.workOrderId,
          operationId: enterInfo?.selectOperation?.operationId,
        },
      }).then(res => {
        if (res && !res.failed) {
          handleAddRecords({
            cardCode,
            messageType: 'SUCCESS',
            message: `${intl.get(`${modelPrompt}.cardQuery`).d('工单报工卡片，查询工单')}${value.workOrderNum}${intl.get(`${modelPrompt}.success`).d('成功')}`,
          });
          dispatch({
            type: DispatchType.update,
            payload: {
              woReportDetail: res,
              workOrderData: res,
            },
          });
          materialLotInput.current?.focus();
        } else {
          handleAddRecords({
            cardCode,
            messageType: 'FAIL',
            message: `${intl.get(`${modelPrompt}.cardQuery`).d('工单报工卡片，查询工单')}${value.workOrderNum}${intl.get(`${modelPrompt}.failure`).d('失败')}`,
          });
        }
      });
    },
    [detailDs, materialLotInput.current, enterInfo?.selectOperation?.operationId],
  );

  const onScanMaterialLot = useCallback(
    e => {
      const _value = e.target.value.trim();
      setMaterialLotData({});
      setChecked(false);
      if (!_value) {
        return;
      }
      scanMaterialLot({
        params: {
          workOrderId: workOrderData?.workOrderId,
          materialLotCode: _value,
        },
      }).then(res => {
        if (res && !res.failed) {
          setChecked(true);
          setMaterialLotData(res);
          dispatch({
            type: DispatchType.update,
            payload: {
              woReportDetail: {
                ...workOrderData,
                lastCompleted: `${res.materialLotCode}/${_value}`,
              },
              workOrderData: {
                ...workOrderData,
                lastCompleted: `${res.materialLotCode}/${_value}`,
              },
            },
          });
          handleAddRecords({
            cardCode,
            messageType: 'SUCCESS',
            message: `${intl.get(`${modelPrompt}.cardScanMaterialLot`).d('工单报工卡片，扫描物料批')}${_value}${intl.get(`${modelPrompt}.success`).d('成功')}`,
          });
          return Promise.resolve();
        }
        handleAddRecords({
          cardCode,
          messageType: 'FAIL',
          message: `${intl.get(`${modelPrompt}.cardScanMaterialLot`).d('工单报工卡片，扫描物料批')}${_value}${intl.get(`${modelPrompt}.failure`).d('失败')}`,
        });
        return Promise.reject();
      });
    },
    [workOrderData],
  );

  const onCompleteWo: (value: string) => boolean = (value: string) => {
    if (!value || !workOrderData?.workOrderId) {
      return false;
    }
    return completeWo({
      params: {
        ...workOrderData,
        ...materialLotData,
        workcellId: enterInfo.workStationId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        operationId: enterInfo?.selectOperation?.operationId,
        inputQty: value,
      },
    }).then(res => {
      if (res && !res.failed) {
        handleAddRecords({
          cardCode,
          messageType: 'SUCCESS',
          message: `${intl.get(`${modelPrompt}.cardSuccessfulExecution`).d('工单报工卡片，执行完工成功')}`,
        });
        ONotification.success({});
        return true;
      }
      handleAddRecords({
        cardCode,
        messageType: 'FAIL',
        message: `${intl.get(`${modelPrompt}.cardFailureExecution`).d('工单报工卡片，执行完工失败')}`,
      });
      return false;
    });
  };

  return (
    <CardLayout.Layout
      spinning={
        queryCardListLoading || fetchWoDetailLoading || scanMaterialLotLoading || completeWoLoading
      }
    >
      <CardLayout.Header
        className='WorkOrderReportingHead'
        title={intl.get(`${modelPrompt}.title`).d('工单报工')}
        help={props?.cardUsage?.remark}
        content={
          <>
            <TextField
              name="materialLot"
              placeholder={intl.get(`${modelPrompt}.scanMaterialLot`).d('请扫描物料批')}
              dataSet={detailDs}
              // @ts-ignore
              ref={materialLotInput}
              onEnterDown={onScanMaterialLot}
              prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />}
            />
          </>
        }
        addonAfter={<div id={styles.processReportingRecords}><CheckBox checked disabled={!checked} /></div>}
      />
      <CardLayout.Content className='WorkOrderReportingForm'>
        <div style={{ display: 'flex', width: '100%' }}>
          {/* <CardCustomizeForm
            style={{ width: '200px', flex: 1 }}
            renderProps={renderProps}
            responseData={workOrderData}
          /> */}
          <Form dataSet={detailDs} labelWidth={130} columns={cardMode === 'Tile' ? formColumns : 4} style={{ minWidth: '200px', flex: 1 }}>
            <Lov
              name="workOrderLov"
              placeholder={intl.get(`${modelPrompt}.selectWo`).d('请选择WO')}
              dataSet={detailDs}
              style={{ marginRight: '8px' }}
              modalProps={{
                contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
                className: styles.workOrderModals,
                okProps: {
                  style: {
                    background: '#1b7efc',
                  },
                },
                cancelProps: {
                  style: {
                    background: '#50819c',
                    color: 'white',
                  },
                },
              }}
              onChange={handleChangeWo}
              suffix={<img src={lovChoose} alt='' style={{ height: '19px' }} />}
            />
            {renderProps.map(item => (
              item[0] !== 'workOrderNum' && item[1].labelVisible && <Output name={item[0]} />
            ))}
          </Form>
          <NumberKeyboard keyboardWidth={256} onOk={onCompleteWo} />
        </div>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default WorkOrderReporting;
