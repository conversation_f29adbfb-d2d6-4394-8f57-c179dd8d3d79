.keyboard {
  flex-grow: 0;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  color: white;
  background: #4c83a1;
  border-radius: 4px;
  padding-bottom: 4px;

  .keyboardRow {
    margin: 2px;
    margin: 2px;
    flex: 1 1;
    display: inline-flex;

    ::selection, body ::selection{
      background: #11C2CF !important;
      color: white;
    }

    .inputArea {
      position: relative;
      padding: 0 28px 0 6px;
      width: 75%;
      background: #2c6584;
      margin: 8px 2px 2px 4px;
      border: 1px solid gray;
      display: inline-flex;
      align-items: center;
      font-size: 1.2vw;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      outline: none;
      transition: all .5s;

      &:focus {
        border: 1px solid blue;
        box-shadow: 0px 0px 2px 2px #5c5cf2;
        transition: all .5s;
      }

      i {
        position: absolute;
        right: 4px;
        font-size: 1.2vw;
        cursor: pointer;
      }
    }

    .uom {
      width: 25%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2vw;
      margin: 8px 2px 2px;
      color: #fff;
    }

    .normalBtn {
      background: #2c6584;
      width: 25%;
      margin: 2px 4px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.2vw;
      cursor: pointer;
      transition: all .5s;

      &:hover {
        opacity: .6;
        transition: all .5s;
      }
    }

    .submitBtn {
      background: #00bbb4;
      color: #fff;
      width: calc(50% + 8px);
    }

    .btnDisabled{
      background: #608da5;
      color: #fff;
      width: calc(50% + 8px);
    }

    .backBtn{
      color: #608da5;
    }
    .backDisabled{
      color: #fff;
    }
  }
}

.fontDisabled{
  color: #608da5;
}
