.operationPlatformSetting {
  display: flex;
  flex-direction: column;
  // height: 76%;
  // min-height: 600px;
  padding: 0;
  background-color: #38708F;
  color: #fff;
  font-size: 16px;
  position: fixed;
  z-index: 109;
  top: 110px;
  bottom: 60px;
  left: 200px;
  right: 30px;
  // margin: auto;
  border: 0;
  border-radius: 2px;
  box-shadow: 0 0.04rem 0.12rem rgba(0, 0, 0, 0.12);
  .cardBackground{
    position: absolute;
    top: 0;
    right: 0;
    height: 241px;
  }
  &-full{
    top: 50px;
    bottom: 60px;
    left: 30px;
    right: 30px;
  }

  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background-color: transparent;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 14px;
    background: #88a9bc;
  }

  // 头部
  .selectModalTitle {
    width: 100%;
    height: 45px;
    display: flex;
    align-items: center;
    background: linear-gradient(to right, rgba(99, 242, 255, 0.74) 0%, rgba(80, 234, 242, 0.55) 13.43%, rgba(48, 97, 219, 0.01) 100%);
    background-size: cover;
    padding: 8px 16px;

    img {
      margin-right: 4px;
    }
  }

  // 主体
  .cardContent {
    flex: 1;
    padding: 16px;
    padding-bottom: 0;
    overflow-y: auto;
    color: #fff;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    .cardTop {
      width: 100%;

      .cardTitle {
        height: 36px;
      }

      .cardItem {
        width: 100%;
        display: flex;
        align-items: center;

        .cardItems {
          width: 181px;
          height: 134px;
          margin-right: 36px;
          background-size: contain !important;
          position: relative;
          cursor: pointer;

          div {
            position: absolute;
            bottom: 4px;
            left: 36px;
          }
        }
      }
    }

    .totalSelect {
      display: flex;
      align-items: center;
      margin-right: 12px;

      .cardTitle {
        font-size: 16px;
        font-weight: 500;
      }

      .cardItem {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.5);

        span {
          color: #33F1FF;
        }
      }
    }

    .cardCenter {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .tabHeader {
        height: 56px;
        width: 100%;
        padding-bottom: 8px;
        display: inline-flex;

        .tabs {
          flex: 1;
          display: inline-flex;
          gap: 12px;
          align-items: center;

          .tab {
            cursor: pointer;

            &:hover {
              color: rgba(51, 241, 255, 1);
            }
          }

          .tabActive {
            .tab();
            color: rgba(51, 241, 255, 1);
          }
        }

        .primaryCardBtn {
          cursor: pointer;
          width: 100px;
        }
      }

      .tabContent {
        // flex: 1;
        display: flex;
        flex-wrap: wrap;
        overflow: hidden auto;
        gap: 12px;

        .previewImgCard {
          position: relative;
          width: 178px;
          height: 102px;
          box-shadow: 0 6px 9px 0 #19567491 !important;
          cursor: pointer;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
          }

          .cardName {
            position: absolute;
            font-size: 12px;
            text-align: center;
            width: 100%;
            bottom: 0;
            z-index: 10;
            height: 20px;
            line-height: 20px;
            background: linear-gradient(90deg, rgba(56, 112, 143, 0) 0%, rgba(29, 73, 97, 1) 49.05%, rgba(63, 117, 158, 0) 100%);
          }

          .cardSelect {
            width: 40px;
            height: 40px;
            background-color: #4be3ee;
            border-radius: 100%;
            position: absolute;
            top: -22px;
            right: -18px;

            .squareOne {
              width: 11px;
              height: 7px;
              background-color: #fff;
              position: absolute;
              bottom: 9px;
              left: 10px;
              border-radius: 2px;
              transform: rotateZ(-45deg);

              .squareTwo {
                width: 17px;
                height: 12px;
                background-color: #4be3ee;
                position: absolute;
                bottom: 3px;
                left: 3px;
                border-radius: 2px;
              }
            }
          }

          .cardSelect2 {
            width: 40px;
            height: 40px;
            background-color: rgba(255, 216, 110, 1);
            border-radius: 100%;
            position: absolute;
            top: -22px;
            right: -18px;

            .squareOne {
              width: 11px;
              height: 7px;
              background-color: #fff;
              position: absolute;
              bottom: 9px;
              left: 10px;
              border-radius: 2px;
              transform: rotateZ(-45deg);

              .squareTwo2 {
                width: 17px;
                height: 12px;
                background-color: rgba(255, 216, 110, 1);
                position: absolute;
                bottom: 3px;
                left: 3px;
                border-radius: 2px;
              }
            }
          }
        }

        .previewImgCardSelected {
          .previewImgCard();
          border: 1px solid rgba(75, 227, 238, 1);
        }
      }
    }

    .cardBottom {
      width: 100%;

      .cardItem {
        font-size: 16px;
        display: flex;
        align-items: center;

        .cardTitle {
          width: 100px;
          margin-right: 36px;
        }

        .cardItems {
          flex: 1;
          // height: 45px;
          margin-top: 16px;
          margin-right: 36px;
          display: flex;
          align-items: center;
          flex-wrap: wrap;
        }
      }

      :global {
        .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper {
          color: #fff !important;
          margin-right: 16px !important;
        }

        .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper:not(.c7n-pro-checkbox-button) .c7n-pro-checkbox:checked+.c7n-pro-checkbox-inner {
          background: #4BE3EE !important;
          border-color: #4BE3EE !important;
        }

        .c7n-pro-checkbox-inner {
          background-color: #608da5 !important;
          border-color: transparent !important;
        }
      }
    }
  }

  // 底部
  .cardFooter {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 16px;

    :global {
      .c7n-pro-btn.c7n-pro-btn-primary {
        background: rgba(17, 194, 207, 1);
        color: white;
        border-color: rgba(17, 194, 207, 1);
      }

      .c7n-pro-btn.c7n-pro-btn-primary.c7n-pro-btn-raised:not(.c7n-pro-btn-disabled):not(.c7n-pro-btn-loading):hover {
        background: rgba(17, 194, 207, 1);
        color: white;
        border-color: rgba(17, 194, 207, 1);
      }

      .c7n-pro-btn.c7n-pro-btn-default {
        background: rgba(50, 97, 127, 1);
        color: white;
        border-color: rgba(50, 97, 127, 1);
      }

      .c7n-pro-btn.c7n-pro-btn-default.c7n-pro-btn-raised:not(.c7n-pro-btn-disabled):not(.c7n-pro-btn-loading):hover {
        background: rgba(50, 97, 127, 1);
        color: white;
        border-color: rgba(50, 97, 127, 1);
      }
    }
  }
}
