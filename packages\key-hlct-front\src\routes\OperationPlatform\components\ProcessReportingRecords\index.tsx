/**
 * @Description: 工序报工记录卡片-工序报工记录
 * @Author: <<EMAIL>>
 * @Date: 2023-07-26 14:40:22
 * @LastEditTime: 2023-08-04 16:23:26
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useMemo, useEffect, useCallback, useRef } from 'react';
import {
  TextField,
  DataSet,
  // Lov,
  CheckBox,
  Button,
  Form,
  Output,
  NumberField,
  Table,
  Radio,
  Tooltip,
} from 'choerodon-ui/pro';
// import { Badge } from 'choerodon-ui';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import moment from 'moment';
import intl from 'utils/intl';
import { CardLayout, NumberKeyboard, ONotification, useRequest } from '../commonComponents';
import { detailDS, processDS, printDS, stepDS, materialDS, workOrderLovDS } from './stores';
import leftTopLog from '@/assets/operationPlatformCard/leftTopLog.svg';
import codePrint from '@/assets/operationPlatformCard/codePrint.png';
import close from '@/assets/operationPlatformCard/close.png';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import lovChoose from '@/assets/operationPlatformCard/lovChoose.svg';
import enableY from '@/assets/operationPlatformCard/enableY.svg';
import enableN from '@/assets/operationPlatformCard/enableN.svg';
import cardSvg from '@/assets/icons/operation.svg';
import qualityNg from '@/assets/operationPlatformCard/qualityNg.svg';
import qualityOk from '@/assets/operationPlatformCard/qualityOk.svg';
import qualityPend from '@/assets/operationPlatformCard/qualityPend.svg';
import { useOperationPlatform, DispatchType } from '../../contextsStore';
import { useResizeObserver } from '../../useResizeObserver';
// import { TemplatePrintButton } from '../../../../components/tarzan-ui';
import TemplatePrintButton from '../../TemplatePrintButton';
import { FetchWoDetail, ScanMaterialLot, CompleteWo, ReturnWo, FetchMaterialLotQty } from './services';
import C7nModal from '../../C7nModal';
// import { QueryCardList } from '../../services';
import styles from './index.module.less';

const modelPrompt = 'tarzan.operationPlatform';

let woModal;
const WorkOrderReporting = (props: any) => {
  const Modal = C7nModal;
  const { cardCode, handleAddRecords } = props;
  const { enterInfo, dispatch, cardMode } = useOperationPlatform();

  const materialLotInput = useRef<HTMLInputElement>(null);

  const printRef = useRef<any>(null);
  const [autoFlag, setAutoFlag] = useState(false);
  const autoFlagRef = useRef(autoFlag);
  useEffect(() => {
    autoFlagRef.current = autoFlag;
  }, [autoFlag]);
  const [checked, setChecked] = useState<boolean>(false)
  const [workOrderData, setWorkOrderData] = useState<{ [kay: string]: any }>({}); // 工单数据
  const [materialLotData, setMaterialLotData] = useState<{ [kay: string]: any }>({}); // 工单数据
  // const [renderProps, setRenderProps] = useState(Array);
  // const [completeNumObj, setCompleteNumObj] = useState({}); // 完工数量key样式
  // const [completeValueObj, setCompleteValueObj] = useState({}); // 完工数量value样式
  const [flagModal, setFlagModal] = useState(''); // 报工退回展示的弹框-是否为末道序'N''Y'
  const [numValue, setNumValue] = useState(''); // 当前键盘输入的值
  const [printModalShow, setPrintModalShow] = useState(false); // 是否展示打印弹框
  const [fetchPrintLoading, setFetchPrintLoading] = useState(false); // 请求数据loading
  const [stepInfoData, setStepInfoData] = useState(Array); // 工序步骤数据
  const [stepModal, setStepModal] = useState(false); // 是否展示步骤弹框
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const materialDs = useMemo(() => new DataSet(materialDS()), []);
  const processDs = useMemo(() => new DataSet(processDS()), []);
  const stepDs = useMemo(() => new DataSet(stepDS()), []); // 工序步骤数据
  const printDs = useMemo(() => new DataSet(printDS()), []);
  const workOrderLovDs = useMemo(() => new DataSet(workOrderLovDS()), []);
  const [samePrintState, setSamePrintState] = useState(false);
  const [sameAgainState, setSameAgainState] = useState(false);
  const [trendsNum, setTrendsNum] = useState(''); // 工单趋势数量
  const trendsNumRef = useRef(trendsNum);
  const [formColumns, setFormColumns] = useState(1); // 工单信息列数
  const { run: fetchWoDetail, loading: fetchWoDetailLoading } = useRequest(FetchWoDetail(), { manual: true, needPromise: true });
  const { run: scanMaterialLot, loading: scanMaterialLotLoading } = useRequest(ScanMaterialLot(), { manual: true, needPromise: true });
  const { run: completeWo, loading: completeWoLoading } = useRequest(CompleteWo(), { manual: true, needPromise: true });
  const { run: returnWo, loading: returnWoLoading } = useRequest(ReturnWo(), { manual: true, needPromise: true });
  const { run: fetchMaterialLotQty } = useRequest(FetchMaterialLotQty(), { manual: true, needPromise: true });
  // const { run: queryCardList, loading: queryCardListLoading } = useRequest(QueryCardList(), { manual: true, needPromise: true });

  useResizeObserver((element, size) => {
    // console.log('Element size:', size, element);
    if (size.width <= 480) {
      setFormColumns(1);
    } else if (size.width > 480 && size.width <= 780) {
      setFormColumns(1);
    } else if (size.width > 780 && size.width <= 1090) {
      setFormColumns(2);
    } else {
      setFormColumns(3);
    }
    // 在这里执行你需要的操作，例如重新布局、更新状态等。
  }, document.querySelector('.ProcessReportingRecordsForm'));

  useEffect(() => {
    trendsNumRef.current = trendsNum;
  }, [trendsNum])

  useEffect(() => {
    // 还原一些状态
    setFlagModal('');
    setNumValue('');
    setPrintModalShow(false);
    setStepModal(false);
    setTrendsNum('');
    // 设置 WO Lov查询条件
    // detailDs.current?.set('workOrderLovLimit', {
    //   // workcellId: enterInfo.workStationId,
    //   // operationId: enterInfo?.selectOperation?.operationId,
    //   prodLineId: enterInfo.productionLineId,
    // })
    // 查询卡片配置数据
    // queryCardList({
    //   params: {
    //     cardCode,
    //   },
    // }).then(res => {
    //   if (res && !res.failed) {
    //     try {
    //       const cardConfig = Object.entries(JSON.parse(res[0].cardConfiguration));
    //       setRenderProps(cardConfig);
    //       // console.log('cardConfig', cardConfig);
    //     } catch (error) {
    //       ONotification.error({ message: "cardConfiguration字段配置错误！" });
    //     }
    //   }
    // })
  }, []);

  useEffect(() => {
    detailDs.loadData([workOrderData]);
  }, [workOrderData]);

  useEffect(() => {
    if (flagModal === 'Y') {
      processDs.current?.set('maxQty', null);
      processDs.current?.set('qty', null);
      processDs.current?.set('materialLotId', null);
      processDs.current?.set('barCode', null);
      processDs.current?.set('requestTypeCode', null);
    }
  }, [flagModal]);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (printDs) {
      const header = flag ? printDs.addEventListener : printDs.removeEventListener;
      // 头选中和撤销监听
      header.call(printDs, 'select', handleTableDsSelect);
      header.call(printDs, 'unSelect', handleTableDsSelect);
      header.call(printDs, 'unSelectAll', handleTableDsSelect);
      // 列表加载事件
      header.call(printDs, 'load', handleTableDsSelect);
    }
  };

  const handleTableDsSelect = () => {
    // NO1-GW-SL001
    const firstSelect = printDs.selected
    if (!firstSelect.length) {
      printDs.records.map(item => {
        item.selectable = true;
        return item;
      });
      setSamePrintState(false);
      setSameAgainState(false);
    } else {
      printDs.records.map(item => {
        item.selectable = item.get('printStatus') === firstSelect[0].get('printStatus');
        return item;
      });
      const _printData = firstSelect.every(item => item.get('printStatus') === '未打印');
      const _againData = firstSelect.every(item => item.get('printStatus') === '已打印');
      setSamePrintState(_printData);
      setSameAgainState(_againData);
    }
  }

  const handleChangeWo = useCallback(
    (value) => {
      setChecked(false);
      setWorkOrderData({});
      setMaterialLotData({});
      const container = document.querySelector(`.${styles.doneStepFlagModals}`);
      if (container) {
        document.querySelector('.ProcessReportingRecordsForm')?.appendChild(container);
        setStepModal(false);
      }
      setTrendsNum('');
      dispatch({
        type: DispatchType.update,
        payload: {
          woReportDetail: {},
          workOrderData: {},
        },
      });
      detailDs.current?.set('materialLot', null);
      if (!value) {
        return;
      }
      return fetchWoDetail({
        params: {
          workOrderId: value.workOrderId,
          operationId: enterInfo?.selectOperation?.operationId,
          routerStep: stepDs.current?.get('routerStep') || null,
          shiftCode: enterInfo?.shiftCode,
          shiftDate: enterInfo?.shiftDate,
          workcellId: enterInfo?.workStationId,
        },
      }).then(res => {
        if (res && res.success) {
          handleAddRecords({
            cardCode,
            messageType: 'SUCCESS',
            message: intl.get(`${modelPrompt}.process.reporting.records.card.query.success`).d(`工序报工记录卡片，查询工单${value.workOrderNum}成功`),
          });
          setWorkOrderData(res.rows);
          dispatch({
            type: DispatchType.update,
            payload: {
              woReportDetail: res?.rows || {},
              workOrderData: res?.rows || {},
            },
          });
          if (res.rows.stepInfoList?.length) {
            setStepInfoData(res.rows.stepInfoList);
            setStepModal(true);
          }
          if (res.rows.doneStepFlag === 'Y') {
            materialLotInput.current?.focus();
          }
        } else {
          handleAddRecords({
            cardCode,
            messageType: 'FAIL',
            message: intl.get(`${modelPrompt}.process.reporting.records.card.query.failed`).d(`工序报工记录卡片查询工单${value.workOrderNum}失败`),
          });
        }
      })
    },
    [detailDs, materialLotInput.current, enterInfo?.selectOperation?.operationId],
  )

  const columnWo = [
    {
      name: 'workOrderNum',
    },
    {
      name: 'woRenderQty',
      renderer: ({ record }) => {
        return `${record?.get('woCompletedQty')}/${record?.get('woQty')}`;
      }
    },
    {
      name: 'materialInfo',
    },
    {
      name: 'materialName',
    },
    {
      name: 'planTime',
      width: 300,
      renderer: ({ record }) => {
        return `${moment(record?.get('planStartTime')).format('YYYY-MM-DD HH:mm')}-${moment(record?.get('planEndTime')).format('YYYY-MM-DD HH:mm')}`;
      },
    }
  ];

  const onRow = ({ record }) => {
    return {
      onClick: () => {
        workOrderLovDs.data.forEach(item => {
          item.isSelected = false;
        });
        record.isSelected = true;
      },
      onDoubleClick: () => {
        woModal.close();
        handleChangeWo(record.data);
      }
    };
  };

  const handleFetchLovData = (value?) => {
    workOrderLovDs.queryDataSet?.current?.reset();
    if (value === 'click') {
      workOrderLovDs.setQueryParameter('workOrderNum', null);
    } else if (value) {
      workOrderLovDs.setQueryParameter('workOrderNum', value);
    } else {
      workOrderLovDs.setQueryParameter('workOrderNum', null);
      materialDs.loadData([]);
      setChecked(false);
      setWorkOrderData({});
      setMaterialLotData({});
      setTrendsNum('');
      return;
    }
    workOrderLovDs.setQueryParameter('prodLineId', enterInfo.productionLineId);
    workOrderLovDs.query().then(res => {
      if (res && !res.failed) {
        if (res.content.length === 1) {
          handleChangeWo(res.content[0]);
          return
        }
        if (res.content.length === 0) {
          return ONotification.error({ message: intl.get(`${modelPrompt}.no.found.wo`).d("未查询到WO") });
        }
        woModal = Modal.open({
          header: (
            <div style={{ display: 'flex' }} className="header">
              <img src={cardSvg} alt="" className="titleIcon" />
              <div className="c7n-pro-modal-title">{intl.get(`${modelPrompt}.workOrder`).d('工单')}</div>
            </div>
          ),
          destroyOnClose: true,
          closable: false,
          style: {
            width: '95%',
          },
          mask: true,
          contentStyle: {
            background: '#38708F',
          },
          className: styles.workOrderTextModals,
          children: <Table dataSet={workOrderLovDs} columns={columnWo} onRow={onRow} />,
          okProps: {
            style: {
              background: '#00D4CD !important',
              color: 'white !important',
              borderColor: '#00d4cd !important',
            },
          },
          onOk: () => {
            handleChangeWo(workOrderLovDs.selected[0].data);
          },
        })
      }
    });
  }

  const onScanMaterialLot = useCallback(
    (e, type?) => {
      const _value = e.target.value.trim();
      setMaterialLotData({})
      setChecked(false);
      if (!_value) {
        return;
      }
      scanMaterialLot({
        params: {
          workOrderId: workOrderData.workOrderId,
          materialLotCode: _value,
        },
      }).then(res => {
        if (res && res.success) {
          setChecked(true);
          setMaterialLotData(res.rows)
          console.log('res', res.rows, type, workOrderData.doneStepFlag, autoFlagRef.current);
          if (type === 'onCompleteWo' && workOrderData.doneStepFlag === 'Y' && autoFlagRef.current) {
            printRef.current.print();
          }
          // setWorkOrderData({
          //   ...workOrderData,
          //   materialLot: _value,
          //   lastCompletedQty: workOrderData.lastCompletedQty + (trendsNumRef.current ? Number(trendsNumRef.current) : 0),
          //   lastCompleted: `${res.rows.materialLotCode}/${_value}`,
          // })
          // setTrendsNum('');
          dispatch({
            type: DispatchType.update,
            payload: {
              woReportDetail: {
                ...workOrderData,
                lastCompleted: `${res.materialLotCode}/${_value}`,
              },
              workOrderData: {
                ...workOrderData,
                lastCompleted: `${res.materialLotCode}/${_value}`,
              },
            },
          });
          materialDs.loadData([res.rows]);
          handleAddRecords({
            cardCode,
            messageType: 'SUCCESS',
            message: `工序报工记录卡片，扫描物料批${_value}成功`,
          });
          return Promise.resolve();
        }
        handleAddRecords({
          cardCode,
          messageType: 'FAIL',
          message: `工序报工记录卡片，扫描物料批${_value}失败`,
        });
        return Promise.reject();
      })
    },
    [workOrderData],
  )

  const onCompleteWo: (value: string) => boolean = (value: string) => {
    if (!value || !workOrderData.workOrderId || Number(value) <= 0) {
      return false;
    }
    setNumValue(value);
    return completeWo({
      params: {
        ...workOrderData,
        materialLotCode: detailDs.current?.get('materialLot') ? materialLotData?.materialLotCode : '',
        workcellId: enterInfo?.workStationId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        inputQty: value,
      },
    }).then((res => {
      if (res && res.success) {
        ONotification.success({});
        setWorkOrderData({
          ...workOrderData,
          lastCompletedQty: workOrderData.lastCompletedQty + (trendsNum ? Number(trendsNum) : 0),
        })
        setTrendsNum(`+${value}`)
        detailDs.current?.set('materialLot', null);
        onScanMaterialLot({ target: { value: res.rows.materialLotCode } }, 'onCompleteWo')
        if (workOrderData.doneStepFlag === 'Y') {
          handleAddRecords({
            cardCode,
            messageType: 'SUCCESS',
            message: `物料批${res.rows.materialLotCode}完工${res.rows.qty}${res.rows.uomName}物料${res.rows.materialCode}`,
          });
        } else {
          handleAddRecords({
            cardCode,
            messageType: 'SUCCESS',
            message: `工序报工记录卡片，执行完工成功`,
          });
        }
        // setTimeout(() => {
        //   handleChangeWo({workOrderId: detailDs.current?.get('workOrderId')});
        // }, 1000);
        return true;
      }
      handleAddRecords({
        cardCode,
        messageType: 'FAIL',
        message: `工序报工记录卡片，执行完工失败`,
      });
      return false;
    }))
  }

  const onReturnWo: (value: string, hanlded?: string) => boolean = (value: string, hanlded?: string) => {
    if ((!value || !workOrderData.workOrderId) && workOrderData.doneStepFlag !== 'Y') {
      return false;
    }
    setNumValue(value);
    if (hanlded === 'hanlded') {
      return returnWo({
        params: {
          ...workOrderData,
          materialLotCode: processDs.current?.get('barCode') || materialLotData?.materialLotCode,
          workcellId: enterInfo?.workStationId,
          shiftCode: enterInfo?.shiftCode,
          shiftDate: enterInfo?.shiftDate,
          returnQty: value,
          returnLotId: processDs?.current?.get('materialLotId') || null,
        },
      }).then((res => {
        if (res && res.success) {
          const container = document.querySelector(`.${styles.doneStepFlagModals}`);
          if (container) {
            document.querySelector('.ProcessReportingRecordsForm')?.appendChild(container);
            setFlagModal('');
          }
          ONotification.success({});
          setWorkOrderData({
            ...workOrderData,
            lastCompletedQty: workOrderData.lastCompletedQty + (trendsNum ? Number(trendsNum) : 0),
          })
          setTrendsNum(`-${value}`)
          detailDs.current?.set('materialLot', null);
          onScanMaterialLot({ target: { value: res.rows.materialLotCode || '' } })
          if (workOrderData.doneStepFlag === 'Y') {
            handleAddRecords({
              cardCode,
              messageType: 'SUCCESS',
              message: `物料批${res.rows.materialLotCode}完工退回${res.rows.qty}${res.rows.uomName}物料${res.rows.materialCode}`,
            });
          } else {
            handleAddRecords({
              cardCode,
              messageType: 'SUCCESS',
              message: `工序报工记录卡片，执行完工退回成功`,
            });
          }
          // setTimeout(() => {
          //   handleChangeWo({workOrderId: detailDs.current?.get('workOrderId')});
          // }, 1000);
          return true;
        }
        handleAddRecords({
          cardCode,
          messageType: 'FAIL',
          message: `工序报工记录卡片，执行完工退回失败`,
        });
        return false;
      }))
    }
    setFlagModal(workOrderData.doneStepFlag);
    return true;
  }

  const handleFetchMaterialQty = () => {
    const value = processDs?.current?.get('requestTypeCode');
    fetchMaterialLotQty({
      params: {
        ...workOrderData,
        workcellId: enterInfo?.workStationId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        materialLotCode: value,
      },
    }).then((res => {
      if (res && res.success) {
        processDs?.current?.init('materialLotId', res.rows.materialLotId);
        processDs?.current?.init('maxQty', res.rows.primaryUomQty);
        processDs?.current?.init('barCode', res.rows.materialLotCode);
        if (numValue && Number(numValue) < Number(res.rows.primaryUomQty)) {
          processDs?.current?.init('qty', Number(numValue));
        } else {
          processDs?.current?.init('qty', res.rows.primaryUomQty);
        }
      }
    }))
  }

  const handleClose = () => {
    const container = document.querySelector(`.${styles.doneStepFlagModals}`);
    if (container) {
      document.querySelector('.ProcessReportingRecordsForm')?.appendChild(container);
      setFlagModal('');
    }
  }

  const handleConfirm = () => {
    if (flagModal === 'Y') {
      if (!processDs.current?.get('maxQty')) {
        return ONotification.error({ message: intl.get(`${modelPrompt}.please.enter.the.scanning.barcode`).d("请输入扫描条码") });
      }
      if (!processDs.current?.get('qty')) {
        return ONotification.error({ message:  intl.get(`${modelPrompt}.please.enter.the.quantity`).d("请输入数量") });
      }
      onReturnWo(processDs.current?.get('qty'), 'hanlded');
    } else {
      onReturnWo(numValue, 'hanlded');
    }
  }

  const renderOkObj = {
    color: 'rgba(100, 222, 163, 1)',
    background: 'rgba(100, 222, 163, 0.15)',
    padding: '4px 14px',
  }

  const renderNgObj = {
    color: 'rgba(242, 58, 80, 1)',
    background: 'rgba(242, 58, 80, 0.2)',
    padding: '4px 14px',
  }

  const renderPdObj = {
    color: 'rgba(255, 255, 255, 1)',
    background: 'rgba(255, 255, 255, 0.15)',
    padding: '4px 14px',
  }

  const column = [
    {
      name: 'identification',
    },
    {
      name: 'primaryUomQty',
    },
    {
      name: 'status',
    },
    {
      name: 'qualityStatusDesc',
      renderer: ({ record }) => {
        if (record.get('qualityStatusDesc') === '合格') {
          return <span style={renderOkObj}>{record.get('qualityStatusDesc')}</span>
        }
        if (record.get('qualityStatusDesc') === '不合格') {
          return <span style={renderNgObj}>{record.get('qualityStatusDesc')}</span>
        }
        if (record.get('qualityStatusDesc') === '待定') {
          return <span style={renderPdObj}>{record.get('qualityStatusDesc')}</span>
        }
        return record.get('qualityStatusDesc');
      },
    },
    {
      name: 'printTimes',
    },
    {
      name: 'productionDate',
      width: 170,
    },
  ]

  const handleCloseStep = () => {
    const container = document.querySelector(`.${styles.doneStepFlagModals}`);
    if (container) {
      document.querySelector('.ProcessReportingRecordsForm')?.appendChild(container);
      setStepModal(false);
    }
  }

  const handleOpenPrint = async () => {
    if (!workOrderData.workOrderId || printModalShow) {
      return;
    }
    setFetchPrintLoading(true);
    detailDs.current?.set('barCode', null);
    printDs.setQueryParameter('eoId', workOrderData?.eoId);
    await printDs.query().then(res => {
      if (res.success) {
        setPrintModalShow(true)
      }
    });
    setFetchPrintLoading(false);
  }

  const handleFilterPrint = () => {
    const materialLotCode = detailDs.current?.get('barCode');
    printDs.setQueryParameter('materialLotCode', materialLotCode);
    printDs.setQueryParameter('eoId', workOrderData?.eoId);
    printDs.query();
  }

  const printCallback = () => {
    handleFilterPrint();
  }

  useEffect(() => {
    if (printModalShow || flagModal || stepModal) {
      const container = document.querySelector(`.${styles.doneStepFlagModals}`);
      if (container) {
        document.getElementById('operationPlatform')?.appendChild(container);
      }
    }
  }, [printModalShow, flagModal, document.querySelector(`.${styles.doneStepFlagModals}`)]);

  const handleCloseStepModal = () => {
    const container = document.querySelector(`.${styles.doneStepFlagModals}`);
    if (container) {
      document.querySelector('.ProcessReportingRecordsForm')?.appendChild(container);
      setPrintModalShow(false)
    }
  }

  const numberKeyboardProps = {
    materialLotTop: detailDs.current?.get('materialLot'),
    checkedMaterialLot: checked,
    workOrderData,
  }

  return (
    <CardLayout.Layout spinning={fetchWoDetailLoading || scanMaterialLotLoading || completeWoLoading || returnWoLoading || fetchPrintLoading}>
      <CardLayout.Header
        className='ProcessReportingRecordsHead'
        title={intl.get(`${modelPrompt}.processReportingRecords.title`).d('工序报工记录') }
        help={props?.cardUsage?.remark}
        content={
          <>
            <TextField
              name="materialLot"
              placeholder={intl.get(`${modelPrompt}.please.scan.materialLot`).d("请扫描物料批") }
              dataSet={detailDs}
              disabled={workOrderData.doneStepFlag !== 'Y'}
              // @ts-ignore
              ref={materialLotInput}
              onEnterDown={onScanMaterialLot}
              prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />}
              style={{
                width: '256px',
                marginRight: '-18px',
              }}
            />
          </>
        }
      // addonAfter={
      //   <div id={styles.processReportingRecords}><CheckBox checked disabled={!(checked && workOrderData.doneStepFlag === 'Y')} /></div>
      // }
      />
      <CardLayout.Content className='ProcessReportingRecordsForm'>
        <div className={styles.detailContent}>
          {/* <CardCustomizeForm style={{ minWidth: '200px', flex: 1 }} renderProps={renderProps} responseData={workOrderData} /> */}
          <div>
            <div className={styles.detailContentTop}>
              <Form dataSet={detailDs} labelWidth={100} columns={cardMode === 'Tile' ? formColumns : 4} style={{ flex: 1 }}>
                {/* <Lov
                  name="workOrderLov"
                  placeholder='请选择WO'
                  dataSet={detailDs}
                  style={{ marginRight: '8px' }}
                  modalProps={{
                    contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
                    className: styles.workOrderModals,
                    okProps: {
                      style: {
                        background: '#1b7efc',
                      },
                    },
                    cancelProps: {
                      style: {
                        background: '#50819c',
                        color: 'white',
                      },
                    },
                  }}
                  onChange={handleChangeWo}
                  suffix={<img src={lovChoose} alt='' style={{height: '19px'}}/>}
                /> */}
                <TextField
                  name="workOrderNum"
                  onChange={(value) => { handleFetchLovData(value) }}
                  placeholder={intl.get(`${modelPrompt}.please.select.wo`).d("请选择WO") }
                  suffix={
                    <img
                      src={lovChoose}
                      alt=''
                      style={{ height: '19px' }}
                      onClick={() => { handleFetchLovData('click') }}
                    />
                  }
                />
                {/* {renderProps.map(item => (
                  item[0] !== 'workOrderNum' && item[1].labelVisible && <Output name={item[0]}/>
                ))} */}
                <Output name='qtyInfo' />
                <Output name='materialCode' />
                <Output name='materialName' />
                <Output
                  name='planStartTime'
                  renderer={({ value }) => {
                    return value ? moment(value).format('YYYY-MM-DD HH:mm') : null;
                  }}
                />
                <Output
                  name='planEndTime'
                  renderer={({ value }) => {
                    return value ? moment(value).format('YYYY-MM-DD HH:mm') : null;
                  }}
                />
                <Output name='customerName' />
                <Output name='remark' />
                <Output name='operationName' />
              </Form>
              <div className={styles.materialLotContent}>
                <div style={{ marginBottom: '8px', width: '256px' }}>
                  <Form dataSet={materialDs} labelWidth={50} columns={1} style={{ flex: 1, background: 'rgba(42, 99, 130, 1)' }}>
                    <Output
                      name="identification"
                      renderer={({ value, record }) => {
                        if (!value) {
                          return null;
                        }
                        if (record?.get('enableFlag') === 'Y') {
                          return (
                            <span style={{ display: 'flex', alignItems: 'center' }}>
                              <span style={{ marginRight: '4px' }}>{value}</span>
                              <img src={enableY} alt='' />
                            </span>
                          );
                        }
                        return (
                          <span style={{ display: 'flex', alignItems: 'center' }}>
                            <span style={{ marginRight: '4px' }}>{value}</span>
                            <img src={enableN} alt='' />
                          </span>
                        );
                      }}
                    />
                    <Output
                      name="qtyInfo"
                      renderer={({ value, record }) => {
                        if (record?.get('qualityStatus')) {
                          if (record?.get('qualityStatus') === 'OK') {
                            return (
                              <span style={{ display: 'flex', alignItems: 'center' }}>
                                {value}
                                <img src={qualityOk} alt='' style={{ marginLeft: '4px' }} />
                              </span>
                            );
                          }
                          if (record?.get('qualityStatus') === 'NG') {
                            return (
                              <span style={{ display: 'flex', alignItems: 'center' }}>
                                {value}
                                <img src={qualityNg} alt='' style={{ marginLeft: '4px' }} />
                              </span>
                            );
                          }
                          return (
                            <span style={{ display: 'flex', alignItems: 'center' }}>
                              {value}
                              <img src={qualityPend} alt='' style={{ marginLeft: '4px' }} />
                            </span>
                          );
                        }
                        return null;
                      }}
                    />
                    <Output name="locatorCode" />
                  </Form>
                </div>
                <NumberKeyboard keyboardWidth={256} onOk={onCompleteWo} onReturn={onReturnWo} numberKeyboardProps={numberKeyboardProps} />
              </div>
            </div>
          </div>
          <div className={`${styles.cardFooter} ProcessReportingRecordsForm`}>
            <div className={styles.completeText}>
              <div className={styles.completeTextName}>{intl.get(`${modelPrompt}.current.work.order.for.this.shift`).d("本班次当前工单") }</div>
              <span className={styles.completeTextName}>{intl.get(`${modelPrompt}.the.number.of.operations.in.this.process`).d("本工序作业数量") }:</span>
              <Tooltip title={workOrderData?.lastCompletedQty} theme="light">
                <span style={{ fontWeight: 800 }}>{workOrderData?.lastCompletedQty || 0}</span>
                <span>{trendsNum}</span>
              </Tooltip>
            </div>
            <div className={styles.printButton}>
              <div id={styles.processReportingRecords}>
                <CheckBox checked={autoFlag} disabled={workOrderData.doneStepFlag !== 'Y'} onChange={() => { setAutoFlag(!autoFlag) }}>
                  {intl.get(`${modelPrompt}.auto.print`).d("自动打印") }
                </CheckBox>
                <TemplatePrintButton
                  name={intl.get(`${modelPrompt}.print`).d("打印")}
                  ref={printRef}
                  style={{ display: 'none' }}
                  disabled={!samePrintState}
                  printButtonCode='HME.REPORT_LOT_PRINT'
                  printParams={{ materialLotIds: materialLotData.materialLotId }}
                  printCallback={printCallback}
                />
              </div>
              <img src={codePrint} alt='' />
              <div onClick={handleOpenPrint}>{intl.get(`${modelPrompt}.history.barcode.print`).d("历史条码打印")}</div>
            </div>
          </div>
        </div>
        {
          flagModal === 'Y' && (
            <div className={`${styles.doneStepFlagModals}`}>
              <div className={styles.modalTitle}>
                <div>
                  <img src={leftTopLog} alt='' />
                  <span>{intl.get(`${modelPrompt}.scan.return.barcode`).d("扫描退回条码")}</span>
                </div>
              </div>
              <div className={styles.modalContent}>
                <Form dataSet={processDs}>
                  <TextField name="requestTypeCode" prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />} onChange={handleFetchMaterialQty} placeholder={intl.get(`${modelPrompt}.please.enter.the.scanning.barcode`).d("请输入扫描条码")} />
                  <Output name="maxQty" />
                  <NumberField name="qty" placeholder={intl.get(`${modelPrompt}.please.enter.the.quantity`).d("请输入数量")} />
                </Form>
              </div>
              <div className={styles.modalFooter}>
                <Button onClick={handleClose}>{intl.get(`${modelPrompt}.close`).d("关闭")}</Button>
                <Button color={ButtonColor.primary} onClick={handleConfirm}>{intl.get(`${modelPrompt}.confirm`).d("确认")}</Button>
              </div>
            </div>
          )
        }
        {
          flagModal === 'N' && (
            <div className={`${styles.doneStepFlagModals}`}>
              <div className={styles.modalContent}>
                <div>{intl.get(`${modelPrompt}.is.sure.execute.work.report.return`).d("是否确认执行报工退回？")}</div>
              </div>
              <div className={styles.modalFooter}>
                <Button onClick={handleClose}>{intl.get(`${modelPrompt}.close`).d("关闭")}</Button>
                <Button color={ButtonColor.primary} onClick={handleConfirm}>{intl.get(`${modelPrompt}.confirm`).d("确认")}</Button>
              </div>
            </div>
          )
        }
        {
          !!printModalShow && (
            <div className={`${styles.doneStepFlagModals}`} style={{ width: '90%' }}>
              <div className={styles.modalTitle}>
                <div>
                  <img src={leftTopLog} alt='' />
                  <span>{intl.get(`${modelPrompt}.barcode.print`).d("条码打印")}</span>
                  <TextField
                    className={styles.barCodeText}
                    placeholder={intl.get(`${modelPrompt}.please.enter.barcode`).d("请输入条码")}
                    name="barCode"
                    dataSet={detailDs}
                    // @ts-ignore
                    ref={materialLotInput}
                    onEnterDown={handleFilterPrint}
                    prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />}
                  />
                </div>
                <div className={styles.modalRightFooter} id={styles.modalRightFooter}>
                  <TemplatePrintButton
                    name={intl.get(`${modelPrompt}.patchwork`).d("补打")}
                    disabled={!sameAgainState}
                    printButtonCode='HME.REPORT_LOT_PRINT'
                    printParams={{ materialLotIds: printDs.selected.map(item => item.get('materialLotId')).join(',') }}
                    printCallback={printCallback}
                  />
                  <TemplatePrintButton
                    name={intl.get(`${modelPrompt}.print`).d("打印")}
                    disabled={!samePrintState}
                    printButtonCode='HME.REPORT_LOT_PRINT'
                    printParams={{ materialLotIds: printDs.selected.map(item => item.get('materialLotId')).join(',') }}
                    printCallback={printCallback}
                  />
                  <div onClick={() => { handleCloseStepModal() }}>
                    <img src={close} alt='' />
                  </div>
                </div>
              </div>
              <div className={styles.modalContent}>
                <Table
                  dataSet={printDs}
                  columns={column}
                  customizedCode='ProcessReportingRecords'
                  showSelectionTips
                  style={{
                    maxHeight: 400,
                  }}
                  onRow={({ record }) => {
                    if (record && record.data && record.get('printStatus') === '已打印') {
                      return {
                        className: styles.printLightRow,
                      };
                    }
                    if (record && record.data && record.get('printStatus') === '未打印') {
                      return {
                        className: styles.againLightRow,
                      };
                    }
                    return {};
                  }}
                />
              </div>
            </div>
          )
        }
        {
          stepModal && (
            <div className={`${styles.doneStepFlagModals}`}>
              <div className={styles.modalTitle}>
                <div>
                  <img src={leftTopLog} alt='' />
                  <span>{intl.get(`${modelPrompt}.select.current.step.processed`).d("选择当前需要加工的步骤")}</span>
                </div>
              </div>
              <div className={styles.modalContent}>
                {
                  stepInfoData.map(item => (
                    <Radio dataSet={stepDs} name="routerStep" value={item?.routerStep}>{item?.showInfo}</Radio>
                  ))
                }
              </div>
              <div className={styles.modalFooter}>
                <Button onClick={handleCloseStep}>{intl.get(`${modelPrompt}.close`).d("关闭")}</Button>
                <Button color={ButtonColor.primary} onClick={() => { handleChangeWo({ workOrderId: detailDs.current?.get('workOrderId') }) }}>{intl.get(`${modelPrompt}.confirm`).d("确认")}</Button>
              </div>
            </div>
          )
        }
      </CardLayout.Content>
    </CardLayout.Layout>
  )
}

export default WorkOrderReporting;
