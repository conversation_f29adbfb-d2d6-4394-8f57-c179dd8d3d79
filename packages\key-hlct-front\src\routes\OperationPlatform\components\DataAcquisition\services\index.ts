/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-17 13:43:36
 * @LastEditTime: 2023-07-17 14:11:07
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 获取数据采集列表
 * @function FetchDataCollection
 * @returns {object} fetch Promise
 */
export function FetchDataCollectionList(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-data-collection/data/record/new/ui`,
    method: 'GET',
  };
}

/**
 * 获取数据采项数据
 * @function FetchDataCollection
 * @returns {object} fetch Promise
 */
export function SaveDataCollectionList(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-data-collection/save/ui`,
    method: 'POST',
  };
}
