/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-17 10:55:14
 * @LastEditTime: 2023-07-20 11:01:31
 * @LastEditors: <<EMAIL>>
 */
import React, { useMemo, useEffect, useState } from 'react';
import { observer } from 'mobx-react';
import { Table, DataSet, TextField, NumberField, Select, Attachment } from 'choerodon-ui/pro';
import { openTab } from 'utils/menuTab';
import intl from 'utils/intl';
import { LabelLayout } from 'choerodon-ui/pro/lib/form/enum';
import { isNil } from 'lodash';
import { Record } from 'choerodon-ui/dataset';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import star from '@/assets/operationPlatformCard/star.svg';
import { CardLayout, useRequest } from '../commonComponents';
import { useOperationPlatform } from '../../contextsStore';
import { tableDS } from './stores';
import { FetchDataCollectionList, SaveDataCollectionList, SaveDataCollectionAll } from './services';
import styles from './index.module.less';
import C7nModal from '../../C7nModal';
// import CalculatorKeyboard from '../commonComponents/CalculatorKeyboard';

interface TagValueInputProps {
  record: Record;
  name: string;
  onEnterDown: (record: Record) => void;
}

const modelPrompt = 'tarzan.operationPlatform.selfMutualInspection';
const Modal = C7nModal;

const TagValueInput = observer((props: TagValueInputProps) => {
  const { record, name, onEnterDown } = props;

  // const handleNumPopup = (e: any) => {
  //   console.log('e', e);
  //   // window.CalculatorKeyboard = CalculatorKeyboard;
  //   CalculatorKeyboard.open({parentId: 'operationPlatform'});
  // };

  const renderDiv = useMemo(() => {
    switch (record.get('valueType')) {
      case 'VALUE':
        return <NumberField record={record} name={name} onEnterDown={() => { onEnterDown(record) }} style={{ width: '100%' }} />;
      // return <NumberField record={record} name={name} readOnly onEnterDown={() => { onEnterDown(record) }} style={{ width: '100%' }} onClick={handleNumPopup}/>;
      case 'DECISION_VALUE':
        return (
          // <Select
          //   record={record}
          //   name={name}
          //   style={{ width: '100%' }}
          //   onChange={() => { onEnterDown(record) }}
          //   getPopupContainer={() => document.getElementById('operationPlatform') || document.body}
          // >
          //   <Select.Option value={record.get('trueValue')}>{record.get('trueValue')}</Select.Option>
          //   <Select.Option value={record.get('falseValue')}>{record.get('falseValue')}</Select.Option>
          // </Select>
          // <form>
          //   <Radio record={record} name={name} value={record.get('trueValue')} >
          //     {record.get('trueValue')}
          //   </Radio>
          //   <Radio record={record} name={name} value={record.get('falseValue')} onChange={() => {onEnterDown(record)}}>
          //     {record.get('falseValue')}
          //   </Radio>
          // </form>
          <div className={`${styles.decisionValue} ${record?.get('valueAllowMissing') !== 'Y' ? styles.requiredValue : ''}`}>
            <div className={record.get(name) === record.get('trueValue') ? styles.trueTag : ''} onClick={() => { handleChangeRadio(record, name, 'trueValue', onEnterDown) }}>{record.get('trueValue')}</div>
            <div className={record.get(name) === record.get('falseValue') ? styles.falseTag : ''} onClick={() => { handleChangeRadio(record, name, 'falseValue', onEnterDown) }}>{record.get('falseValue')}</div>
          </div>
        );
      case 'ENCLOSURE':
        return <Attachment
          record={record}
          name={name}
          labelLayout={LabelLayout.none}
          viewMode='popup'
          sortable={false}
        />;
      case 'TEXT':
        return <TextField record={record} name={name} onChange={() => { onEnterDown(record) }} style={{ width: '100%' }} />;
      case 'VALUE_LIST':
        return (
          <Select
            record={record}
            combo
            name={name}
            style={{ width: '100%' }}
            onChange={() => { onEnterDown(record) }}
            getPopupContainer={() => document.getElementById('operationPlatform') || document.body}
          >
            {(record.get('valueList') || '').split(',').map(item => <Select.Option value={item}>{item}</Select.Option>)}
          </Select>
        );
      default:
        return <></>;
    }
  }, [])

  return renderDiv;
})

const handleChangeRadio = (record: Record, name: string, value: string, onEnterDown) => {
  record.set(name, record.get(value));
  onEnterDown(record)
};
const DataAcquisition = observer((props) => {
  // const [ everyOK, setEveryOK] = useState(false);
  // const [ someNG, setSomeNG] = useState(false);
  const [selfInspectionFlag, setSelfInspectionFlag] = useState('');
  const { enterInfo, workOrderData } = useOperationPlatform();
  const { run: fetchDataCollectionList, loading: queryLoading } = useRequest(FetchDataCollectionList(), {
    manual: true,
    needPromise: true,
  });
  const { run: saveDataCollectionList, loading: saveLoading } = useRequest(SaveDataCollectionList(), {
    manual: true,
    needPromise: true,
  });
  const { run: saveDataCollectionAll, loading: saveAllLoading } = useRequest(SaveDataCollectionAll(), {
    manual: true,
    needPromise: true,
  });

  const tableDs = useMemo(() => new DataSet(tableDS()), []);

  useEffect(() => {
    if (!workOrderData?.routerStepId || !workOrderData?.eoId) {
      tableDs.loadData([]);
      return;
    }
    handleRefresh()
  }, [workOrderData]);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (tableDs) {
      const handlerQuery = flag
        ? tableDs.addEventListener
        : tableDs.removeEventListener;
      // 查询条件更新时操作
      handlerQuery.call(tableDs, 'load', handleQDataUpdate);
      handlerQuery.call(tableDs, 'update', handleQDataUpdate);
    }
  };

  const handleQDataUpdate = () => {
    const dataList = tableDs.data.filter(item => item.get('valueAllowMissing') !== 'Y');
    const everyHave = dataList.every(item => item.get('tagCalculateResult'));
    const everyY = dataList.filter(item => item.get('outboundFlag') === 'Y');
    let _everyOK = false;
    let _someNG = false;
    if (everyHave) {
      _everyOK = everyY.every(item => item.get('tagCalculateResult') === 'OK');
      _someNG = everyY.some(item => item.get('tagCalculateResult') === 'NG');
    }
    // console.log('44444', everyY, everyHave, _everyOK, _someNG);
    // setEveryOK(_everyOK);
    // setSomeNG(_someNG);
    if (_everyOK) {
      return 'OK'
    }
    if (_someNG) {
      return 'NG'
    }
    return '';
  }

  const validateValueCompliant = (record: Record) => {
    const {
      valueType,
      // valueAllowMissing,
      minimumValue,
      maximalValue,
      valueList,
      trueValue,
      // falseValue,
      tagValue,
    } = record.toData();
    if (['ENCLOSURE'].includes(valueType)) {
      // 附件的采集项，不做合规性校验，需要用户手动修改
      return;
    }
    if (isNil(tagValue)) {
      // 当清空录入值时，清空合规列
      record.set('tagCalculateResult', null);
      return;
    }
    let _tagCalculateResult: "OK" | "NG" | '' = 'OK';
    switch (valueType) {
      case 'VALUE':
        // 数值类型的判断
        if (!minimumValue && !maximalValue && tagValue) {
          _tagCalculateResult = 'OK';
        }
        if (minimumValue >= 0 && Number(tagValue) < Number(minimumValue)) {
          _tagCalculateResult = 'NG';
        }
        if (maximalValue >= 0 && Number(tagValue) > Number(maximalValue)) {
          _tagCalculateResult = 'NG';
        }
        break;
      case 'DECISION_VALUE':
        if (tagValue !== trueValue) {
          _tagCalculateResult = 'NG';
        }
        break;
      case 'VALUE_LIST':
        if (!(valueList || '').split(',').includes(tagValue)) {
          _tagCalculateResult = 'NG';
        }
        break;
      case 'TEXT':
        if (tagValue) {
          _tagCalculateResult = 'OK';
        }
        break;
      default:
        break;
    }
    record.set('tagCalculateResult', _tagCalculateResult);
  }


  const handleSave = async (record?: Record, typeResult?: string) => {
    if (!record?.get('tagValue') && record?.get('valueAllowMissing') !== 'Y') {
      record?.set('tagCalculateResult', null);
      setSelfInspectionFlag('');
    }
    if (record) {
      validateValueCompliant(record);
      const flag = await record.validate(true, true);
      if (!flag) {
        return;
      }
    }
    const result = handleQDataUpdate();
    saveDataCollectionList({
      params: {
        tagDataList: record ? [record.toData()] : tableDs.data.map(item => item.toData()),
        eoId: workOrderData?.eoId,
        workcellId: enterInfo?.workStationId,
        operationId: enterInfo?.selectOperation?.operationId,
        routerStepId: workOrderData?.routerStepId,
        selfInspectionFlag: typeResult || result,
      },
    }).then(res => {
      if (!res || res.message) {
        return;
      }
      record?.init('dataRecordId', res);
      record?.init('tagValue', record.get('tagValue'));
      record?.init('tagCalculateResult', record.get('tagCalculateResult'));
      tableDs.next();
      handleRefresh();
    })
  }

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'lineNumber',
        width: 70,
        align: ColumnAlign.right,
        renderer: ({ dataSet, record }) => {
          return (dataSet?.indexOf(record!) || 0) + 1;
        },
      },
      {
        name: 'tagColumn',
        title: <span style={{ marginLeft: '16px' }}>{intl.get(`${modelPrompt}.dataRecord`).d('数据项')}</span>,
        renderer: ({ record }) => {
          if (record?.get('outboundFlag') === 'Y') {
            return (
              <div style={{ verticalAlign: 'middle' }}>
                <img src={star} alt="" style={{ marginRight: '4px' }} />
                <span>{record?.get('tagDescription') || record?.get('tagCode')}</span>
              </div>
            )
          }
          return (
            <div>
              <span style={{ marginLeft: '16px' }}>{record?.get('tagDescription') || record?.get('tagCode')}</span>
            </div>
          )
        },
      },
      // {
      //   name: 'valueTypeDescription',
      //   width: 100,
      // },
      // {
      //   name: 'minimumValue',
      //   width: 80,
      // },
      // {
      //   name: 'maximalValue',
      //   width: 80,
      // },
      {
        name: 'valueList',
        width: 120,
        renderer: ({ record }) => {
          switch (record?.get('valueType')) {
            case 'VALUE':
              if (!record?.get('minimumValue') && !record?.get('maximalValue')) {
                return '';
              }
              return `${record?.get('minimumValue') >= 0 ? record?.get('minimumValue') : ''}~${record?.get('maximalValue') >= 0 ? record?.get('maximalValue') : ''}`;
            case 'DECISION_VALUE':
              if (!record?.get('trueValue') && !record?.get('falseValue')) {
                return '';
              }
              return `${record?.get('trueValue') || ''}/${record?.get('falseValue') || ''}`;
            case 'VALUE_LIST':
              return (record?.get('valueList') || '');
            default:
              return <></>;
          }
        },
      },
      {
        name: 'tagValue',
        align: ColumnAlign.center,
        width: 100,
        renderer: ({ record }) => {
          return <TagValueInput record={record!} onEnterDown={handleSave} name='tagValue' />;
        },
      },
      {
        name: 'tagCalculateResult',
        align: ColumnAlign.center,
        width: 80,
        renderer: ({ record }) => {
          // return <Select
          //   record={record!}
          //   name="tagCalculateResult"
          //   onChange={() => { handleSave(record!) }}
          //   getPopupContainer={() => document.getElementById('operationPlatform') || document.body}
          // />;
          if (record?.get('tagCalculateResult') === 'OK') {
            return <div className={styles.colButtonOk}>OK</div>
          }
          if (record?.get('tagCalculateResult') === 'NG') {
            return <div className={styles.colButtonNg}>NG</div>
          }
          return null;
        },
      },
    ];
  }, [tableDs, workOrderData, enterInfo]);

  const handleRefresh = () => {
    if (!workOrderData?.routerStepId || !workOrderData?.eoId) {
      return;
    }
    fetchDataCollectionList({
      params: {
        eoId: workOrderData?.eoId,
        operationId: enterInfo?.selectOperation?.operationId,
        routerStepId: workOrderData?.routerStepId,
        workcellId: enterInfo?.workStationId,
      },
    }).then(res => {
      if (!res || res.message) {
        tableDs.loadData([]);
        return;
      }
      tableDs.loadData(res.rows)
      const currentValue = res.rows.find(item => item.valueAllowMissing !== 'Y').selfInspectionFlag;
      setSelfInspectionFlag(currentValue);
      tableDs.current = undefined;
    })
  }

  const handleSaveAll = (typeResult: string) => {
    return saveDataCollectionAll({
      params: {
        eoId: workOrderData?.eoId,
        // workcellId: enterInfo?.workStationId,
        // operationId: enterInfo?.selectOperation?.operationId,
        routerStepId: workOrderData?.routerStepId,
        // typeResult,
      },
    }).then(res => {
      if (!res || res.message) {
        return false;
      }
      handleSave(undefined, typeResult);
      return true;
    })
  }

  const handleConfirmSure = (typeResult: string) => {
    if (!workOrderData?.routerStepId || !workOrderData?.eoId) {
      return;
    }
    // console.log('typeResult', typeResult, tableDs.data);
    const otherType = typeResult === 'NG' ? 'OK' : 'NG';
    const someOther = tableDs.data.some(item => item.get('tagCalculateResult') === otherType);
    if (someOther) {
      Modal.open({
        title: intl.get(`tarzan.common.title.tips`).d('提示'),
        children: intl.get(`${modelPrompt}.message.judgeResult`, { otherType, typeResult }).d(`本次自互检验项存在${otherType}，是否判定自互检结果为${typeResult}？`),
        contentStyle: {
          background: '#38708F',
        },
        className: styles.operationPlatformEnterModal,
        onOk: () => {
          handleSaveAll(typeResult);
        },
      })
    } else {
      handleSaveAll(typeResult);
    }
  };

  const handleCreate = () => {
    if (!workOrderData?.routerStepId || !workOrderData?.eoId) {
      return;
    }
    openTab({
      title: intl.get(`${modelPrompt}.ngReportPlatform`).d('不良记录平台'),
      key: `/hmes/bad-record/platform/detail/create`,
      path: `/hmes/bad-record/platform/detail/create`,
      closable: true,
      state: {
        toDefault: 'Y',
        siteId: enterInfo?.siteId, // 站点
        siteName: enterInfo?.siteName,
        workcellId: enterInfo?.workStationId, // 工作单元
        workcellName: enterInfo?.workStationName,
        eoId: workOrderData?.eoId, // 执行作业编码
        eoNum: workOrderData?.eoNum,
        routerId: workOrderData?.routerId, // 工艺路线
        routerName: workOrderData?.routerName,
        routerStepId: workOrderData?.routerStepId, // 步骤
        routerStepDesc: workOrderData?.routerStepName,
        processesFlag: 'Y',// 是否从工序跳转不良记录
      },
    });
  }

  return (
    <CardLayout.Layout
      spinning={queryLoading || saveLoading || saveAllLoading}
      className={styles.dataQcquisition}
    >
      <CardLayout.Header
        className='SelfMutualInspectionHead'
        title={intl.get(`${modelPrompt}.title`).d('自互检')}
        help={props?.cardUsage?.remark}
        addonAfter={
          <div className={styles.topRight}>
            {/* <Form dataSet={formDs} style={{width: '300px'}}>
              <Select
                name="tagValueResult"
              // onChange={() => { handleSave(record!) }}
              // getPopupContainer={() => document.getElementById('operationPlatform') || document.body}
              />
            </Form> */}
            <span className={styles.buttonText}>{intl.get(`${modelPrompt}.selfMutualInspectionResult`).d('自互检结果')}:</span>
            <div className={selfInspectionFlag === 'OK' ? styles.buttonOk : styles.buttonDefault} onClick={() => { handleConfirmSure('OK') }}>OK</div>
            <div className={selfInspectionFlag === 'NG' ? styles.buttonNg : styles.buttonDefault} onClick={() => { handleConfirmSure('NG') }}>NG</div>
            <div className={styles.buttonRed} onClick={handleCreate}>
              {intl.get(`${modelPrompt}.ngCreate`).d('不良创建')}
            </div>
          </div>
        }
      />
      <CardLayout.Content className='SelfMutualInspectionForm'>
        <Table id={styles.tableInnerField} dataSet={tableDs} columns={columns} customizedCode='SelfMutualInspection' style={{ height: 'calc(100% - 16px)' }} renderEmpty={() => <></>} rowHeight={30} />
      </CardLayout.Content>
    </CardLayout.Layout>
  )
})

export default DataAcquisition;
