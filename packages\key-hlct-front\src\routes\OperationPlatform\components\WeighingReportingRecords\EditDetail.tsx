import React, { useEffect, useRef, useState } from "react";
import { Form, NumberField, Output, SelectBox } from "choerodon-ui/pro";
import { ViewMode } from 'choerodon-ui/pro/lib/radio/enum';
import { ContainerUpdate } from './services';
import { ONotification, useRequest } from '../commonComponents';
import NumberKeyboard from './components/NumberKeyboard';
import styles from './index.module.less';

const { Option } = SelectBox;

const EditDetail = props => {
  const { type, formDs, detailDs, tableDs, closeModal } = props

  const bagWeightField = useRef(null);
  const palletWeightField = useRef(null);
  const transformNumberField1 = useRef(null);
  const transformNumberField2 = useRef(null);
  const [weighingWeighVal, setWeighingWeighVal] = useState(0);
  const [inputName, setInputName] = useState("");

  const { run: containerUpdate } = useRequest(ContainerUpdate(), { manual: true, needPromise: true });

  useEffect(() => {
    const { bagWeight, palletWeight, transformNumber, netWeight } = detailDs.current?.toData();
    if (type === 'bagWeight') {
      // @ts-ignore
      bagWeightField.current?.focus();
      formDs.current.set('atPresentBagWeight', bagWeight)
    }
    if (type === 'palletWeight') {
      // @ts-ignore
      palletWeightField.current?.focus();
      formDs.current.set('atPresentPalletWeight', palletWeight)
    }
    if (type === 'transformNumber') {
      // @ts-ignore
      transformNumberField1.current?.focus();
      setInputName("transformNumberField1")
      formDs.current.set('transformNumber', transformNumber)
      formDs.current.set('netWeight', netWeight)
    }
    if (type === 'doBack') {
      const { materialLotCode, primaryUomQty } = tableDs.selected.map(record => record.toData())[0]
      formDs.current.set('materialLotCode', materialLotCode)
      formDs.current.set('qty', primaryUomQty)
    }
  }, [type])

  const numberKeyboardProps = {
  }

  // @ts-ignore
  const onCompleteWo: (value: string) => boolean = async (value: string) => {
    if (!value || Number(value) <= 0) {
      return false;
    }
    const validate = await formDs.validate();
    if (!validate) {
      return false;
    }
    if (type === 'bagWeight') {
      const _bagWeight = formDs.current?.get('editBagWeight');
      detailDs.current?.set('bagWeight', _bagWeight);
    }
    if (type === 'palletWeight') {
      const _palletWeight = formDs.current?.get('editPalletWeight');
      const res = await containerUpdate({
        params: {
          containerId: detailDs.current?.get('containerId'),
          containerCode: detailDs.current?.get('palletBarCode'),
          containerWeight: formDs.current?.get('editPalletWeight'),
        },
      })
      if (res?.success) {
        ONotification.success({})
        detailDs.current?.set('palletWeight', _palletWeight);
        closeModal()
        formDs.reset();
        return true;
      }
      return false;
    }
    if (type === 'transformNumber') {
      const _transformNumber = formDs.current?.get('transformNumber');
      const _weighingWeight = formDs.current?.get('weighingWeight')
      const _weighingNum = formDs.current?.get('weighingNum')
      const _unitWeight = formDs.current?.get('unitWeight')
      detailDs.current?.set('transformNumber', _transformNumber);
      detailDs.current?.set('weighingWeight2', _weighingWeight);
      detailDs.current?.set('weighingNum', _weighingNum);
      detailDs.current?.set('unitWeight', _unitWeight);
      if (formDs.current?.get('weighingWeight') && !formDs.current?.get('weighingNum')) {
        // @ts-ignore
        transformNumberField1.current?.focus();
        return true;
      }
      if (formDs.current?.get('weighingNum') && !formDs.current?.get('weighingWeight')) {
        // @ts-ignore
        transformNumberField2.current?.focus();
        return true;
      }
    }
    closeModal()
    formDs.reset();
    return true;
  }

  const handleChangeKeyNumber = (value) => {
    if (type === 'bagWeight') {
      formDs.current?.set('editBagWeight', value ? Number(value) : null);
    }
    if (type === 'palletWeight') {
      formDs.current?.set('editPalletWeight', value ? Number(value) : null)
    }
    if (type === 'transformNumber') {
      if (inputName === 'transformNumberField1') {
        formDs.current?.set('weighingNum', value ? Number(value) : null)
      } else {
        formDs.current?.set('weighingWeight', value ? Number(value) : null)
      }
    }
  };

  const handleChangeVal = (value) => {
    setWeighingWeighVal(value);
  }

  const onReturnWo = () => true;

  const handleChange = (value) => {
    if (value) {
      formDs.current?.set('weighingNum', value);
    }
  }

  return (
    <div className={styles.editDetailContent}>
      <Form dataSet={formDs} columns={1} labelWidth={130}>
        {type === 'bagWeight' && (
          <>
            <Output name="atPresentBagWeight" renderer={({ value }) => {
              return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                <div>{value || ''}</div>
                <div>KG</div>
              </div>;
            }} />
            <NumberField name="editBagWeight" ref={bagWeightField} renderer={({ value }) => {
              return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                <div>{value || ''}</div>
                <div>KG</div>
              </div>;
            }} onChange={handleChangeVal}/>
          </>
        )}
        {type === 'palletWeight' && (
          <>
            <Output name="atPresentPalletWeight" renderer={({ value }) => {
              return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                <div>{value || ''}</div>
                <div>KG</div>
              </div>;
            }} />
            <NumberField name="editPalletWeight" ref={palletWeightField} renderer={({ value }) => {
              return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                <div>{value || ''}</div>
                <div>KG</div>
              </div>;
            }} onChange={handleChangeVal}/>
          </>
        )}
        {type === 'transformNumber' && (
          <>
            <NumberField name="weighingNum" ref={transformNumberField1} renderer={({ value }) => {
              return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                <div>{value || ''}</div>
                <div>PCS</div>
              </div>;
            }} onChange={handleChangeVal} onFocus={() => {
              setInputName("transformNumberField1");
              setWeighingWeighVal(formDs.current?.get('weighingNum') || 0)
            }} />
            <SelectBox mode={ViewMode.button} onChange={handleChange}>
              <Option value="50">50</Option>
              <Option value="100">100</Option>
              <Option value="200">200</Option>
              <Option value="300">300</Option>
              <Option value="500">500</Option>
            </SelectBox>
            <NumberField name="weighingWeight" ref={transformNumberField2} renderer={({ value }) => {
              return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                <div>{value || ''}</div>
                <div>KG</div>
              </div>;
            }} onChange={handleChangeVal} onFocus={() => {
              setInputName("transformNumberField2");
              setWeighingWeighVal(formDs.current?.get('weighingWeight') || 0)
            }} />
            <Output name="unitWeight" renderer={({ value }) => {
              return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                <div>{value || ''}</div>
                <div>KG/PCS</div>
              </div>;
            }} />
            <Output name="transformNumber" renderer={({ value }) => {
              return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                <div>{value || ''}</div>
                <div>PCS</div>
              </div>;
            }} />
          </>
        )}
        {type === 'doBack' && (
          <>
            <Output name="materialLotCode" />
            <Output name="qty" />
            <NumberField name="returnNum" />
          </>
        )}
      </Form>
      {type !== 'doBack' && <NumberKeyboard keyboardWidth={256} onOk={onCompleteWo} onReturn={onReturnWo} numberKeyboardProps={numberKeyboardProps} number={weighingWeighVal} handleChange={handleChangeKeyNumber} ds={detailDs} />}
    </div>
  )
}

export default EditDetail;
