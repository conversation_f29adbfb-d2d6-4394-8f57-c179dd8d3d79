
// 追溯确认DS
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.hmes.ProcessComponents';
const treeDS = () => ({
  autoQuery: false,
  autoCreate: false,
  // primaryKey: 'bomComponentId',
  transport: {
    read: ({ data }) => {
      return {
        data,
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-material-traces/left-material-list/ui`,
        method: 'GET',
      };
    },
  },
});

const tableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  primaryKey: 'id',
  selection: false,
  parentField: 'parentId',
  idField: 'id',
  paging: false,
  expandField: 'expand',
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('需求物料编码/版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('需求物料描述'),
    },
    {
      name: 'ecnFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ecnFlag`).d('ECN标识'),
    },
    {
      name: 'ecnRemark',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.ecnRemark`).d('ECN备注'),
    },
    {
      name: 'qtyProcess',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qtyProcess`).d('装配/需求'),
    },
    {
      name: 'assembleMaterialRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMaterialCode`).d('装配物料编码/版本'),
    },
    {
      name: 'assembleMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('装配物料描述'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
    },
  ],
  transport: {
    read: ({ data }) => {
      return {
        data,
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-material-traces/eo-wip-comp/list/ui`,
        method: 'GET',
      };
    },
  },
});

export { treeDS, tableDS };
