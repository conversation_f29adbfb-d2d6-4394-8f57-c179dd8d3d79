/**
 * @Description: 工序报工记录卡片-称重报工
 * @Author: <<EMAIL>>
 * @Date: 2023-07-26 14:40:22
 * @LastEditTime: 2023-08-04 16:23:26
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useMemo, useEffect, useCallback, useRef } from 'react';
import {
  TextField,
  DataSet,
  Button,
  Form,
  Output,
  NumberField,
  Table,
  Radio,
  Icon,
  Switch,
  Tooltip,
} from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { observer } from 'mobx-react';
import moment from 'moment';
import intl from 'utils/intl';
import { CardLayout, ONotification, useRequest } from '../commonComponents';
import NumberKeyboard from './components/NumberKeyboard';
import { detailDS, tableDS, processDS, printDS, stepDS, materialDS, workOrderLovDS, editModalDetailDS } from './stores';
import leftTopLog from '@/assets/operationPlatformCard/leftTopLog.svg';
import close from '@/assets/operationPlatformCard/close.png';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import lovChoose from '@/assets/operationPlatformCard/lovChoose.svg';
import enableY from '@/assets/operationPlatformCard/enableY.svg';
import enableN from '@/assets/operationPlatformCard/enableN.svg';
import cardSvg from '@/assets/icons/operation.svg';
import qualityNg from '@/assets/operationPlatformCard/qualityNg.svg';
import qualityOk from '@/assets/operationPlatformCard/qualityOk.svg';
import qualityPend from '@/assets/operationPlatformCard/qualityPend.svg';
import { useOperationPlatform, DispatchType } from '../../contextsStore';
import TemplatePrintButton from '../../TemplatePrintButton';
import { FetchWoDetail, ScanMaterialLot, CompleteWo, ReturnWo, FetchMaterialLotQty, ContainerUpdate, ProgressStart, ContainerScan, ProgressEnd, ExectueBack } from './services';
import C7nModal from '../../C7nModal';
import EditDetail from './EditDetail';
import styles from './index.module.less';

const modelPrompt = 'tarzan.operationPlatform.weighingReport';

let woModal;
let timeOut;
let editingModal;
const WorkOrderReporting = observer((props: any) => {
  const Modal = C7nModal;
  const { cardCode, handleAddRecords } = props;
  const { enterInfo, dispatch } = useOperationPlatform();

  const materialLotInput = useRef<HTMLInputElement>(null);

  const printRef = useRef<any>(null);
  const [checked, setChecked] = useState<boolean>(false)
  const [workOrderData, setWorkOrderData] = useState<{ [kay: string]: any }>({}); // 工单数据
  const [buttonAlter, setButtonAlter] = useState(''); // 工单数据
  const [materialLotData, setMaterialLotData] = useState<{ [kay: string]: any }>({}); // 工单数据
  const [flagModal, setFlagModal] = useState(''); // 报工退回展示的弹框-是否为末道序'N''Y'
  const [numValue, setNumValue] = useState(''); // 当前键盘输入的值
  const [printModalShow, setPrintModalShow] = useState(false); // 是否展示打印弹框
  const [stepInfoData, setStepInfoData] = useState(Array); // 工序步骤数据
  const [stepModal, setStepModal] = useState(false); // 是否展示步骤弹框
  const detailDs = useMemo(() => new DataSet(detailDS()), []);
  const materialDs = useMemo(() => new DataSet(materialDS()), []);
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const processDs = useMemo(() => new DataSet(processDS()), []);
  const stepDs = useMemo(() => new DataSet(stepDS()), []); // 工序步骤数据
  const printDs = useMemo(() => new DataSet(printDS()), []);
  const editModalDetailDs = useMemo(() => new DataSet(editModalDetailDS()), []);
  const workOrderLovDs = useMemo(() => new DataSet(workOrderLovDS()), []);
  const [samePrintState, setSamePrintState] = useState(false);
  const [sameAgainState, setSameAgainState] = useState(false);
  const [trendsNum, setTrendsNum] = useState(''); // 工单趋势数量
  const [countNum, setCountNum] = useState(0);
  const [weighingWeighVal, setWeighingWeighVal] = useState(0); // 称重重量
  const trendsNumRef = useRef(trendsNum);
  const { run: fetchWoDetail, loading: fetchWoDetailLoading } = useRequest(FetchWoDetail(), { manual: true, needPromise: true });
  const { run: scanMaterialLot, loading: scanMaterialLotLoading } = useRequest(ScanMaterialLot(), { manual: true, needPromise: true });
  const { run: completeWo, loading: completeWoLoading } = useRequest(CompleteWo(), { manual: true, needPromise: true });
  const { run: returnWo, loading: returnWoLoading } = useRequest(ReturnWo(), { manual: true, needPromise: true });
  const { run: fetchMaterialLotQty } = useRequest(FetchMaterialLotQty(), { manual: true, needPromise: true });
  const { run: containerUpdate } = useRequest(ContainerUpdate(), { manual: true, needPromise: true });
  const { run: progressStart, loading: progressStartLoading } = useRequest(ProgressStart(), { manual: true, needPromise: true });
  const { run: containerScan } = useRequest(ContainerScan(), { manual: true, needPromise: true });
  const { run: progressEnd, loading: progressEndLoading } = useRequest(ProgressEnd(), { manual: true, needPromise: true });
  const { run: exectueBack, loading: exectueBackLoading } = useRequest(ExectueBack(), { manual: true, needPromise: true });

  useEffect(() => {
    trendsNumRef.current = trendsNum;
  }, [trendsNum])

  useEffect(() => {
    // 还原一些状态
    setFlagModal('');
    setNumValue('');
    setPrintModalShow(false);
    setStepModal(false);
    setTrendsNum('');
  }, []);

  useEffect(() => {
    if (numValue) {
      detailDs.current?.set('weighingWeight', numValue);
    }
  },[numValue])

  useEffect(() => {
    detailDs.loadData([workOrderData]);
    detailDs.current?.set('bagWeight', workOrderData.tonBagWeight)
    detailDs.current?.set('packedNumber', workOrderData.completeMaterialLotList?.length)
  }, [workOrderData]);

  useEffect(() => {
    if (flagModal === 'Y') {
      processDs.current?.set('maxQty', null);
      processDs.current?.set('qty', null);
      processDs.current?.set('materialLotId', null);
      processDs.current?.set('barCode', null);
      processDs.current?.set('requestTypeCode', null);
    }
  }, [flagModal]);

  // DS事件监听
  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (printDs) {
      const header = flag ? printDs.addEventListener : printDs.removeEventListener;
      // 头选中和撤销监听
      header.call(printDs, 'select', handleTableDsSelect);
      header.call(printDs, 'unSelect', handleTableDsSelect);
      header.call(printDs, 'unSelectAll', handleTableDsSelect);
      // 列表加载事件
      header.call(printDs, 'load', handleTableDsSelect);
    }
  };

  const handleTableDsSelect = () => {
    // NO1-GW-SL001
    const firstSelect = printDs.selected
    if (!firstSelect.length) {
      printDs.records.map(item => {
        item.selectable = true;
        return item;
      });
      setSamePrintState(false);
      setSameAgainState(false);
    } else {
      printDs.records.map(item => {
        item.selectable = item.get('printStatus') === firstSelect[0].get('printStatus');
        return item;
      });
      const _printData = firstSelect.every(item => item.get('printStatus') === '未打印');
      const _againData = firstSelect.every(item => item.get('printStatus') === '已打印');
      setSamePrintState(_printData);
      setSameAgainState(_againData);
    }
  }

  const handleChangeWo = useCallback(
    (value) => {
      setChecked(false);
      setWorkOrderData({});
      setMaterialLotData({});
      const container = document.querySelector(`.${styles.doneStepFlagModals}`);
      if (container) {
        document.querySelector('.ProcessReportingRecordsForm')?.appendChild(container);
        setStepModal(false);
      }
      setTrendsNum('');
      dispatch({
        type: DispatchType.update,
        payload: {
          woReportDetail: {},
          workOrderData: {},
        },
      });
      detailDs.current?.set('materialLot', null);
      if (!value) {
        return;
      }
      return fetchWoDetail({
        params: {
          workOrderId: value.workOrderId,
          operationId: enterInfo?.selectOperation?.operationId,
          routerStep: stepDs.current?.get('routerStep') || null,
          shiftCode: enterInfo?.shiftCode,
          shiftDate: enterInfo?.shiftDate,
          workcellId: enterInfo?.workStationId,
        },
      }).then(res => {
        if (res && res.success) {
          handleAddRecords({
            cardCode,
            messageType: 'SUCCESS',
            message: `${intl.get(`${modelPrompt}.weighing.reporting.card.query`).d(`称重报工记录卡片，查询工单`)}${value.workOrderNum}${intl.get(`${modelPrompt}.success`).d('成功')}`,
          });
          editModalDetailDs.reset();
          if (res.rows?.completeMaterialLotList?.length) {
            tableDs.loadData(res.rows?.completeMaterialLotList)
          }
          setWorkOrderData(res.rows);
          setButtonAlter(res.rows?.buttonAlter);
          dispatch({
            type: DispatchType.update,
            payload: {
              woReportDetail: res?.rows || {},
              workOrderData: res?.rows || {},
            },
          });
          if (res.rows.stepInfoList?.length) {
            setStepInfoData(res.rows.stepInfoList);
            setStepModal(true);
          }
          if (res.rows.doneStepFlag === 'Y') {
            materialLotInput.current?.focus();
          }
        } else {
          handleAddRecords({
            cardCode,
            messageType: 'FAIL',
            message: `${intl.get(`${modelPrompt}.weighing.reporting.card.query`).d(`称重报工记录卡片,查询工单`)}${value.workOrderNum}${intl.get(`${modelPrompt}.failed`).d('失败') }`,
          });
        }
      })
    },
    [detailDs, materialLotInput.current, enterInfo?.selectOperation?.operationId],
  )

  const columnWo = [
    {
      name: 'workOrderNum',
    },
    {
      name: 'woRenderQty',
      renderer: ({ record }) => {
        return `${record?.get('woCompletedQty')}/${record?.get('woQty')}`;
      },
    },
    {
      name: 'materialInfo',
    },
    {
      name: 'materialName',
    },
    {
      name: 'planTime',
      width: 300,
      renderer: ({ record }) => {
        return `${moment(record?.get('planStartTime')).format('YYYY-MM-DD HH:mm')}-${moment(record?.get('planEndTime')).format('YYYY-MM-DD HH:mm')}`;
      },
    },
  ];

  const onRow = ({ record }) => {
    return {
      onClick: () => {
        workOrderLovDs.data.forEach(item => {
          item.isSelected = false;
        });
        record.isSelected = true;
      },
      onDoubleClick: () => {
        woModal.close();
        handleChangeWo(record.data);
      },
    };
  };

  const handleFetchLovData = (value?) => {
    workOrderLovDs.queryDataSet?.current?.reset();
    if (value === 'click') {
      workOrderLovDs.setQueryParameter('workOrderNum', null);
    } else if (value) {
      workOrderLovDs.setQueryParameter('workOrderNum', value);
    } else {
      workOrderLovDs.setQueryParameter('workOrderNum', null);
      materialDs.loadData([]);
      setChecked(false);
      setWorkOrderData({});
      setMaterialLotData({});
      setTrendsNum('');
      return;
    }
    workOrderLovDs.setQueryParameter('prodLineId', enterInfo.productionLineId);
    workOrderLovDs.setQueryParameter('workcellId', enterInfo.workStationId);
    workOrderLovDs.setQueryParameter('operationId', enterInfo.selectOperation?.operationId);
    workOrderLovDs.query().then(res => {
      if (res && !res.failed) {
        if (res.content.length === 1) {
          handleChangeWo(res.content[0]);
          return
        }
        if (res.content.length === 0) {
          return ONotification.error({ message: intl.get(`${modelPrompt}.no.found.wo`).d("未查询到WO") });
        }
        woModal = Modal.open({
          header: (
            <div style={{ display: 'flex' }} className="header">
              <img src={cardSvg} alt="" className="titleIcon" />
              <div className="c7n-pro-modal-title">{intl.get(`${modelPrompt}.workOrder`).d('工单')}</div>
            </div>
          ),
          destroyOnClose: true,
          closable: false,
          style: {
            width: '95%',
          },
          mask: true,
          contentStyle: {
            background: '#38708F',
          },
          className: styles.workOrderTextModals,
          // @ts-ignore
          children: <Table dataSet={workOrderLovDs} columns={columnWo} onRow={onRow} />,
          okProps: {
            style: {
              background: '#00D4CD !important',
              color: 'white !important',
              borderColor: '#00d4cd !important',
            },
          },
          onOk: () => {
            handleChangeWo(workOrderLovDs.selected[0].data);
          },
        })
      }
    });
  }

  const onScanMaterialLot = useCallback(
    (e) => {
      const _value = e.target.value.trim();
      setMaterialLotData({})
      setChecked(false);
      if (!_value) {
        return;
      }
      scanMaterialLot({
        params: {
          workOrderId: workOrderData.workOrderId,
          materialLotCode: _value,
        },
      }).then(res => {
        if (res && res.success) {
          setChecked(true);
          setMaterialLotData(res.rows)
          // setWorkOrderData({
          //   ...workOrderData,
          //   materialLot: _value,
          //   lastCompletedQty: workOrderData.lastCompletedQty + (trendsNumRef.current ? Number(trendsNumRef.current) : 0),
          //   lastCompleted: `${res.rows.materialLotCode}/${_value}`,
          // })
          // setTrendsNum('');
          dispatch({
            type: DispatchType.update,
            payload: {
              woReportDetail: {
                ...workOrderData,
                lastCompleted: `${res.materialLotCode}/${_value}`,
              },
              workOrderData: {
                ...workOrderData,
                lastCompleted: `${res.materialLotCode}/${_value}`,
              },
            },
          });
          materialDs.loadData([res.rows]);
          handleAddRecords({
            cardCode,
            messageType: 'SUCCESS',
            message: `${intl.get(`${modelPrompt}.weighing.reporting.card.scanMaterialLot`).d('称重报工记录卡片，扫描物料批')}${_value}${intl.get(`${modelPrompt}.success`).d('成功')}`,
          });
          return Promise.resolve();
        }
        handleAddRecords({
          cardCode,
          messageType: 'FAIL',
          message: `${intl.get(`${modelPrompt}.weighing.reporting.card.scanMaterialLot`).d('称重报工记录卡片，扫描物料批')}${_value}${intl.get(`${modelPrompt}.failed`).d('失败')}`,
        });
        return Promise.reject();
      })
    },
    [workOrderData],
  )

  const onCompleteWo: (value: string) => boolean = (value: string) => {
    if (!value || !workOrderData.workOrderId || Number(value) <= 0) {
      return false;
    }
    setNumValue(value);
    return completeWo({
      params: {
        ...workOrderData,
        ...detailDs.current?.toData(),
        tonBagWeight: detailDs.current?.get('bagWeight'),
        materialLotCode: detailDs.current?.get('materialLot') ? materialLotData?.materialLotCode : '',
        workcellId: enterInfo?.workStationId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        inputQty: value,
      },
    }).then((res => {
      if (res && res.success) {
        handleChangeWo({ workOrderId: detailDs.current?.get('workOrderId') })
        ONotification.success({});
        setWorkOrderData({
          ...workOrderData,
          lastCompletedQty: workOrderData.lastCompletedQty + (trendsNum ? Number(trendsNum) : 0),
        })
        setTrendsNum(`+${value}`)
        detailDs.current?.set('materialLot', null);
        onScanMaterialLot({ target: { value: res.rows.materialLotCode } })
        if (workOrderData.doneStepFlag === 'Y') {
          handleAddRecords({
            cardCode,
            messageType: 'SUCCESS',
            message: `${intl.get(`${modelPrompt}.materialLot`).d('物料批')}${res.rows.materialLotCode}${intl.get(`${modelPrompt}.complete`).d('完工')}${res.rows.qty}${res.rows.uomName}${intl.get(`${modelPrompt}.material`).d('物料')}${res.rows.materialCode}`,
          });
        } else {
          handleAddRecords({
            cardCode,
            messageType: 'SUCCESS',
            message: `${intl.get(`${modelPrompt}.weighing.reporting.card.successfulExecution`).d('称重报工记录卡片，执行完工成功')}`,
          });
        }
        // setTimeout(() => {
        //   handleChangeWo({workOrderId: detailDs.current?.get('workOrderId')});
        // }, 1000);
        return true;
      }
      handleAddRecords({
        cardCode,
        messageType: 'FAIL',
        message: `${intl.get(`${modelPrompt}.weighing.reporting.card.executionCompletionFailure`).d('工序报工记录卡片，执行完工失败')}`,
      });
      return false;
    }))
  }

  const onReturnWo: (value: string, hanlded?: string) => boolean = (value: string, hanlded?: string) => {
    if ((!value || !workOrderData.workOrderId) && workOrderData.doneStepFlag !== 'Y') {
      return false;
    }
    setNumValue(value);
    if (hanlded === 'hanlded') {
      return returnWo({
        params: {
          ...workOrderData,
          materialLotCode: processDs.current?.get('barCode') || materialLotData?.materialLotCode,
          workcellId: enterInfo?.workStationId,
          shiftCode: enterInfo?.shiftCode,
          shiftDate: enterInfo?.shiftDate,
          returnQty: value,
          returnLotId: processDs?.current?.get('materialLotId') || null,
        },
      }).then((res => {
        if (res && res.success) {
          const container = document.querySelector(`.${styles.doneStepFlagModals}`);
          if (container) {
            document.querySelector('.ProcessReportingRecordsForm')?.appendChild(container);
            setFlagModal('');
          }
          ONotification.success({});
          setWorkOrderData({
            ...workOrderData,
            lastCompletedQty: workOrderData.lastCompletedQty + (trendsNum ? Number(trendsNum) : 0),
          })
          setTrendsNum(`-${value}`)
          detailDs.current?.set('materialLot', null);
          onScanMaterialLot({ target: { value: res.rows.materialLotCode || '' } })
          if (workOrderData.doneStepFlag === 'Y') {
            handleAddRecords({
              cardCode,
              messageType: 'SUCCESS',
              message: `${intl.get(`${modelPrompt}.materialLot`).d('物料批')}${res.rows.materialLotCode}${intl.get(`${modelPrompt}.returnCompletion`).d('完工退回')}${res.rows.qty}${res.rows.uomName}${intl.get(`${modelPrompt}.material`).d('物料')}${res.rows.materialCode}`,
            });
          } else {
            handleAddRecords({
              cardCode,
              messageType: 'SUCCESS',
              message: `${intl.get(`${modelPrompt}.weighing.reporting.card.returnCompletionSuccess`).d('工序报工记录卡片，执行完工退回成功')}`,
            });
          }
          // setTimeout(() => {
          //   handleChangeWo({workOrderId: detailDs.current?.get('workOrderId')});
          // }, 1000);
          return true;
        }
        handleAddRecords({
          cardCode,
          messageType: 'FAIL',
          message: `${intl.get(`${modelPrompt}.weighing.reporting.card.returnCompletionFailure`).d('工序报工记录卡片，执行完工退回失败')}`,
        });
        return false;
      }))
    }
    setFlagModal(workOrderData.doneStepFlag);
    return true;
  }

  const handleFetchMaterialQty = () => {
    const value = processDs?.current?.get('requestTypeCode');
    fetchMaterialLotQty({
      params: {
        ...workOrderData,
        workcellId: enterInfo?.workStationId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        materialLotCode: value,
      },
    }).then((res => {
      if (res && res.success) {
        processDs?.current?.init('materialLotId', res.rows.materialLotId);
        processDs?.current?.init('maxQty', res.rows.primaryUomQty);
        processDs?.current?.init('barCode', res.rows.materialLotCode);
        if (numValue && Number(numValue) < Number(res.rows.primaryUomQty)) {
          processDs?.current?.init('qty', Number(numValue));
        } else {
          processDs?.current?.init('qty', res.rows.primaryUomQty);
        }
      }
    }))
  }

  const handleClose = () => {
    const container = document.querySelector(`.${styles.doneStepFlagModals}`);
    if (container) {
      document.querySelector('.ProcessReportingRecordsForm')?.appendChild(container);
      setFlagModal('');
    }
  }

  const handleConfirm = () => {
    if (flagModal === 'Y') {
      if (!processDs.current?.get('maxQty')) {
        return ONotification.error({ message: intl.get(`${modelPrompt}.please.enter.the.scanning.barcode`).d("请输入扫描条码") });
      }
      if (!processDs.current?.get('qty')) {
        return ONotification.error({ message: intl.get(`${modelPrompt}.please.enter.the.quantity`).d("请输入数量") });
      }
      onReturnWo(processDs.current?.get('qty'), 'hanlded');
    } else {
      onReturnWo(numValue, 'hanlded');
    }
  }

  const renderOkObj = {
    color: 'rgba(100, 222, 163, 1)',
    background: 'rgba(100, 222, 163, 0.15)',
    padding: '4px 14px',
  }

  const renderNgObj = {
    color: 'rgba(242, 58, 80, 1)',
    background: 'rgba(242, 58, 80, 0.2)',
    padding: '4px 14px',
  }

  const renderPdObj = {
    color: 'rgba(255, 255, 255, 1)',
    background: 'rgba(255, 255, 255, 0.15)',
    padding: '4px 14px',
  }

  const column = [
    {
      name: 'identification',
    },
    {
      name: 'primaryUomQty',
    },
    {
      name: 'status',
    },
    {
      name: 'qualityStatusDesc',
      renderer: ({ record }) => {
        if (record.get('qualityStatusDesc') === '合格') {
          return <span style={renderOkObj}>{record.get('qualityStatusDesc')}</span>
        }
        if (record.get('qualityStatusDesc') === '不合格') {
          return <span style={renderNgObj}>{record.get('qualityStatusDesc')}</span>
        }
        if (record.get('qualityStatusDesc') === '待定') {
          return <span style={renderPdObj}>{record.get('qualityStatusDesc')}</span>
        }
        return record.get('qualityStatusDesc');
      },
    },
    {
      name: 'printTimes',
    },
    {
      name: 'productionDate',
      width: 170,
    },
  ]

  const handleCloseStep = () => {
    const container = document.querySelector(`.${styles.doneStepFlagModals}`);
    if (container) {
      document.querySelector('.ProcessReportingRecordsForm')?.appendChild(container);
      setStepModal(false);
    }
  }

  const handleOpenPrint = async () => {
    printRef.current.print();
  }

  const handleFilterPrint = () => {
    const materialLotCode = detailDs.current?.get('barCode');
    printDs.setQueryParameter('materialLotCode', materialLotCode);
    printDs.setQueryParameter('eoId', workOrderData?.eoId);
    printDs.query();
  }

  const printCallback = () => {
    handleFilterPrint();
  }

  useEffect(() => {
    if (printModalShow || flagModal || stepModal) {
      const container = document.querySelector(`.${styles.doneStepFlagModals}`);
      if (container) {
        document.getElementById('operationPlatform')?.appendChild(container);
      }
    }
  }, [printModalShow, flagModal, document.querySelector(`.${styles.doneStepFlagModals}`)]);

  const handleCloseStepModal = () => {
    const container = document.querySelector(`.${styles.doneStepFlagModals}`);
    if (container) {
      document.querySelector('.ProcessReportingRecordsForm')?.appendChild(container);
      setPrintModalShow(false)
    }
  }

  const numberKeyboardProps = {
    materialLotTop: detailDs.current?.get('materialLot'),
    checkedMaterialLot: checked,
    workOrderData,
  }


  const columns: ColumnProps[] = [
    {
      name: 'materialLotCode',
      width: 180,
    },
    {
      name: 'productionDate',
      width: 100,
      renderer: ({ record }) => {
        const date = record?.get('productionDate');
        const time = date.split(' ');
        return <Tooltip placement="right" title={date} popupInnerStyle={{ color: '#fff', backgroundColor: '#000',zIndex: 999 }}>{time[1]}</Tooltip>
      },
    },
    {
      name: 'materialCode',
      width: 150,
    },
    {
      name: 'materialName',
      width: 150,
    },
    {
      name: 'primaryUomQty',
      width: 120,
    },
    {
      name: 'secondaryUomQty',
      width: 120,
    },
    {
      name: 'lot',
      width: 120,
    },
    {
      name: 'containerCode',
      width: 150,
    },
    {
      name: 'createdByName',
      width: 130,
    },
  ];

  const handleChangeSwitch = (value, name) => {
    if (name === 'weighingWeight') {
      let count = 0
      if (value && !timeOut) {
        timeOut = setInterval(() => {
          count += 1
          setCountNum(count);
        }, 1000)
      }
      if (!value && timeOut) {
        clearInterval(timeOut)
        timeOut = null;
        setCountNum(0);
      }
    }
    if (name === 'palletBarCode') {
      if (!value) {
        detailDs.setState('disabled', true)
        detailDs.current?.set('palletWeight', null)
        detailDs.current?.set('palletBarCode', null)
        detailDs.current?.set('containerId', null)
      } else {
        detailDs.setState('disabled', false)
      }
    }
  }

  const editModal = (type) => {
    if (!detailDs.current?.get('workOrderId') || buttonAlter === 'B') {
      return
    }
    if (type === 'palletWeight' && detailDs.getState('disabled') || type === 'palletWeight' && !detailDs.current?.get('containerId')) {
      return
    }
    if (type === 'doBack' && !tableDs.selected?.length) {
      return ONotification.error({ message: intl.get(`${modelPrompt}.noDataSelectedPleaseCheck`).d('未选择数据，请检查！')})
    }
    editModalDetailDs.setState('type', type)
    const obj = {
      bagWeight: intl.get(`${modelPrompt}.editBagWeight`).d('吨袋重量修改'),
      palletWeight: intl.get(`${modelPrompt}.editPalletWeight`).d('托盘重量修改'),
      transformNumber: intl.get(`${modelPrompt}.transformNumber`).d('转换个数'),
      doBack: intl.get(`${modelPrompt}.materialLotDoBack`).d('物料批退回'),
    }
    editingModal = Modal.open({
      key: Modal.key(),
      destroyOnClose: true,
      closable: false,
      style: {
        width: '45%',
      },
      header: (
        <CardLayout.Layout>
          <CardLayout.Header title={obj[type]} />
        </CardLayout.Layout>),
      contentStyle: {
        background: '#38708F',
      },
      children: (<EditDetail type={type} formDs={editModalDetailDs} detailDs={detailDs} tableDs={tableDs} closeModal={closeModal} />),
      onOk: ()=>handleOk(type),
    })
  }

  const closeModal = () => {
    editingModal.close();
  }

  const handleOk = async (type) => {
    const validate = await editModalDetailDs.validate();
    if (!validate) {
      return false;
    }
    if (type === 'bagWeight') {
      const _bagWeight = editModalDetailDs.current?.get('editBagWeight');
      detailDs.current?.set('bagWeight', _bagWeight);
    }
    if (type === 'palletWeight') {
      const _palletWeight = editModalDetailDs.current?.get('editPalletWeight');
      const res = await containerUpdate({
        params: {
          containerId: detailDs.current?.get('containerId'),
          containerCode: detailDs.current?.get('palletBarCode'),
          containerWeight: editModalDetailDs.current?.get('editPalletWeight'),
        },
      })
      if (res?.success) {
        ONotification.success({})
        detailDs.current?.set('palletWeight', _palletWeight);
        return true;
      }
      return false;
    }
    if (type === 'transformNumber') {
      const _transformNumber = editModalDetailDs.current?.get('transformNumber');
      const _weighingWeight = editModalDetailDs.current?.get('weighingWeight')
      const _weighingNum = editModalDetailDs.current?.get('weighingNum')
      const _unitWeight = editModalDetailDs.current?.get('unitWeight')
      detailDs.current?.set('transformNumber', _transformNumber);
      detailDs.current?.set('weighingWeight2', _weighingWeight);
      detailDs.current?.set('weighingNum', _weighingNum);
      detailDs.current?.set('unitWeight', _unitWeight);
    }
    if (type === 'doBack') {
      const res = await exectueBack({
        params: {
          returnLotId: tableDs.selected.map(record => record.get('materialLotId'))[0],
          workOrderId: detailDs.current?.get('workOrderId'),
          inputQty: editModalDetailDs.current?.get('returnNum'),
          returnQty: editModalDetailDs.current?.get('returnNum'),
          eoId: workOrderData?.eoId,
          routerId: workOrderData?.routerId,
          workcellId: enterInfo?.workStationId,
          operationId: workOrderData?.operationId,
          materialId: workOrderData?.materialId,
          routerStepId: workOrderData?.routerStepId,
          shiftCode: enterInfo?.shiftCode,
          shiftDate: enterInfo?.shiftDate,
        },
      })
      if (res?.success) {
        ONotification.success({});
        handleChangeWo({ workOrderId: detailDs.current?.get('workOrderId') })
        return true;
      }
      return false;
    }
  }

  // 加工开始
  const handleStartWork = async () => {
    const params = {
      workOrderId: detailDs.current?.get('workOrderId'),
      eoId: workOrderData?.eoId,
      routerId: workOrderData?.routerId,
      workcellId: enterInfo?.workStationId,
      operationId: workOrderData?.operationId,
      materialId: workOrderData?.materialId,
      inputQty: 0,
      routerStepId: workOrderData?.routerStepId,
      shiftCode: enterInfo?.shiftCode,
      shiftDate: enterInfo?.shiftDate,
    }
    const res = await progressStart({ params });
    if (res?.success) {
      ONotification.success({})
      handleChangeWo({ workOrderId: detailDs.current?.get('workOrderId') })
    }
  }

  // 加工完成
  const handleEndWork = async () => {
    const params = {
      workOrderId: detailDs.current?.get('workOrderId'),
      eoId: workOrderData?.eoId,
      routerId: workOrderData?.routerId,
      workcellId: enterInfo?.workStationId,
      operationId: workOrderData?.operationId,
      materialId: workOrderData?.materialId,
      inputQty: 0,
      routerStepId: workOrderData?.routerStepId,
      shiftCode: enterInfo?.shiftCode,
      shiftDate: enterInfo?.shiftDate,
    };
    const res = await progressEnd({ params });
    if (res?.success) {
      ONotification.success({})
      handleChangeWo({ workOrderId: detailDs.current?.get('workOrderId') })
    }
  }

  // 扫描托盘条码
  const handleScanCode = async () => {
    const res = await containerScan({
      params: {
        containerCode: detailDs.current?.get('palletBarCode'),
      },
    })
    if (res?.success) {
      ONotification.success({})
      detailDs.current?.set({
        containerId: res.rows?.containerId,
        palletWeight: res.rows?.containerWeight ? Number(res.rows?.containerWeight) : null,
      })
    }
  }

  const handleChangeWeighing = (value) => {
    if (value) {
      setWeighingWeighVal(value);
    } else {
      setWeighingWeighVal(0);
    }
  }

  const handleChangeKeyNumber = (value) => {
    if (value) {
      detailDs.current?.set('weighingWeight',value)
    } else {
      detailDs.current?.set('weighingWeight', null)
    }
  }

  return (
    <CardLayout.Layout spinning={fetchWoDetailLoading || scanMaterialLotLoading || completeWoLoading || returnWoLoading}>
      <CardLayout.Header
        className='ProcessReportingRecordsHead'
        title={intl.get(`${modelPrompt}.title`).d('称重报工')}
        help={props?.cardUsage?.remark}
        content={
          <>
            <Button
              color={ButtonColor.primary}
              disabled={!detailDs.current?.get('workOrderId') || buttonAlter === 'B'}
              loading={progressEndLoading}
              onClick={handleEndWork}
            >
              {intl.get(`${modelPrompt}.endWork`).d('加工完成')}
            </Button>
            <Button
              color={ButtonColor.primary}
              disabled={!detailDs.current?.get('workOrderId') || buttonAlter === 'A'}
              style={{ marginRight: 8 }}
              onClick={handleStartWork}
              loading={progressStartLoading}
            >
              {intl.get(`${modelPrompt}.startWork`).d('加工开始')}
            </Button>
            <TextField
              disabled={buttonAlter === 'B'}
              name="materialLot"
              placeholder={intl.get(`${modelPrompt}.please.scan.materialLot`).d("请扫描物料批")}
              dataSet={detailDs}
              // @ts-ignore
              ref={materialLotInput}
              onEnterDown={onScanMaterialLot}
              prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />}
              style={{
                width: '256px',
                marginRight: '-18px',
              }}
            />
          </>
        }
      />
      <CardLayout.Content className='ProcessReportingRecordsForm'>
        <div className={styles.detailContent} id={styles.detailContent}>
          <div>
            <div className={styles.detailContentTop}>
              <Form dataSet={detailDs} labelWidth={100} columns={2} style={{ flex: 1 }}>
                <TextField
                  name="workOrderNum"
                  onChange={(value) => { handleFetchLovData(value) }}
                  placeholder={intl.get(`${modelPrompt}.please.select.wo`).d("请选择WO")}
                  suffix={
                    <img
                      src={lovChoose}
                      alt=''
                      style={{ height: '19px' }}
                      onClick={() => { handleFetchLovData('click') }}
                    />
                  }
                />
                <NumberField name='weighingWeight'
                  disabled={buttonAlter === 'B'}
                  renderer={({ value }) => {
                    return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                      <div>{value || ''}</div>
                      <div>KG</div>
                    </div>;
                  }}
                  addonAfterStyle={{
                    background: 'transparent',
                    border: 'none',
                    color: '#35deef',
                    cursor: 'pointer',
                  }}
                  onChange={handleChangeWeighing}
                  addonAfter={
                    <div>
                      {!!countNum && <span style={{ color: countNum > 5 ? '#60e660' : 'yellow',fontSize: '16px',margin: '5px' }}>{countNum}S</span>}
                      <Switch onChange={val => handleChangeSwitch(val, 'weighingWeight')} disabled={buttonAlter === 'B'} />
                    </div>
                  }
                // addonAfterStyle={{
                //   background: 'transparent',
                //   border: 'none',
                //   color: '#35deef',
                //   cursor: 'pointer',
                // }}
                // addonAfter={
                //   <Icon type="edit-o" />
                // }
                />
                <Output name='batch' />
                <Output name='bagWeight'
                  renderer={({ value }) => {
                    return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                      <div>{value || ''}</div>
                      <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        <div>KG</div>
                        <div style={{ padding: '0 8px', color: "#35deef", cursor: 'pointer' }}>
                          <Icon type="edit-o" onClick={() => { editModal('bagWeight') }}/>
                        </div>
                      </div>
                    </div>;
                  }}
                />
                <Output name='materialCode' />
                <TextField
                  name='palletBarCode'
                  addonAfterStyle={{
                    background: 'transparent',
                    border: 'none',
                    color: '#35deef',
                    cursor: 'pointer',
                  }}
                  disabled={buttonAlter === 'B'}
                  onEnterDown={handleScanCode}
                  addonAfter={
                    <Switch defaultChecked onChange={val => handleChangeSwitch(val, 'palletBarCode')} disabled={buttonAlter === 'B'} />
                  }
                />
                <Output name='materialName' />
                <Output
                  name='palletWeight'
                  renderer={({ value }) => {
                    return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                      <div>{value || ''}</div>
                      <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        <div>KG</div>
                        <div style={{ padding: '0 8px', color: "#35deef", cursor: 'pointer' }}>
                          <Icon type="edit-o" onClick={() => { editModal('palletWeight') }} />
                        </div>
                      </div>
                    </div>;
                  }}
                />
                <Output
                  name='woQty'
                  renderer={({ value }) => {
                    return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                      <div>{value || ''}</div>
                      <div>KG</div>
                    </div>;
                  }}
                />
                <Output
                  name='netWeight'
                  renderer={({ value }) => {
                    return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                      <div>{value || ''}</div>
                      <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        <div>KG</div>
                        <div style={{ padding: '0 8px', color: "transparent" }}>
                          <Icon type="edit-o" />
                        </div>
                      </div>
                    </div>;
                  }}
                />
                <Output
                  name='woCompletedQty'
                  renderer={({ value }) => {
                    return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                      <div>{value || ''}</div>
                      <div>KG</div>
                    </div>;
                  }}
                />
                <Output
                  name='transformNumber'
                  renderer={({ value }) => {
                    return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                      <div>{value || ''}</div>
                      <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                        <div>PCS</div>
                        <div style={{ padding: '0 8px', color: "#35deef", cursor: 'pointer' }}>
                          <Icon type="edit-o" onClick={() => { editModal('transformNumber') }} />
                        </div>
                      </div>
                    </div>;
                  }}
                />
                <Output
                  name='packedNumber'
                  // renderer={({ value }) => {
                  //   return <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                  //     <div>{value || ''}</div>
                  //     <div>PCK</div>
                  //   </div>;
                  // }}
                />
              </Form>
              <div className={styles.materialLotContent}>
                <div style={{ marginBottom: '8px', width: '256px' }}>
                  <Form dataSet={materialDs} labelWidth={60} columns={1} style={{ flex: 1, background: 'rgba(42, 99, 130, 1)' }}>
                    <Output
                      name="identification"
                      renderer={({ value, record }) => {
                        if (!value) {
                          return null;
                        }
                        if (record?.get('enableFlag') === 'Y') {
                          return (
                            <span style={{ display: 'flex', alignItems: 'center' }}>
                              <span style={{ marginRight: '4px' }}>{value}</span>
                              <img src={enableY} alt='' />
                            </span>
                          );
                        }
                        return (
                          <span style={{ display: 'flex', alignItems: 'center' }}>
                            <span style={{ marginRight: '4px' }}>{value}</span>
                            <img src={enableN} alt='' />
                          </span>
                        );
                      }}
                    />
                    <Output
                      name="qtyInfo"
                      renderer={({ value, record }) => {
                        if (record?.get('qualityStatus')) {
                          if (record?.get('qualityStatus') === 'OK') {
                            return (
                              <span style={{ display: 'flex', alignItems: 'center' }}>
                                {value}
                                <img src={qualityOk} alt='' style={{ marginLeft: '4px' }} />
                              </span>
                            );
                          }
                          if (record?.get('qualityStatus') === 'NG') {
                            return (
                              <span style={{ display: 'flex', alignItems: 'center' }}>
                                {value}
                                <img src={qualityNg} alt='' style={{ marginLeft: '4px' }} />
                              </span>
                            );
                          }
                          return (
                            <span style={{ display: 'flex', alignItems: 'center' }}>
                              {value}
                              <img src={qualityPend} alt='' style={{ marginLeft: '4px' }} />
                            </span>
                          );
                        }
                        return null;
                      }}
                    />
                    <Output name="locatorCode" />
                  </Form>
                </div>
                <NumberKeyboard
                  keyboardWidth={256}
                  onOk={onCompleteWo}
                  onReturn={onReturnWo}
                  numberKeyboardProps={numberKeyboardProps}
                  number={weighingWeighVal}
                  handleChange={handleChangeKeyNumber}
                  ds={detailDs}
                />
              </div>
            </div>
          </div>
          <Table
            columns={columns}
            dataSet={tableDs}
          />
          <div className={`${styles.cardFooter} ProcessReportingRecordsForm`}>
            <div className={styles.completeText}>
            </div>
            <div className={styles.printButton}>
              <div id={styles.processReportingRecords}>
                <Button
                  color={ButtonColor.primary}
                  disabled={workOrderData.buttonAlter === 'B'}
                  style={{ background: '#007aff', border: '#007aff' }}
                  onClick={() => { editModal('doBack') }}
                  loading={exectueBackLoading}
                >
                  {intl.get(`${modelPrompt}.doBack`).d("退回")}
                </Button>
                <Button
                  color={ButtonColor.primary}
                  onClick={handleOpenPrint}
                >
                  {intl.get(`${modelPrompt}.print`).d("打印")}
                </Button>
                {/* @ts-ignore */}
                <TemplatePrintButton
                  name={intl.get(`${modelPrompt}.print`).d("打印")}
                  ref={printRef}
                  style={{ display: 'none' }}
                  printButtonCode='HME.REPORT_LOT_PRINT'
                  // printParams={{ materialLotIds: materialLotData.materialLotId }}
                  printCallback={printCallback}
                />
              </div>
            </div>
          </div>
        </div>
        {
          flagModal === 'Y' && (
            <div className={`${styles.doneStepFlagModals}`}>
              <div className={styles.modalTitle}>
                <div>
                  <img src={leftTopLog} alt='' />
                  <span>{intl.get(`${modelPrompt}.scan.return.barcode`).d("扫描退回条码")}</span>
                </div>
              </div>
              <div className={styles.modalContent}>
                <Form dataSet={processDs}>
                  <TextField name="requestTypeCode" prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />} onChange={handleFetchMaterialQty} placeholder={intl.get(`${modelPrompt}.please.enter.the.scanning.barcode`).d("请输入扫描条码")} />
                  <Output name="maxQty" />
                  <NumberField name="qty" placeholder={intl.get(`${modelPrompt}.please.enter.the.quantity`).d("请输入数量")} />
                </Form>
              </div>
              <div className={styles.modalFooter}>
                <Button onClick={handleClose}>{intl.get(`${modelPrompt}.close`).d("关闭")}</Button>
                <Button color={ButtonColor.primary} onClick={handleConfirm}>{intl.get(`${modelPrompt}.confirm`).d("确认")}</Button>
              </div>
            </div>
          )
        }
        {
          flagModal === 'N' && (
            <div className={`${styles.doneStepFlagModals}`}>
              <div className={styles.modalContent}>
                <div>{intl.get(`${modelPrompt}.is.sure.execute.work.report.return`).d("是否确认执行报工退回？")}</div>
              </div>
              <div className={styles.modalFooter}>
                <Button onClick={handleClose}>{intl.get(`${modelPrompt}.close`).d("关闭")}</Button>
                <Button color={ButtonColor.primary} onClick={handleConfirm}>{intl.get(`${modelPrompt}.confirm`).d("确认")}</Button>
              </div>
            </div>
          )
        }
        {
          !!printModalShow && (
            <div className={`${styles.doneStepFlagModals}`} style={{ width: '90%' }}>
              <div className={styles.modalTitle}>
                <div>
                  <img src={leftTopLog} alt='' />
                  <span>{intl.get(`${modelPrompt}.barcode.print`).d("条码打印")}</span>
                  <TextField
                    className={styles.barCodeText}
                    placeholder={intl.get(`${modelPrompt}.please.enter.barcode`).d("请输入条码")}
                    name="barCode"
                    dataSet={detailDs}
                    // @ts-ignore
                    ref={materialLotInput}
                    onEnterDown={handleFilterPrint}
                    prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />}
                  />
                </div>
                <div className={styles.modalRightFooter} id={styles.modalRightFooter}>
                  {/* @ts-ignore */}
                  <TemplatePrintButton
                    name={intl.get(`${modelPrompt}.patchwork`).d("补打")}
                    disabled={!sameAgainState}
                    printButtonCode='HME.REPORT_LOT_PRINT'
                    printParams={{ materialLotIds: printDs.selected.map(item => item.get('materialLotId')).join(',') }}
                    printCallback={printCallback}
                  />
                  {/* @ts-ignore */}
                  <TemplatePrintButton
                    name={intl.get(`${modelPrompt}.print`).d("打印")}
                    disabled={!samePrintState}
                    printButtonCode='HME.REPORT_LOT_PRINT'
                    printParams={{ materialLotIds: printDs.selected.map(item => item.get('materialLotId')).join(',') }}
                    printCallback={printCallback}
                  />
                  <div onClick={() => { handleCloseStepModal() }}>
                    <img src={close} alt='' />
                  </div>
                </div>
              </div>
              <div className={styles.modalContent}>
                <Table
                  dataSet={printDs}
                  // @ts-ignore
                  columns={column}
                  customizedCode='ProcessReportingRecords'
                  showSelectionTips
                  style={{
                    maxHeight: 400,
                  }}
                  onRow={({ record }) => {
                    if (record && record.data && record.get('printStatus') === '已打印') {
                      return {
                        className: styles.printLightRow,
                      };
                    }
                    if (record && record.data && record.get('printStatus') === '未打印') {
                      return {
                        className: styles.againLightRow,
                      };
                    }
                    return {};
                  }}
                />
              </div>
            </div>
          )
        }
        {
          stepModal && (
            <div className={`${styles.doneStepFlagModals}`}>
              <div className={styles.modalTitle}>
                <div>
                  <img src={leftTopLog} alt='' />
                  <span>{intl.get(`${modelPrompt}.select.current.step.processed`).d("选择当前需要加工的步骤")}</span>
                </div>
              </div>
              <div className={styles.modalContent}>
                {
                  stepInfoData.map(item => (
                    // @ts-ignore
                    <Radio dataSet={stepDs} name="routerStep" value={item?.routerStep}>{item?.showInfo}</Radio>
                  ))
                }
              </div>
              <div className={styles.modalFooter}>
                <Button onClick={handleCloseStep}>{intl.get(`${modelPrompt}.close`).d("关闭")}</Button>
                <Button color={ButtonColor.primary} onClick={() => { handleChangeWo({ workOrderId: detailDs.current?.get('workOrderId') }) }}>{intl.get(`${modelPrompt}.confirm`).d("确认")}</Button>
              </div>
            </div>
          )
        }
      </CardLayout.Content>
    </CardLayout.Layout>
  )
})

export default WorkOrderReporting;
