/*
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2024-10-18 17:25:18
 * @LastEditors: <<EMAIL>>
 * @LastEditTime: 2024-10-31 11:38:53
 */
/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-02-17 09:42:06
 * @LastEditTime: 2023-05-19 17:48:51
 * @LastEditors: <<EMAIL>>
 */
import React, { Dispatch, useContext, useReducer } from 'react';

export enum DispatchType {
  update = 'update',
  addMessageRecord = 'addMessageRecord',
}

export interface contextProps {
  /**
   * 是否登录
   */
  logined: boolean;
  /**
   * 登录信息
   */
  enterInfo: EnterInfoObj;
  // cardUsageList: Object[],
  /**
   * 所有的卡片
   */
  cardsList: cardsListArr[];
  /**
   * 设置为主卡片的id
   */
  selectCardsMainId: string;
  /**
   * 卡片操作模式: 平铺模式Tile，独立模式Independent
   */
  cardMode: 'Tile' | 'Independent';
  /**
   * 操作记录
   * 当前操作的卡片取用第一条
   */
  operationRecords: Object[];
  /**
   * 有效的卡片id
   */
  enabledCards: string[];
  /**
   * 是否朗读
   */
  synthRead: boolean;
  /**
   * 通用栏按钮
   */
  commonButton: CommonButtonType[];
  /**
   * 启用的按钮Key
   */
  enabledButtonKeys: string[];
  /**
   * 每个块的布局
   */
  itemLayout: object[];

  workCellList: object[]; // 全部工位

  woReportDetail: object;
  /**
   * 工单数据
   */
  workOrderData?: woObject;
  // modelCategorys: LovData[],
  // modelTypes: LovData[],
  // modelDataMap: {
  //   [key: string]: {
  //     [key: string]: ModelDataInfo[],
  //   } | ModelDataInfo[],
  // } | null,

  containerDetail: Object; // 当前容器信息
  dispatch: Dispatch<{
    type: DispatchType;
    payload: Object;
  }>;
}

const defaultValues: contextProps = {
  logined: false,
  enterInfo: {
    siteId: undefined,
    workStationId: undefined,
    selectOperation: {},
  },
  // cardUsageList: [],
  cardsList: [],
  selectCardsMainId: '',
  cardMode: 'Tile',
  operationRecords: [],
  enabledCards: [],
  enabledButtonKeys: [],
  synthRead: true,
  commonButton: [],
  itemLayout: [],
  containerDetail: {
    containerId: undefined,
    containerLoadDetailList: [],
    containerCode: '',
    containerTypeDescription: '',
    capacityQty: undefined,
    sumLoadQty: undefined,
  },
  workCellList: [],
  woReportDetail: {},
  workOrderData: {},
  // modelCategorys: [],
  // modelTypes: [],
  // modelDataMap: null,
  dispatch: () => 0,
};

const OperationPlatformContext = React.createContext<contextProps>(defaultValues);

function operationPlatformReducer(
  state: contextProps,
  action: { type: DispatchType; payload: Object },
) {
  switch (action.type) {
    case 'update':
      return { ...state, ...action.payload };
    case 'addMessageRecord':
      return { ...state, operationRecords: [...(state.operationRecords || []), action.payload] };
    default:
      throw new Error('错误的dispatch type!');
  }
}

export const OperationPlatformProvider = ({ children }) => {
  const [operationPlatformContext, dispatch] = useReducer(operationPlatformReducer, defaultValues);
  const context: contextProps = { ...operationPlatformContext, dispatch };
  return (
    <OperationPlatformContext.Provider value={context}>
      {children}
    </OperationPlatformContext.Provider>
  );
};

export const useOperationPlatform = () => {
  const context = useContext(OperationPlatformContext);
  return context;
};

type EnterInfoObj = {
  siteId: any;
  siteName?: string;
  userId?: number;
  userName?: number;
  workStationId?: number;
  workStationCode?: string;
  workStationName?: string;
  shiftCode?: string;
  shiftDate?: string;
  productionLineId?: number;
  productionLineCode?: string;
  productionLineName?: string;
  employeeDTO?: { [key: string]: any };
  selectOperation: SelectObj;
};

type SelectObj = {
  workStationId?: number;
  operationName?: string;
  description?: string;
  operationId?: number;
};

type cardsListArr = {
  cardId?: string,
  cardCode: string,
  cardName?: string,
  cardType?: string,
  cardPreviewUrl?: string,
  checked?: boolean,
  cardDefaultW?: number,
  cardDefaultH?: number,
  cardMinH?: number,
  cardMinW?: number,
  priority?: string,
}

type CommonButtonType = {
  meaning: string;
  value: string;
};

type woObject = {
  routerStepId?: number,
  routerStepName?: string,
  eoId?: number,
  eoNum?: string,
  routerId?: number,
  routerName?: string,
  workOrderId?: number,
  containerRefreshFlag?: string,
  /**
   * 是否展示数据采集卡片，为Y时展示, 为N时不展示
   */
  dataCollectionShowFlag?: 'Y' | 'N',
}
// type ModelDataInfo = {
//   attachmentUUID: string, // 文件夹名，模型唯一标识！
//   bucketName: string, // 桶名
//   category: string, // 类别
//   type: string, // 类型
//   picName: string, // 图片名
//   picUrl: string, // 图片存储地址
//   fileName: string, // 模型名称
//   fileUrl: string, // 模型存储地址
//   initialFlag: 'Y' | 'N', // 初始化标识
//   enableFlag: 'Y' | 'N', // 有效性标识
//   userId: string, // 上传用户Id
// }

// type LovData = {
//   value: string,
//   meaning: string,
//   orderSeq: number,
//   enabledFlag: 0 | 1,
//   parentValue?: string,
// }
