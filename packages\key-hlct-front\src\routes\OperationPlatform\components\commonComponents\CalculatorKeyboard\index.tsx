import React, { createElement } from "react";
import { renderToNodeStream } from "react-dom/server";
import notification from "utils/notification";
import './index.modules.less';

type KeyBoardOpenParams = {
  parentId: string;
}

const HelloDiv = () => {
  return(
    <div id='HelloWorld'>wairi</div>
  )
}


const CalculatorKeyboard = {
  open: (params: KeyBoardOpenParams) => {
    if(document.getElementById('HelloWorld')){
      CalculatorKeyboard.close();
    }
    const parentNode = document.getElementById(params.parentId);
    if(!parentNode) {
      notification.error({
        message: '未找到父节点',
      });
      return;
    }
    const _div = document.createElement('div')
    _div.innerHTML = 'hello world';
    _div.id = 'HelloWorld';
    parentNode.appendChild(_div);
  },
  close: () => {
    const _div = document.getElementById('HelloWorld');
    if(!_div){
      return;
    }
    const parentNode = _div.parentNode;
    parentNode!.removeChild(_div);
  },
}

export default CalculatorKeyboard;
