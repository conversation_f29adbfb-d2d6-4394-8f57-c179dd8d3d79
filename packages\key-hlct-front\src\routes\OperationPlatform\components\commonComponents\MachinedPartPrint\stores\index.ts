import intl from 'utils/intl';
import { DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.operationPlatform';

const printDS: () => DataSetProps = () => ({
  selection: DataSetSelection.multiple,
  autoQuery: false,
  autoCreate: true,
  paging: true,
  primaryKey: 'eoId',
  // cacheSelection: true,
  // cacheModified: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'eoId',
    },
    {
      name: 'identification',
      label: intl.get(`${modelPrompt}.identification`).d('执行作业标识'),
    },
    {
      name: 'eoNum',
      label: intl.get(`${modelPrompt}.eoNum`).d('执行作业编码'),
    },
    {
      name: 'wipQty',
      label: intl.get(`${modelPrompt}.wipQty`).d('数量'),
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('赋码时间'),
    },
    {
      name: 'operationName',
      label: intl.get(`${modelPrompt}.operationName`).d('当前工序'),
    },
    {
      name: 'wipStatusDesc',
      label: intl.get(`${modelPrompt}.wipStatusDesc`).d('当前状态'),
    },
    {
      name: 'printTimes',
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'qualityStatusDesc',
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'printStatus',
      label: intl.get(`${modelPrompt}.printStatus`).d('打印状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-workpiece-common/print/ui`,
        method: 'GET',
      };
    },
  },
  // data: [
  //   {
  //     materialLotId: 1,
  //     materialLotCode: '457670',
  //     primaryUomQty: 1,
  //     materialLotStatus: '待上架',
  //     qualityStatus: '合格',
  //     printTimes: '2',
  //     printStatus: '已打印',
  //     productionDate: '12.22 7:45:41',
  //   },
  //   {
  //     materialLotId: 2,
  //     materialLotCode: '457670',
  //     primaryUomQty: 1,
  //     materialLotStatus: '待上架',
  //     qualityStatus: '合格',
  //     printTimes: '2',
  //     printStatus: '已打印',
  //     productionDate: '12.22 7:45:41',
  //   },
  //   {
  //     materialLotId: 3,
  //     materialLotCode: '457670',
  //     primaryUomQty: 1,
  //     materialLotStatus: '待上架',
  //     qualityStatus: '合格',
  //     printTimes: '2',
  //     printStatus: '未打印',
  //     productionDate: '12.22 7:45:41',
  //   },
  // ],
});

export { printDS };
