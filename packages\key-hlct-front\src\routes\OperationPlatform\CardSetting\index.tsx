import React, { useMemo, useState, useCallback } from 'react';
import { <PERSON>B<PERSON>, But<PERSON> } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import notification from 'utils/notification';
import intl from 'utils/intl';
import styles from './index.module.less';
import { useOperationPlatform, DispatchType } from '../contextsStore';
import leftTopLog from '@/assets/operationPlatformCard/leftTopLog.svg';
import cardBackground from '@/assets/operationPlatformCard/cardBackground.png';
import singleUnselect from '@/assets/operationPlatformCard/singleUnselect.png';
import singleSelect from '@/assets/operationPlatformCard/singleSelect.png';
import multipleUnselect from '@/assets/operationPlatformCard/multipleUnselect.png';
import multipleSelect from '@/assets/operationPlatformCard/multipleSelect.png';
import TabCardsRender from './TabCardsRender';

const modelPrompt = 'tarzan.operationPlatform';

const CardSetting = (props) => {
  const {
    dispatch,
    cardMode,
    selectCardsMainId,
    cardsList,
    enabledCards,
    commonButton,
    enabledButtonKeys,
    itemLayout,
  } = useOperationPlatform();

  const [activeKey, setActiveKey] = useState('全部') // 默认tab
  const [tempCardMode, setTempCardMode] = useState(cardMode); // 当前卡片模式
  const [primaryCardId, setPrimaryCardId] = useState(selectCardsMainId); // 主卡片ID，仅当cardMode为Tile时使用
  const [tempSelectedCardIds, setTempSelectedCardIds] = useState<string[]>(enabledCards); // 当前启用卡片ids
  const [tempSelectedButtonKeys, setTempSelectedButtonKeys] = useState(enabledButtonKeys); // 当前启用按钮keys

  // useEffect(() => {
  //   initData();
  // }, [cardsList, commonButton]);

  /**
   * 卡片设置取消编辑
   */
  const closeModal = () => {
    props.setCardShow();
  }

  /**
   * 卡片设置确认
   */
  const handleConfirm = () => {
    if (tempCardMode === 'Independent' && !primaryCardId) {
      notification.error({
        message: intl.get(`${modelPrompt}.notification.mainCardRequired`).d('请确认主卡片！'),
      })
      return;
    }
    const newLayout: any = [];
    // 已经有布局的卡片id
    const hasLayout = itemLayout.map(item => item.i);
    // 未有布局的卡片id
    const nHaseLayout = tempSelectedCardIds.filter(item => !hasLayout.includes(item));
    cardsList.forEach(item => {
      if (nHaseLayout.includes(item.cardCode)) {
        newLayout.push({
          i: item.cardCode,
          x: nHaseLayout.indexOf(item.cardCode) === 3 ? 0 : nHaseLayout.indexOf(item.cardCode) > 2 ? (nHaseLayout.indexOf(item.cardCode) - 3) * 4 : 4 * nHaseLayout.indexOf(item.cardCode),
          y: 100,
          w: item.cardDefaultW,
          h: item.cardDefaultH,
          minH: item.cardMinH,
          minW: item.cardMinW,
          moved: false,
          static: false,
          priority: item.priority,
        });
      }
    });
    dispatch({
      type: DispatchType.update,
      payload: {
        cardMode: tempCardMode,
        selectCardsMainId: primaryCardId,
        enabledCards: tempSelectedCardIds,
        enabledButtonKeys: tempSelectedButtonKeys,
        itemLayout: [
          ...newLayout,
          ...itemLayout,
        ],
      },
    })
    props.setCardShow();
  }

  /**
   * 修改卡片操作模式
   * @param type 选中类型 "Tile" | "Independent"
   */
  const handleUpdateMode = (type: typeof cardMode) => {
    if (type === tempCardMode) {
      return;
    }
    setTempCardMode(type);
    if (type === 'Tile') {
      setPrimaryCardId('');
      setTempSelectedButtonKeys([]);
    }
  }

  // 设置为主卡片
  const handleMainCards = () => {
    if (!tempSelectedCardIds.length) {
      return;
    }
    const _currentCardId = tempSelectedCardIds[tempSelectedCardIds.length - 1];
    if (primaryCardId === _currentCardId) {
      setPrimaryCardId('')
      return;
    }
    setPrimaryCardId(_currentCardId)
  }

  // 通用按钮事件
  const handleChange = useCallback((value, oldValue) => {
    const _selectedButtonKeys = [...tempSelectedButtonKeys];
    if (value) {
      _selectedButtonKeys.push(value);
    } else {
      _selectedButtonKeys.splice(_selectedButtonKeys.indexOf(oldValue), 1);
    }
    setTempSelectedButtonKeys(_selectedButtonKeys);
    dispatch({
      type: DispatchType.update,
      payload: {
        enabledButtonKeys: _selectedButtonKeys,
      },
    });
  }, [commonButton, tempSelectedButtonKeys]);

  const commonRender = useMemo(() => {
    return (
      commonButton?.map(item => (
        <CheckBox
          name='commonButton'
          value={item.value}
          checked={tempSelectedButtonKeys.indexOf(item.value) !== -1}
          onChange={handleChange}
        >
          {item.meaning}
        </CheckBox>
      ))
    )
  }, [commonButton, tempSelectedButtonKeys]);

  /**
   * 选择/反选卡片
   */
  const handleSelectCard = useCallback(
    (cardInfo) => {
      const _cardId = cardInfo.cardCode
      if (tempSelectedCardIds.includes(_cardId)) {
        // 当前卡片id已存在已选择ids中，做反选
        if (_cardId === primaryCardId) {
          // 反选卡片为主卡片时，清空选中的主卡片id
          setPrimaryCardId('')
        }
        setTempSelectedCardIds([...tempSelectedCardIds.filter(item => item !== _cardId)])
        return;
      }
      if (cardInfo.attribute1) {
        // 存在attribute1，说明是加工件卡片，加工件卡片只能同时存在一张
        // @ts-ignore
        const processedCardIds = cardsList.filter(item => item.attribute1).map(item => item.cardCode)
        if (tempSelectedCardIds.some(item => processedCardIds.includes(item))) {
          // 存在其它加工件卡片
          setTempSelectedCardIds([...tempSelectedCardIds.filter(item => !processedCardIds.includes(item)), _cardId])
          if (processedCardIds.includes(primaryCardId)) {
            // 主卡片也是加工件卡片时，切换主卡片为当前选择的加工件卡片
            setPrimaryCardId(_cardId)
          }
          return
        }
      }
      // 选择卡片
      setTempSelectedCardIds([...tempSelectedCardIds, _cardId])
    },
    [tempSelectedCardIds, primaryCardId, cardsList],
  )

  return (
    <div className={`${styles.operationPlatformSetting} ${props.isFullFlag ? styles['operationPlatformSetting-full'] : ''}`}>
      <div className={styles.selectModalTitle}>
        <img src={leftTopLog} alt='' />
        <span>{intl.get(`${modelPrompt}.cardSetting.title`).d('卡片配置')}</span>
      </div>
      <img src={cardBackground} alt='' className={styles.cardBackground} />
      <div className={styles.cardContent}>
        <div className={styles.cardTop}>
          {/* <div className={styles.cardTitle}>卡片操作模式：</div> */}
          <div className={styles.cardItem}>
            <div
              className={styles.cardItems}
              style={{
                background: `url(${tempCardMode === 'Tile' ? multipleSelect : multipleUnselect})`,
              }}
              onClick={() => handleUpdateMode('Tile')}
            >
              <div>{intl.get(`${modelPrompt}.tile.mode`).d('平铺模式')}</div>
            </div>
            <div
              className={styles.cardItems}
              style={{ background: `url(${tempCardMode === 'Independent' ? singleSelect : singleUnselect})` }}
              onClick={() => handleUpdateMode('Independent')}
            >
              <div>{intl.get(`${modelPrompt}.independent.mode`).d('独立模式')}</div>
            </div>
          </div>
        </div>
        <div className={styles.cardCenter}>
          <div className={styles.tabHeader}>
            <div className={styles.totalSelect}>
              <div className={styles.cardTitle}>{intl.get(`${modelPrompt}.cardSetting.title`).d('卡片配置')}</div>
              <div className={styles.cardItem}>({intl.get(`${modelPrompt}.cardSetting.selected`).d('已选择')}
                <span>{tempSelectedCardIds.length}</span>
                {intl.get(`${modelPrompt}.cardSetting.itemNumber`).d('项')})</div>
            </div>
            <div className={styles.tabs}>
              <div
                key='全部'
                className={activeKey === '全部' ? styles.tabActive : styles.tab}
                onClick={() => { setActiveKey('全部') }}
              >
                {`${intl.get(`${modelPrompt}.cardSetting.whole`).d('全部') || ''}(${cardsList.length || 0})`}
              </div>
              {
                props.cardTypes.map(item => (
                  <div
                    key={item}
                    className={activeKey === item ? styles.tabActive : styles.tab}
                    onClick={() => { setActiveKey(item) }}
                  >
                    {`${item || ''}(${cardsList.filter(i => i.cardType === item).length || 0})`}
                  </div>
                ))
              }
            </div>
            {tempCardMode === 'Independent' && (
              // <div className={styles.primaryCardBtn} onClick={handleMainCards}>
              //   设置为主卡片
              // </div>
              <Button
                color={ButtonColor.primary}
                onClick={handleMainCards}
              >
                {intl.get(`${modelPrompt}.cardSetting.setMainCard`).d('设置为主卡片')}
              </Button>
            )}
          </div>
          <TabCardsRender
            tabType={activeKey}
            primaryCardId={primaryCardId}
            tempCardMode={tempCardMode}
            cardsList={cardsList}
            selectedCardIds={tempSelectedCardIds}
            handleSelectCard={handleSelectCard}
          />
        </div>
        <div className={styles.totalSelect}>
          <div className={styles.cardTitle}>{intl.get(`${modelPrompt}.cardSetting.commonButtons`).d('通用栏按钮')}</div>
          <div className={styles.cardItem}>({intl.get(`${modelPrompt}.cardSetting.selected`).d('已选择')}
            <span>{tempSelectedButtonKeys.length}</span>
            {intl.get(`${modelPrompt}.cardSetting.itemNumber`).d('项')})</div>
        </div>
        <div className={styles.cardBottom}>
          <div className={styles.cardItem}>
            <div className={styles.cardItems}>
              {commonRender}
            </div>
          </div>
        </div>
      </div>
      <div className={styles.cardFooter}>
        <Button onClick={closeModal}>{intl.get('tarzan.common.button.cancel').d('取消')}</Button>
        <Button color={ButtonColor.primary} onClick={handleConfirm}>
          {intl.get('tarzan.common.button.confirm').d('确定')}
        </Button>
      </div>
    </div>
  )
}

export default CardSetting;
