.headerAfter {
  display: inline-flex;
  align-items: center;

  .pageChange {
    display: inline-flex;
    margin-right: 6px;
    color: white;
  }

  .actionIconDiv {
    i {
      color: white;
      font-size: 20px !important;
      cursor: pointer;

      &:hover {
        color: #5ac9d3;
      }
    }

    i:first-child {
      margin-right: 6px;
    }
  }
}

.InstructionsContent {
  display: inline-flex;
  overflow: hidden;

  .leftSider {
    overflow-y: auto;
    width: 160px;
    background: #2a445e55;
    margin-right: 12px;
    border-radius: 4px;

    .fileLine {
      height: 30px;
      line-height: 30px;
      padding: 0 8px;
      border-radius: 4px;
      color: white;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      cursor: pointer;

      &:hover {
        background: #5ac9d3;
      }

      &_active {
        background: #5ac9d3;
      }
    }
  }

  .rightSider {
    display: flex;
    overflow: auto;
    flex: 1 1;
    border-radius: 4px;
    border: 1px solid white;
    justify-content: center;

    img,
    video {
      max-width: 100%;
      width: fit-content;
      height: fit-content;
    }

    textarea {
      min-height: 100%;
      min-width: 100%;
      max-height: 100%;
      max-width: 100%;
    }
  }
}
