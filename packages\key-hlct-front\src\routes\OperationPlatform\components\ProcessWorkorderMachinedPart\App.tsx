// 数量条形图
import React from 'react';
import { Bar } from '@ant-design/charts';

const NumberCharts = props => {
  const data = [
    {
      year: '数量',
      value: props.data?.scrappedQty || null,
      type: '不良数量',
    },
    {
      year: '数量',
      value: props.data?.notFinishedQty || null,
      type: '未完成数量',
    },
    {
      year: '数量',
      value: props.data?.completedQty || null,
      type: '已完成数量',
    },
  ];
  const config = {
    style: { height: 75 },
    data: data.reverse(),
    isStack: true,
    xField: 'value',
    yField: 'year',
    seriesField: 'type',
    tooltip: null,
    legend: {
      // layout: 'horizontal',
      position: 'bottom',
      // maxRow: '2',
      color: '#5a9ebd',
      itemName: {
        style: {
          fill: 'white',
        },
      },
    },
    xAxis: {
      label: {
        formatter: () => null,
      },

      grid: {
        line: {
          style: {
            lineWidth: 0,
          },
        },
      },
    },
    yAxis: {
      label: {
        formatter: () => null,
      },
      tickLine: null,
      line: null,
    },
    color: ['#10bcce', '#305c79', '#f45063'],

    label: {
      // 可手动配置 label 数据标签位置
      position: 'middle',
      // 'left', 'middle', 'right'
      // 可配置附加的布局方法
      // layout: [
      //   // 柱形图数据标签位置自动调整
      //   {
      //     type: 'interval-adjust-position',
      //   }, // 数据标签防遮挡
      //   {
      //     type: 'interval-hide-overlap',
      //   }, // 数据标签文颜色自动调整
      //   {
      //     type: 'adjust-color',
      //   },
      // ],
    },
  };

  return <Bar {...config} />;
};

export default NumberCharts;
