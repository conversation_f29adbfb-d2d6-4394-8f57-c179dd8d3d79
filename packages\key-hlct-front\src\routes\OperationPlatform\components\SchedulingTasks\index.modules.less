.schedulingTasks {
  :global {
    .c7n-table-tbody {
      .c7n-pro-table-row {
        background: #5a9ebd !important;
      }
    }

    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content table .c7n-pro-table-thead tr th {
      background: #38708F !important;
      color: #70BBF3 !important;
    }

    .c7n-pro-table-content .c7n-pro-table-thead .SchedulingTasks {
      background: #38708F !important;
      color: #70BBF3 !important;
    }

    .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-selection-column {
      background: #5a9ebd !important;
      color: white !important;
    }

    .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
      background: #38708F !important;
      color: white !important;
    }

    .c7n-progress-text {
      color: white !important;
    }
  }
  .schedulingTasksRightHead{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex-flow: row-reverse;
    .schedulingTasksProgress{
      // width: 20vw;
      display: flex;
      .schedulingTasksProgressText{
        margin-right: 10px;
        color: #33F1FF;
        white-space: nowrap;
      }
    }
    .schedulingTasksSelect{
      background: #2F5E81;
      height: 36px;
      line-height: 1;
      color: #fff;
      margin-right: 16px;
    }
  }
}
