import intl from 'utils/intl';
import { DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.operationPlatform';

const siteDS = () => ({
  selection: DataSetSelection.single,
  autoQuery: false,
  autoCreate: true,
  paging: true,
  primaryKey: 'eoId',
  // cacheSelection: true,
  // cacheModified: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'eoId',
    },
    {
      name: 'identification',
      label: intl.get(`${modelPrompt}.identification`).d('执行作业编码'),
    },
    {
      name: 'creationDate',
      label: intl.get(`${modelPrompt}.creationDate`).d('进站时间'),
    },
    {
      name: 'materialRevision',
      label: intl.get(`${modelPrompt}.materialRevision`).d('物料编码/版本'),
    },
    {
      name: 'materialCode',
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'revisionCode',
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
    },
    {
      name: 'workOrderNum',
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
    {
      name: 'workOrderStatusDesc',
      label: intl.get(`${modelPrompt}.workOrderStatusDesc`).d('工单状态'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-workpiece-common/wip/ui`,
        method: 'GET',
      };
    },
  },
});

export { siteDS };
