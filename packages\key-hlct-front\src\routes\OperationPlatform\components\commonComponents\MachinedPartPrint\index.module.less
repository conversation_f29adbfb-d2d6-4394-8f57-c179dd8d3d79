.doneStepFlagModals{
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  // transform: translateX(-50%) translateY(-50%);
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.288);
}
.modalBox{
  width: 95%;
  // height: 80%;
  font-size: 1vw !important;
  background-color: rgba(56, 112, 143, 1);
  // border: 1px solid #63F2FF;
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  z-index: 10;
  .modalTitle{
    width: 100%;
    height: 45px;
    display: flex;
    align-items: center;
    background: linear-gradient(to right, rgba(99, 242, 255, 0.74) 0%, rgba(80, 234, 242, 0.55) 13.43%, rgba(48, 97, 219, 0.01) 100%);
    background-size: cover;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      margin-right: 4px;
    }
    .barCodeText{
      margin-left: 4px;
    }
    .modalRightFooter{
      div{
        display: inline-block;
        cursor: pointer;
        img{
          margin-left: 8px;
        }
      }
    }
    #modalRightFooter{
      :global{
        .c7n-pro-btn.c7n-pro-btn-default{
          background-color: rgba(17, 194, 207, 1) !important;
          color: #fff !important;
          border: none !important;
        }
        .c7n-pro-btn.c7n-pro-btn-default.c7n-pro-btn-disabled{
          background-color: #32617f !important;
          color: #fff !important;
          border: none !important;
        }
      }
    }
  }
  .modalContent{
    padding: 0;
    :global{
      .c7n-pro-table-pagination-with-selection-tips{
        padding: 0 16px 16px;
      }
    }
  }
  // 底部
  .modalFooter {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 8px;
  }

  :global{
    .c7n-pro-table-cell-fix-left, .c7n-pro-table-cell-fix-right{
      background-image: none !important;
    }
  }
  // radio
  // :global{
  //   .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper:not(.c7n-pro-radio-button) .c7n-pro-radio:checked + .c7n-pro-radio-inner{
  //     border-color: #33F1FF !important;
  //     background-color: #fff !important;
  //   }
  //   .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper:not(.c7n-pro-radio-button) .c7n-pro-radio-label{
  //     color: #fff !important;
  //   }
  // }
}
