// 加工件（工单）DS
import intl from 'utils/intl';
import { FieldType, FieldIgnore, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.ProcessWorkorderMachinedPart';
const tenantId = getCurrentOrganizationId();

const searchDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  paging: false,
  fields: [
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('箱码'),
    },
  ],
});

const detailDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  paging: false,
  fields: [
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('箱码'),
    },
    {
      name: 'containerTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerType`).d('容器类型'),
    },
    {
      name: 'capacitySumLoadQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.capacitySumLoadQty`).d('已装数量/容量'),
    },
    {
      name: 'capacityQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.capacityQty`).d('容量'),
    },
    {
      name: 'sumLoadQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumLoadQty`).d('已装载数量'),
    },
    {
      name: 'sumLoadBarcodeQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.sumLoadBarcodeQty`).d('条码个数'),
    },
    {
      name: 'printTimes',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位'),
    },
    {
      name: 'packingLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packingLevel`).d('装载类型'),
    },
  ],
});

const createDS: (enterInfo) => DataSetProps = (enterInfo) => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  paging: false,
  fields: [
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('箱码'),
    },
    {
      name: 'containerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerName`).d('箱码描述'),
    },
    // {
    //   name: 'locatorLov',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
    //   lovCode: 'HME.ORGANIZATION_LIMIT_LOCATOR',
    //   required: true,
    //   dynamicProps: {
    //     lovPara() {
    //       return {
    //         tenantId,
    //         workcellId: enterInfo.workStationId,
    //         siteId: enterInfo.siteId,
    //         organizationId: enterInfo.productionLineId,
    //       };
    //     },
    //   },
    //   ignore: FieldIgnore.always,
    // },
    {
      name: 'locatorId',
      // bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorCode',
      required: true,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位'),
    },
    {
      name: 'containerTypeId',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.containerType`).d('容器类型'),
    },
    {
      name: 'capacityQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.capacityQty`).d('容量'),
      required: true,
    },
    {
      name: 'packingLevel',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.packingLevel`).d('装载对象'),
    },
    {
      name: 'containerClassification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerClassification`).d('容器分类'),
      lookupCode: 'MT.CONTAINER_CLASSIFICATION',
    },
    {
      name: 'printFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.printFlag`).d('是否打印'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
  ],
});

const queryDS: (enterInfo) => DataSetProps = (enterInfo) => ({
  autoQuery: true,
  selection: false,
  fields: [
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
    },
    {
      name: 'containerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerName`).d('容器描述'),
    },
    {
      name: 'containerTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeCode`).d('容器类型'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位'),
    },
    {
      name: 'statusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.statusDesc`).d('状态'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('创建人'),
    },
    {
      name: 'creationDate',
      type: FieldType.dateTime,
      label: intl.get(`${modelPrompt}.creationDate`).d('创建时间'),
    },
    {
      name: 'emptyContainerFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.emptyContainerFlag`).d('是否空容器'),
    },
  ],
  queryFields: [
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('容器编码'),
    },
    {
      name: 'containerName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerName`).d('容器描述'),
    },
    {
      name: 'containerTypeLov',
      type: FieldType.object,
      label: intl.get(`${modelPrompt}.containerType`).d('容器类型'),
      lovCode: 'MT.CONTAINER_TYPE_CODE',
      dynamicProps: {
        lovPara() {
          return {
            tenantId,
          };
        },
      },
      ignore: FieldIgnore.always,
    },
    {
      name: 'containerTypeId',
      bind: 'containerTypeLov.containerTypeId',
    },
    {
      name: 'containerTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeCode`).d('容器类型'),
    },
    // {
    //   name: 'locatorLov',
    //   type: FieldType.object,
    //   label: intl.get(`${modelPrompt}.locatorName`).d('库位'),
    //   lovCode: 'HME.ORGANIZATION_LIMIT_LOCATOR',
    //   dynamicProps: {
    //     lovPara() {
    //       return {
    //         tenantId,
    //         workcellId: enterInfo.workStationId,
    //         siteId: enterInfo.siteId,
    //         organizationId: enterInfo.productionLineId,
    //       };
    //     },
    //   },
    //   ignore: FieldIgnore.always,
    // },
    {
      name: 'locatorId',
      // bind: 'locatorLov.locatorId',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位'),
    },
    {
      name: 'emptyContainerFlag',
      type: FieldType.boolean,
      label: intl.get(`${modelPrompt}.emptyContainerFlag`).d('是否空容器'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
  ],

  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-encasement/container/lov/ui`,
        method: 'GET',
      };
    },
  },
});

const containerTypeDS: () => DataSetProps = () => ({
  selection: DataSetSelection.single,
  autoQuery: false,
  autoCreate: false,
  paging: true,
  dataKey: 'content',
  queryFields: [
    {
      name: 'containerTypeCode',
      labelWidth: 150,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeCode`).d('容器类型编码'),
    },
    {
      name: 'containerTypeDescription',
      labelWidth: 150,
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeDescription`).d('容器类型描述'),
    }
  ],
  fields: [
    {
      name: 'containerTypeId',
    },
    {
      name: 'containerTypeCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeCode`).d('容器类型编码'),
    },
    {
      name: 'containerTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerTypeDescription`).d('容器类型描述'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/mt-container-type/lov/ui`,
        method: 'GET',
      };
    }
  },
});

const locatorDS: () => DataSetProps = () => ({
  selection: DataSetSelection.single,
  autoQuery: false,
  autoCreate: false,
  paging: true,
  dataKey: 'content',
  queryFields: [
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('货位名称'),
    }
  ],
  fields: [
    {
      name: 'locatorId',
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorCode`).d('货位编码'),
    },
    {
      name: 'locatorName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('货位名称'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-container-encasement/locator/lov/ui`,
        method: 'POST',
      };
    }
  },
});

export { detailDS, createDS, searchDS, queryDS, containerTypeDS, locatorDS };
