/**
 * @Description: 卡片布局
 * @Author: <<EMAIL>>
 * @Date: 2023-07-07 14:21:49
 * @LastEditTime: 2023-07-26 14:49:36
 * @LastEditors: <<EMAIL>>
 */
import React, { ReactNode, CSSProperties } from 'react';
import { Tooltip, Icon, Spin } from 'choerodon-ui/pro';
import cardSvg from '@/assets/icons/operation.svg';
import styles from './index.module.less';

interface LayoutProps {
  children: ReactNode,
  spinning?: boolean, // 卡片loading状态
  className?: string,
  style?: CSSProperties,
}
const Layout = (props: LayoutProps) => {
  const { spinning = false, children, className, style } = props;

  return (
    <div style={style} className={`${styles.cardLayout} ${className}`}>
      <Spin spinning={spinning}>
        {children}
      </Spin>
    </div>
  )
}

interface HeaderProps {
  title: ReactNode,
  help?: string,
  content?: ReactNode,
  addonAfter?: ReactNode,
  className?: string,
  style?: CSSProperties,
}
const Header = (props: HeaderProps) => {
  const {
    title = '',
    help,
    content,
    addonAfter,
    className,
    style,
  } = props;

  return (
    <div style={style} className={`${styles.cardHeader} ${className}`}>
      <div className={styles.headerTitle}>
        <img src={cardSvg} alt='' className={styles.titleIcon} />
        <div>
          {title}
        </div>
        {
          help && (
            <Tooltip title={help} theme="light">
              <Icon type="help" className={styles.helpIcon} />
            </Tooltip>
          )}
      </div>
      <div className={styles.headerContent}>
        {content}
      </div>
      {addonAfter && (
        <div className={styles.headerAction}>
          {addonAfter}
        </div>
      )}
    </div>
  )
}

interface ContentProps {
  children: ReactNode,
  className?: string,
  style?: CSSProperties,
}
const Content = (props: ContentProps) => {
  const { children, className, style } = props;

  return (
    <div style={style} className={`${styles.cardContent} ${className}`}>
      {children}
    </div>
  )
}
const CardLayout = {
  Layout,
  Header,
  Content,
}

export default CardLayout;
