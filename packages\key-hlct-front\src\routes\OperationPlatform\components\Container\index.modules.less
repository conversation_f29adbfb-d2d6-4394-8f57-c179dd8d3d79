.container {
  :global {
    .c7n-divider.c7n-divider-horizontal {
      margin: 0 0 !important;
    }

    .c7n-card-body {
      padding: 0 !important;
      background: #395470;
    }

    .c7n-pro-modal.c7n-pro-modal .c7n-pro-modal-header {
      background-color: #3c87ad !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-button .c7n-pro-checkbox:disabled + i.c7n-pro-checkbox-inner + .c7n-pro-checkbox-label {
      color: #fff !important;
    }
    .c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper .c7n-pro-switch:checked + .c7n-pro-switch-label::after {
      background-color: rgb(0, 212, 205) !important;
    }
    .icon-refresh{
      background-color: rgb(56, 112, 143) !important;
    }
  }
}

.titleMater {
  flex-grow: 0;
  flex-shrink: 0;
  color: white;
  background: #45acf140;
  margin: 0.08rem;
  width: 60px;
  /* text-align: center; */
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  flex-direction: column;

  .Alternative {
    background: #ffffff40;
    border-radius: 10px;
    padding: 3px;
  }
}

.hzero-draggable-card-content {
  background: #38708f !important;
}

.numberModal {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .icon-close {
      color: white !important;
      top: 0 !important;
    }
  }
}

.modalForm {
  :global {
    .c7n-pro-field-label {
      color: #1cdbef !important;
    }

    .c7n-pro-output {
      color: white !important;
    }

    .c7n-pro-select-wrapper {
      background: #50819c !important;
      color: white !important;
    }
    .c7n-pro-output-wrapper{
      color: white !important;
    }
    .c7n-pro-input {
      color: white !important;
      // border-color: #50819c !important;
    }
    .c7n-pro-input-wrapper {
      color: white !important;
      background: #50819c !important;
    }
    .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper{
      font-size: unset !important;
    }
    .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper label .c7n-pro-input-number{
     // border: 1px solid rgba(255, 255, 255, 0.5) !important;
      background: #50819c !important;
      color: #fff !important;
      font-size: unset !important;
    }

    .c7n-pro-radio-wrapper {
      color: white !important;
      background: #50819c !important;
    }
    .c7n-pro-radio-inner {
      color: white !important;
      background: #50819c !important;
      border-color: white !important;
      &::after {
        color: #00d4cd !important;
      }
    }
    .c7n-pro-radio-label {
      color: white !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-radio:checked + .c7n-pro-radio-inner {
      color: #00d4cd !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper.c7n-pro-radio-button:not(.c7n-pro-table-customization-select-view-option)
      .c7n-pro-radio:checked
      + .c7n-pro-radio-inner {
      color: #00d4cd !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-button .c7n-pro-checkbox-label {
      background: #50819c !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper{
      color: #fff !important;
      margin-right: 16px !important;
      background: #50819c !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper:not(.c7n-pro-checkbox-button) .c7n-pro-checkbox:checked + .c7n-pro-checkbox-inner{
      color: #00d4cd !important;
      background: #50819c !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper.c7n-pro-checkbox-button .c7n-pro-checkbox:checked:disabled + i + .c7n-pro-checkbox-label {
      color: #fff !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper.c7n-pro-checkbox-button .c7n-pro-checkbox:checked:disabled + i + .c7n-pro-checkbox-label::after {
      color: #00d4cd !important;
    }

    .c7n-pro-checkbox-inner{
      background-color: #50819c !important;
      border-color: #00d4cd !important;
    }
  }
}

.MachineMaterialsModal {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }
  }

  .icon-refresh {
    background-color: rgb(91, 136, 160) !important;
    color: white !important;
  }

  .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-cell {
    background-color: #3c87ad !important;
    color: white !important;
    border: none !important;
  }

  .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
    background-color: #3c87ad !important;
    color: white !important;
  }

  .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
    background-color: #3c87ad !important;
    color: white !important;
  }

  .c7n-pro-btn-wrapper {
    background-color: #3c87ad !important;
    color: white !important;
  }

  .icon {
    color: white !important;
  }

  .c7n-pro-pagination-perpage {
    color: white !important;
  }

  .c7n-pro-pagination-page-info {
    color: white !important;
  }
}

.laneTitle {
  display: inline-flex;
  width: 100%;

  .materialInfo {
    color: white;
    margin-left: 10px;
    flex-grow: 1;
    width: 50%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .numberInfo {
    .materialInfo;
    text-align: right;
    margin-left: 0;
  }
}

    .c7nProModalheader {
      background-color: #3c87ad !important;
      padding: 4px 16px 8px !important;
      display: flex;
    }
    .c7nProModalTitle {
      color: white !important;
      margin-left: 8px;
    }

    .orangeButton {
      background-color: #f79901 !important;
    }

    .none {
      display: none !important;
    }
    .settingsIcon {
      margin-left: 10px;
      color: rgb(0, 212, 205) !important;
      cursor: pointer ;
    }

.workOrderTextModals{
  :global{
    .c7n-pro-modal-header {
      display: inline-flex;
      align-items: center;
      height: 42px;
      overflow: hidden;
      width: 100%;
      padding: 0 8px !important;
      background: linear-gradient(
        172.09deg,
        rgba(99, 242, 255, 0.74) 0%,
        rgba(80, 234, 242, 0.55) 23.43%,
        rgba(75, 214, 239, 0.47) 34.52%,
        rgba(48, 97, 219, 0.01) 100%
      );
      .titleIcon {
        margin-right: 8px;
      }
      .c7n-pro-modal-title {
        display: inline-flex;
        align-items: center;
        // font-size: 16px;
        color: white;
      }
    }
    .c7n-pro-modal-body {
      min-height: 400px !important;
      max-height: 550px !important;
      padding: 0 !important;
      .c7n-pro-table-professional-query-bar-button {
        margin-right: 16px;
      }
      .c7n-pro-table-pagination{
        margin-right: 16px;
      }
    }
  }
}
