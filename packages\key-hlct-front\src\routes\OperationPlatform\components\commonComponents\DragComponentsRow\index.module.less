/**
 * @Description: 寻址策略样式表
 * @Author: <<EMAIL>>
 * @Date: 2021-09-08 13:38:56
 * @LastEditTime: 2021-09-26 19:57:12
 * @LastEditors: <<EMAIL>>
 */
.strategy-self-form {
  :global {
    .c7n-pro-field-label {
      padding-bottom: 0 !important;
    }

    .c7n-pro-field-wrapper {
      padding-top: 2px !important;
      padding-bottom: 0 !important;

      .c7n-slider.c7n-pro-field {
        margin-bottom: 0 !important;
      }
    }
  }
}

.strategy-select-box {
  padding-left: 20px;

  :global {
    .c7n-pro-checkbox-wrapper {
      margin-right: 30px;
    }
  }
}

.menu-box {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.row-drag-item,
.row-dragging-item {
  // width: 40%;
  display: flex;
  // padding: 8px 12px;
  // margin-bottom: 16px;
  margin-right: 10px;
  // background-color: #f8f8f8;
  cursor: move;
}

.row-dragging-item {
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
}

// .row-drag-item:hover,
// .row-dragging-item:hover {
//   color: #29bece;
// }

.row-drag-item-left,
.col-drag-item-left {
  display: flex;
  justify-content: left;
  align-items: center;
  :global{
    .c7n-card.c7n-card.c7n-card.c7n-card{
      margin: 3px !important;
    }
    .c7n-card.c7n-card.c7n-card.c7n-card.c7n-card-bordered{
      border: none !important;
    }
    .c7n-card.c7n-card.c7n-card.c7n-card.c7n-card-bordered.c7n-card-selected{
      border: 1px solid #29BECE !important;
    }
    .c7n-card.c7n-card.c7n-card.c7n-card div:nth-last-child(1):nth-child(1).c7n-card-body{
      border-radius: 5px !important;
    }
    .c7n-card-selected-bottomRight::after{
      left: -1px !important;
      top: 0 !important;
      right: auto !important;
      bottom: auto !important;
      border-bottom-color: transparent !important;
      border-top-style: solid !important;
      border-right-color: transparent !important;
      border-right-style: solid !important;
      border-top-color: #29BECE !important;
      border-top-style: dashed !important;
      border-left-color: #29BECE !important;
      border-left-style: dashed !important;
      border-top-left-radius: 5px !important;
    }
  }
}

.row-drag-item-right,
.col-drag-item-right {
  display: flex;
  justify-content: left;
  align-items: center;
}

.col-drag-container {
  display: flex;
}

.col-drag-item,
.col-dragging-item {
  margin-right: 12px;
  display: flex;
  padding: 0 10px;
  cursor: move;
  border-radius: 4px;
}

.col-drag-item {
  box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.25);
}

.col-dragging-item {
  box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
}

.col-drag-item-icon-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.col-drag-item-icon {
  position: relative;
  margin-left: 6px;
  margin-right: 6px;
}

.col-drag-item-icon:hover {
  cursor: pointer;
}

.row-drag-container {
  position: relative;
  display: flex;
  flex-wrap: wrap;
}

.row-drag-container-mask {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.row-drag-container-mask:hover {
  cursor: not-allowed;
}

.icon-up {
  padding-left: 2px;
  font-size: 20px;
  color: #666;
  transform: rotate(-90deg);
}

.icon-down {
  padding-right: 6px;
  font-size: 20px;
  color: #666;
  transform: rotate(90deg);
}

.select-box-address {
  :global {
    .c7n-pro-radio-label {
      padding-left: 10px !important;
      padding-right: 10px !important;
    }

    .c7n-pro-radio-disabled.c7n-pro-radio-button
      .c7n-pro-radio:checked
      + .c7n-pro-radio-inner
      + span {
      color: #333;
    }

    .c7n-pro-radio-disabled.c7n-pro-radio-button .c7n-pro-radio + .c7n-pro-radio-inner + span {
      color: rgba(0, 0, 0, 0.25);
    }

    .c7n-pro-select-box
      .c7n-pro-radio-disabled.c7n-pro-radio-button:last-child
      .c7n-pro-radio-inner,
    .c7n-pro-select-box .c7n-pro-radio-button:last-child {
      border-color: rgba(0, 0, 0, 0.25);
    }

    .c7n-pro-select-box
      .c7n-pro-checkbox-disabled.c7n-pro-checkbox-button
      .c7n-pro-checkbox:checked
      + .c7n-pro-checkbox-inner
      + span {
      color: #333;
    }

    .c7n-pro-checkbox:disabled + .c7n-pro-checkbox-inner + span {
      color: rgba(0, 0, 0, 0.25);
    }
  }
}

.icon-help-icon {
  margin-left: 8px;
  position: relative;
  top: -2px;
  color: rgba(0, 0, 0, 0.3);
}

:global {
  .strategy-col-drag-item {
    margin-right: 12px;
    display: flex;
    padding: 0 10px;
    cursor: move;
    border-radius: 4px;
  }

  .strategy-col-drag-item-height,
  .strategy-col-dragging-item-height {
    margin-right: 12px;
    display: flex;
    padding: 0 10px;
    cursor: move;
    border-radius: 4px;
  }

  .strategy-col-drag-item-height {
    box-shadow: 1px 1px 2px rgba(0, 0, 0, 0.25);
  }

  .strategy-col-dragging-item-height {
    box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.25);
  }

  .strategy-col-drag-item-height.strategy-col-drag-item-height-height,
  .strategy-col-dragging-item-height.strategy-col-dragging-item-height-height {
    background-color: #fff;
  }
}
