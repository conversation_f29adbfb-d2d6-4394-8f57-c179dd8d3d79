/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-17 13:43:36
 * @LastEditTime: 2023-07-17 14:11:07
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 获取数据采集列表
 * @function FetchDataCollection
 * @returns {object} fetch Promise
 */
export function FetchDataCollectionList(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-inspection-card/tag-data/query/ui`,
    method: 'POST',
  };
}

/**
 * 获取数据采项数据
 * @function FetchDataCollection
 * @returns {object} fetch Promise
 */
export function SaveDataCollectionList(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-inspection-card/tag-data/save/ui`,
    method: 'POST',
  };
}

/**
 * 确定当条的数据自互检结果
 * @function FetchDataCollection
 * @returns {object} fetch Promise
 */
export function SaveDataCollectionAll(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-inspection-card/nc-record/query/ui`,
    method: 'GET',
  };
}
