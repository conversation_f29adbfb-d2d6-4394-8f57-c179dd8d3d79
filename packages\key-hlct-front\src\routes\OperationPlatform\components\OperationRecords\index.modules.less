.OperationRecordsTimeline {
  padding: 16px;
  // border: 1px solid white;
  height: 100%;
  overflow: auto;
  font-size: 16px;

  :global {
    .c7n-timeline.c7n-timeline .c7n-timeline-item-last .c7n-timeline-item-content {
      color: white;
    }

    .c7n-timeline-item-pending .c7n-timeline-item-tail {
      display: block;
      border-left: 0.02rem dotted #e8e8e8;
    }

    .c7n-timeline-item-pending .c7n-timeline-item-head {
      background: #387090;
    }

    .c7n-timeline-item-last {
      padding: 0;

      .c7n-timeline-item-tail {
        display: none !important;
      }
    }

    .c7n-timeline-item-content {
      color: white !important;
    }

    .c7n-timeline-item{
      padding: 0 !important;
    }
    .c7n-progress-loading.c7n-progress-loading.c7n-progress-status-normal .c7n-progress-inner circle {
      stroke: white;
    }
  }

  // margin-left: 10px;
  // margin-right: 10px;
  // border: 1px solid white;
  // padding: 10px;
  // z-index: 10;
  // height: calc(100% - 40px);
  // overflow-y: scroll;

  // white-space: nowrap;
  // overflow: hidden;
  // text-overflow: ellipsis;
  // display: inline-block;
  // width: 100%;
  // color: white;
  // padding-right: 10px;
}
