/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-08-15 15:18:17
 * @LastEditTime: 2023-09-04 20:16:57
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Icon, Tooltip } from 'choerodon-ui/pro';
import PDF from 'react-pdf-js';
import intl from 'utils/intl';
import pdfjsWorker from "pdfjs-dist-2.1.266/build/pdf.worker.entry";
import { getCurrentOrganizationId, getAccessToken } from 'utils/utils';
import { HZERO_FILE } from 'utils/config';
import { CardLayout, ONotification, useRequest } from '../commonComponents';
import { useOperationPlatform } from '../../contextsStore';
import { GetSopEnclosure, SopEnclosureType, GetFileSignedUrl } from './services';
import styles from './index.module.less';

const fileExts = ['.xlsx', '.xls', '.pdf', '.doc', '.docx', '.pptx', '.ppt', '.txt'];
const imgExts = ['.jpg', '.jpeg', '.bmp', '.webp', '.png', '.gif', '.svg'];
const videoExts = ['.wmv', '.mpg', '.mpeg', '.mov', '.rm', '.swf', '.flv', '.mp4'];

const extsMap = new Map([
  ['file', fileExts],
  ['img', imgExts],
  ['video', videoExts],
])

const modelPrompt = 'tarzan.operationPlatform';

const InstructionsCard = (props) => {
  const { enterInfo, workOrderData } = useOperationPlatform();

  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState<SopEnclosureType[]>([]);
  const [selectedFile, setSelectedFile] = useState<SopEnclosureType | null>(null);
  // const [realFileUrl, setRealFileUrl] = useState('');
  const [pages, setPages] = useState({ page: 1, allPages: 1 })
  const { run: getSopEnclosure } = useRequest(GetSopEnclosure(), { manual: true, needPromise: true, showNotification: false });
  // const { run: fetchAttachmentFiles } = useRequest(FetchAttachmentFiles(''), { manual: true, needPromise: true });
  const { run: getFileSignedUrl, loading: getFileSignedUrlLoading } = useRequest(GetFileSignedUrl(), { manual: true, needPromise: true });
  // const { run: previewFile } = useRequest(PreviewFile(), { manual: true, needPromise: true });


  useEffect(() => {
    if (!workOrderData?.eoId) {
      return;
    }
    setLoading(true);
    getSopEnclosure({
      params: {
        workcellId: enterInfo?.workStationId,
        operationId: enterInfo?.selectOperation?.operationId,
        eoId: workOrderData?.eoId,
      },
    }).then(res => {
      if (!res?.success) {
        return;
      }
      const _fileList: SopEnclosureType[] = res.rows || [];
      setFileList(_fileList);
      if (_fileList.length) {
        setSelectedFile(_fileList[0])
        setLoading(getFileType(_fileList[0]) === 'file')
      } else {
        setLoading(false);
      }
    })
  }, [workOrderData?.eoId])

  const handleSelectFile = useCallback(
    (file: SopEnclosureType) => {
      setSelectedFile(file)
      setPages({ page: 1, allPages: 1 });
      if (getFileType(file) === 'file') {
        setLoading(true)
      }
    },
    [],
  )

  const getAllPages = useCallback(
    (pageNums) => {
      if (pageNums) {
        setPages((prev) => ({ ...prev, allPages: pageNums }))
        setLoading(false)
      }
    },
    [],
  )

  const getFileType = useCallback(
    (file: SopEnclosureType) => {
      let fileType = '';
      const splitFileName = file.fileName.split('.');
      const suffix = splitFileName[splitFileName.length - 1];
      extsMap.forEach((value, key) => {
        if (value.includes(`.${suffix}`)) {
          fileType = key;
        }
      })
      return fileType;
    },
    [],
  )

  const nextPage = useCallback(
    (type) => {
      const currentPage = pages.page;
      if (type === 'next' && currentPage !== pages.allPages) {
        setPages((prev) => ({ ...prev, page: currentPage + 1 }))
      }
      if (type === 'up' && currentPage !== 1) {
        setPages((prev) => ({ ...prev, page: currentPage - 1 }))
      }
    },
    [pages],
  )

  const priviewDiv = useMemo(() => {
    if (!selectedFile || !selectedFile.fileUrl) {
      return <></>;
    }
    switch (getFileType(selectedFile)) {
      case 'file':
        return (
          <PDF
            file={`${HZERO_FILE}/v1/${getCurrentOrganizationId()}/file-preview/by-url?url=${encodeURIComponent(
              selectedFile?.fileUrl,
            )}&bucketName=key-hmes&storageCode=DEFAULT&access_token=${getAccessToken()}`}
            onDocumentComplete={getAllPages}
            page={pages.page}
            scale={1}
            workerSrc={pdfjsWorker}
          />
        );
      case 'img':
        return <img src={selectedFile.fileUrl} alt=''></img>
      case 'video':
        return <video src={selectedFile.fileUrl} muted autoPlay loop controls />
      default:
        return <div style={{ color: 'white' }}>{intl.get(`${modelPrompt}.not.currently.supported`).d('暂不支持')}</div>
    }
  }, [selectedFile, pages])

  const handleDownload = useCallback(
    () => {
      if (!selectedFile?.fileUrl) {
        return;
      }
      getFileSignedUrl({
        params: {
          bucketName: 'key-hmes',
          url: selectedFile.fileUrl,
        },
      }).then(res => {
        if (res && !res?.failed) {
          const elink = document.createElement('a');
          elink.style.display = 'none';
          elink.href = res;
          document.body.appendChild(elink);
          elink.click();
          document.body.removeChild(elink);
        } else {
          ONotification.error({ message: res.message })
        }
      })
    },
    [selectedFile],
  )

  const handlePriview = useCallback(
    () => {
      if (!selectedFile?.fileUrl) {
        return;
      }
      getFileSignedUrl({
        params: {
          bucketName: 'key-hmes',
          url: selectedFile.fileUrl,
          download: 0, // 是否是下载 0-不下载，不传则为下载
        },
      }).then(res => {
        if (res && !res?.failed) {
          const elink = document.createElement('a');
          elink.style.display = 'none';
          elink.href = res;
          elink.target = '_blank';
          document.body.appendChild(elink);
          elink.click();
          document.body.removeChild(elink);
        } else {
          ONotification.error({ message: res.message })
        }
      })
    },
    [selectedFile],
  )

  return (
    <CardLayout.Layout spinning={loading || getFileSignedUrlLoading}>
      <CardLayout.Header
        className='InstructionsCardHead'
        title={intl.get(`${modelPrompt}.instructionsCard.preview`).d('作业指导书-预览')}
        help={props?.cardUsage?.remark}
        addonAfter={
          <div className={styles.headerAfter}>
            {pages?.allPages > 1 && <div
              className={styles.pageChange}
            >
              <div style={{ cursor: 'pointer' }} onClick={() => nextPage('up')}>{'<-'}</div>
              &nbsp;
              <span>
                {pages.page}/{pages.allPages}{intl.get(`${modelPrompt}.page`).d('页')}
              </span>
              &nbsp;
              <div style={{ cursor: 'pointer' }} onClick={() => nextPage('next')}>{'->'}</div>
            </div>}
            <div className={styles.actionIconDiv}>
              <Icon type="zoom_out_map-o" onClick={handlePriview} />
              <Icon type="file_download_black-o" onClick={handleDownload} />
            </div>
          </div>
        }
      />
      <CardLayout.Content className={`${styles.InstructionsContent} InstructionsCardForm`}>
        <div className={styles.leftSider}>
          {
            fileList.map((file: SopEnclosureType) => {
              return (
                <Tooltip title={file.fileName}>
                  <div
                    key={file.sopFileId}
                    className={`${styles.fileLine} ${selectedFile?.sopFileId === file.sopFileId ? `${styles.fileLine_active}` : ''}`}
                    onClick={() => { handleSelectFile(file) }}
                  >
                    {file.fileName}
                  </div>
                </Tooltip>
              )
            })
          }
        </div>
        <div className={styles.rightSider}>
          {priviewDiv}
        </div>
      </CardLayout.Content>
    </CardLayout.Layout>
  )
}

export default InstructionsCard;
