.workOrderModals {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .icon-refresh {
      background-color: rgb(56, 112, 143) !important;
      color: white !important;
    }

    .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      border: none !important;
    }

    .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
      // background-color: #3c87ad !important;
      color: white !important;
    }

    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .c7n-pro-btn-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .icon {
      color: white !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
    }
  }
}

#processReportingRecords{
  :global{
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-disabled:not(.c7n-pro-checkbox-button) .c7n-pro-checkbox:checked + .c7n-pro-checkbox-inner{
      background-color: #608da5 !important;
      border-color: transparent !important;
      border-radius: 50% !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper:not(.c7n-pro-checkbox-button) .c7n-pro-checkbox:checked + .c7n-pro-checkbox-inner{
      border-radius: 50% !important;
    }
  }
}
