.workOrderModals {
  .c7n-pro-table-wrapper.c7n-pro-table-wrapper
    .c7n-pro-table
    .c7n-pro-table-content
    .c7n-pro-table-row {
    height: 45px !important;
  }
}

.machinedPartModal {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }
  }
}

// .workOrderModals {
//   :global {
//     .c7n-pro-modal-header {
//       background-color: #3c87ad !important;
//       padding: 8px 16px 8px !important;
//     }

//     .c7n-pro-modal-title {
//       color: white !important;
//     }

//     .icon-refresh {
//       background-color: rgb(91, 136, 160) !important;
//       color: white !important;
//     }

//     .c7n-pro-modal-content
//       .c7n-pro-modal-body
//       .c7n-spin-nested-loading
//       .c7n-spin-container
//       .c7n-pro-table-content
//       .c7n-pro-table-thead
//       .c7n-pro-table-cell {
//       background-color: #3c87ad !important;
//       color: white !important;
//       border: none !important;
//       // font-size: 17px !important;
//     }

//     .c7n-pro-modal-content
//       .c7n-pro-modal-body
//       .c7n-spin-nested-loading
//       .c7n-spin-container
//       .c7n-pro-table-content
//       .c7n-pro-table-tbody
//       .c7n-pro-table-cell {
//       // background-color: #3c87ad !important;
//       color: white !important;
//       // font-size: 17px !important;
//     }

//     .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
//       background-color: #3c87ad !important;
//       color: white !important;
//     }

//     .c7n-pro-btn-wrapper {
//       background-color: #3c87ad !important;
//       color: white !important;
//     }

//     .icon {
//       color: white !important;
//     }

//     .c7n-pro-pagination-perpage {
//       color: white !important;
//     }

//     .c7n-pro-pagination-page-info {
//       color: white !important;
//     }
//   }
// }

.workOrderModals{
  :global{
    .c7n-pro-modal-header {
      display: inline-flex;
      align-items: center;
      height: 42px;
      overflow: hidden;
      width: 100%;
      padding: 0 8px !important;
      background: linear-gradient(
        172.09deg,
        rgba(99, 242, 255, 0.74) 0%,
        rgba(80, 234, 242, 0.55) 23.43%,
        rgba(75, 214, 239, 0.47) 34.52%,
        rgba(48, 97, 219, 0.01) 100%
      );
      .titleIcon {
        margin-right: 8px;
      }
      .c7n-pro-modal-title {
        display: inline-flex;
        align-items: center;
        font-size: 16px;
        color: white;
      }
    }
    .c7n-pro-modal-body {
      min-height: 400px !important;
      max-height: 550px !important;
      padding: 0 !important;
      .c7n-pro-table-professional-query-bar-button {
        margin-right: 16px;
      }
      .c7n-pro-table-pagination{
        margin-right: 16px;
      }
    }
  }
}

.modalForm {
  :global {
    .c7n-pro-field-label {
      color: white !important;
    }

    .c7n-pro-select-wrapper {
      background: #50819c !important;
      color: white !important;
    }

    .c7n-pro-select {
      color: white !important;
    }
  }
}

.customTitle {
  width: 100%;
  // font-size: 20px;
  background: rgba(42, 99, 130, 1);
  height: 36px;
  line-height: 36px;
}

.cardFooter{
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 16px;
  .completeText{
    display: flex;
    align-items: center;
    color: rgba(51, 241, 255, 0.85);
    // font-size: 16px;
    font-family: auto;
    min-width: 130px;
    text-align: right;
    .completeValue{
      color: #fff;
      // font-size: 26px;
      font-weight: 800;
      padding-left: 8px;
    }
    .completeValueAdd{
      color: #fff;
      // font-size: 16px;
      padding-left: 8px;
    }
  }
  .printButton{
    width: 50%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 8px;
    color: #33F1FF;
    .buttonContent{
      width: 50%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      img {
        margin-right: 4px;
      }
      div{
        cursor: pointer;
      }
    }
  }
}
