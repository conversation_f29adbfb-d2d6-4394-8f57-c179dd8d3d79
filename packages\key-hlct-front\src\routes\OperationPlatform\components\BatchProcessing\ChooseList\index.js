import React, {useEffect, useState} from 'react';
import styles from '../index.module.less';
import CountdownRender from './CountdownRender';

const Index = ({cardsList, selectedIds, handleSelectCard, queryContainerDetail}) => {



  return (
    <div className={styles.tabContent}>
      {
        cardsList.length ? cardsList.map(item => {
          return (
            <CountdownRender selectedIds={selectedIds} handleSelectCard={handleSelectCard} queryContainerDetail={queryContainerDetail} item={item} styleClass={styles.topLeft}/>
          )
        }) : null
      }

    </div>

  )
}

export default Index;
