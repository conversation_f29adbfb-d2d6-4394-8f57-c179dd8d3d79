.operationPlatformSetting {
    :global {
      .c7n-pro-modal-header {
        // background-color: rgba(55, 150, 0, 0) !important;
        background: linear-gradient(179.62deg, rgba(99, 242, 255, 0.74) 0%, rgba(80, 234, 242, 0.55) 23.43%, rgba(48, 97, 219, 0.01) 100%);
        padding: 8px 16px 8px !important;
      }
      .c7n-pro-modal-title {
        color: white !important;
        .selectModalTitle{
          display: flex;
          align-items: center;
        }
      }
      .icon-close {
        color: white !important;
        top: 0 !important;
      }
      .tabBarAllSection .c7n-tabs-bar .c7n-tabs-bar-inner .c7n-tabs-nav-container .c7n-tabs-nav-wrap{
        background: rgba(56, 112, 143, 1) !important;
      }
    }
    .c7n-pro-modal-content .c7n-pro-modal-body .c7n-tabs .c7n-tabs-nav-container .c7n-tabs-nav-wrap .c7n-tabs-nav-scroll{
      background: #3c87ad !important;
    }
    .c7n-tabs-nav-wrap{
      background: none !important;
      .c7n-tabs-nav-scroll{
        background: #3c87ad !important;
      }
    }

  }

.returnButton{
  position: fixed;
  bottom: 5px;
  right: 10px;
  z-index: 9999999;
  width: 60px;
  height: 60px;
  border-radius: 100%;
  font-size: 12px;
  background: #11c2cf;
  color: #fff;
  box-shadow: 5px 8px 12px rgba(0, 0, 0, 0.16);
  margin-bottom: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  text-align: center;
  padding: 5px;
}

#headerButton{
  display: flex;
  flex-flow: row-reverse;
  .c7n-pro-btn.c7n-pro-btn{
    font-size: 1vw !important;
  }
}
