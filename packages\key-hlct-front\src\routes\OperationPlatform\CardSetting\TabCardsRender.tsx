import React, { useMemo } from 'react';
import styles from './index.module.less';

const TabCardsRender = ({ tabType, cardsList, selectedCardIds, primaryCardId, handleSelectCard, tempCardMode }) => {
  const tabCards = useMemo(() => {
    if (tabType === '全部') {
      return cardsList
    }
    return cardsList.filter((item) => item.cardType === tabType)
  }, [tabType, cardsList])
  return (
    <div className={styles.tabContent}>
      {
        tabCards.map(item => {
          let _className;
          const _cardId = item.cardCode
          if (selectedCardIds.includes(_cardId)) {
            _className = styles.previewImgCardSelected
          } else {
            _className = styles.previewImgCard
          }
          return (
            <div
              className={_className}
              onClick={() => handleSelectCard(item)}
            >
              <img alt='' src={item.cardPreviewUrl} />
              <div className={styles.cardName}>
                {item.cardName}
              </div>
              {
                selectedCardIds.includes(_cardId) && (
                  <div className={styles.cardSelect}>
                    <div className={styles.squareOne}>
                      <div className={styles.squareTwo}></div>
                    </div>
                  </div>
                )
              }
              {
                tempCardMode === 'Independent' && primaryCardId === _cardId && (
                  <div className={styles.cardSelect2}>
                    <div className={styles.squareOne}>
                      <div className={styles.squareTwo2}></div>
                    </div>
                  </div>
                )
              }
            </div>
          )
        })
      }
    </div>
    // <Row>
    //   {item.length && item.map(listItem => {
    //     if (!listItem.mainChecked) {
    //       return (
    //         <Col span={5}>
    //           <div
    //             onClick={() => handleSelect(listItem, listItem?.checked)}
    //             style={{ border: !listItem?.checked ? 'none' : '2px solid rgba(75, 227, 238, 1)' }}
    //             className={styles.previewImgCard}
    //           >
    //             <div className={styles.previewImgDiv} style={{ background: `url(${listItem.cardPreviewUrl})` }}></div>
    //             <div className={styles.cardName}>
    //               {listItem.cardName}
    //               {
    //                 currentRecord.cardCode === listItem.cardCode && listItem.checked && '(当前选择)'
    //               }
    //             </div>
    //             {
    //               !!listItem?.checked && (
    //                 <div className={styles.cardSelect}>
    //                   <div className={styles.squareOne}>
    //                     <div className={styles.squareTwo}></div>
    //                   </div>
    //                 </div>
    //               )
    //             }
    //           </div>
    //           <br />
    //         </Col>
    //       );
    //     }
    //     return (
    //       <Col span={5}>
    //         <div
    //           onClick={() => handleSelect(listItem, listItem?.checked)}
    //           style={{ border: !listItem?.checked ? 'none' : '2px solid rgba(255, 216, 110, 1)' }}
    //           className={styles.previewImgCard}
    //         >
    //           <div className={styles.previewImgDiv} style={{ background: `url(${listItem.cardPreviewUrl})` }}></div>
    //           <div className={styles.cardName2}>
    //             {listItem.cardName}
    //             {
    //               currentRecord.cardCode === listItem.cardCode && '(主卡片)'
    //             }
    //           </div>
    //           {
    //             !!listItem?.checked && (
    //               <div className={styles.cardSelect2}>
    //                 <div className={styles.squareOne}>
    //                   <div className={styles.squareTwo2}></div>
    //                 </div>
    //               </div>
    //             )
    //           }
    //         </div>
    //         <br />
    //       </Col>
    //     );
    //   })}
    // </Row>
  )
}

export default TabCardsRender;
