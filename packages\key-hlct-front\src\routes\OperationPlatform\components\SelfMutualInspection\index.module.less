// :global {
//   .c7n-pro-attachment-list-wrapper .c7n-pro-attachment-list.c7n-pro-attachment-list.c7n-pro-attachment-list .c7n-pro-attachment-list-item.c7n-pro-attachment-list-item {
//     background: #4892bb;
//   }
// }

.dataQcquisition {

  :global {
    .c7n-pro-table {
      border-top: 0;

      .c7n-pro-table-content {
        background: #387090;
      }
    }

    .c7n-pro-table-toolbar{
      padding: 0 !important;
    }
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content table .c7n-pro-table-tfoot tr .c7n-pro-table-cell,
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content table .c7n-pro-table-thead tr .c7n-pro-table-cell {
      background: #387090 !important;
      border-color: #387090;
      color: white;
    }

    .c7n-pro-table.c7n-pro-table .c7n-pro-table-tbody .c7n-pro-table-row .c7n-pro-table-cell {
      background: #4892bb;
      color: white;

      &>span {
        border: none;
        height: auto !important;
      }

      .c7n-pro-table-cell-inner-invalid {
        background: #4892bb;
      }

      input {
        color: white;
        background: #4892bb;
        text-align: left;
      }
    }

    .c7n-pro-table-cell[data-index='tagValue'] {
      padding: 0 !important;

      span:first-child {
        padding: 0 !important;
      }
    }

    .c7n-pro-table-parity-row.c7n-pro-table-parity-row.c7n-pro-table-parity-row .c7n-pro-table-row:nth-of-type(2n)>.c7n-pro-table-cell {
      background: #387090;
      color: white;

      .c7n-pro-table-cell-inner-invalid {
        background: #387090;
      }

      input {
        color: white;
        background: #387090;
        text-align: left;
      }
    }

    .c7n-pro-table .c7n-pro-table-tbody .c7n-pro-table-row.c7n-pro-table-row-current .c7n-pro-table-cell {
      background: #03517e !important;

      .c7n-pro-table-cell-inner-invalid {
        background: #03517e !important;
      }
    }

    .c7n-pro-table:not(.c7n-pro-table-bordered) .c7n-pro-table-tbody {
      border-top: 0;
    }

    // .c7n-pro-input-wrapper.c7n-pro-input-wrapper label .c7n-pro-input {
    //   border: 1px solid rgb(232, 232, 232) !important;
    // }

    .c7n-pro-input-wrapper.c7n-pro-input-wrapper.c7n-pro-input-required-colors .c7n-pro-input {
      border-color: rgb(251, 173, 0) !important
    }

    .c7n-pro-input-wrapper.c7n-pro-input-wrapper.c7n-pro-input-invalid .c7n-pro-input {
      border-color: rgb(251, 173, 0) !important;
    }

    .c7n-pro-table:not(.c7n-pro-table-aggregation) .c7n-pro-table-tbody .c7n-pro-table-cell-inner .c7n-pro-btn {
      color: white;
    }

    .c7n-pro-attachment-list-wrapper .c7n-pro-attachment-header.c7n-pro-attachment-header .c7n-pro-attachment-header-buttons>.c7n-pro-btn.c7n-pro-btn.c7n-pro-btn.c7n-pro-btn.c7n-pro-btn.c7n-pro-btn-link:not(.c7n-pro-btn-disabled):hover {
      color: white;
    }

    .c7n-pro-select-wrapper.c7n-pro-select-wrapper.c7n-pro-select-disabled label input {
      -webkit-text-fill-color: white;
    }
  }

  .okBtn {
    background: green !important;
    border-color: green !important;

    &:hover {
      background: #00c400 !important;
      border-color: #00c400 !important;
    }
  }

  .ngBtn {
    background: #c20000 !important;
    border-color: #c20000 !important;

    &:hover {
      background: red !important;
      border-color: red !important;
    }
  }

  :global {
    .c7n-pro-btn-disabled {
      background-color: rgb(191, 191, 191) !important;
      border-color: rgb(191, 191, 191) !important;

      &:hover {
        background-color: rgb(191, 191, 191) !important;
        border-color: rgb(191, 191, 191) !important;
      }
    }
  }

  .ok {
    input {
      font-weight: bold;
      background: #238000 !important;
    }
  }

  .ng {
    input {
      font-weight: bold;
      background: #c20000 !important
    }
  }

  .topRight{
    display: flex;
    align-items: center;
    .buttonText{
      height: 36px;
      line-height: 36px;
      font-weight: 400;
      color: rgba(51, 241, 255, 0.85);
    }
    .buttonRed{
      display: inline-block;
      background-color: #F23A50;
      color: #fff;
      padding: 0px 0.12rem;
      height: 36px;
      line-height: 36px;
      border-radius: 2px;
      margin-left: 8px;
      cursor: pointer;
    }
  }
  .buttonDefault{
    display: inline-block;
    background-color: rgba(69, 190, 255, 0.28);
    color: #fff;
    padding: 0px 0.12rem;
    height: 36px;
    line-height: 36px;
    border-radius: 2px;
    margin-left: 8px;
    cursor: pointer;
  }

  .buttonOk{
    display: inline-block;
    background-color: #11C2CF;
    color: #fff;
    padding: 0px 0.12rem;
    height: 36px;
    line-height: 36px;
    border-radius: 2px;
    margin-left: 8px;
    cursor: pointer;
  }
  .buttonNg{
    display: inline-block;
    background-color: #F23A50;
    color: #fff;
    padding: 0px 0.12rem;
    height: 36px;
    line-height: 36px;
    border-radius: 2px;
    margin-left: 8px;
    cursor: pointer;
  }
  .colButtonOk{
    display: inline-block;
    background-color: #2F5E81;
    color: #64DEA3;
    padding: 0px 0.12rem;
    height: 28px;
    line-height: 28px;
    border-radius: 2px;
    margin-left: 8px;
    cursor: pointer;
  }
  .colButtonNg{
    display: inline-block;
    background-color: #2F5E81;
    color: #F23A50;
    padding: 0px 0.12rem;
    height: 28px;
    line-height: 28px;
    border-radius: 2px;
    margin-left: 8px;
    cursor: pointer;
  }
  .doneStepFlagModals{
    background-color: rgba(56, 112, 143, 1);
    border: 1px solid #63F2FF;
    color: #fff;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    .modalTitle{
      width: 100%;
      height: 45px;
      display: flex;
      align-items: center;
      background: linear-gradient(to right, rgba(99, 242, 255, 0.74) 0%, rgba(80, 234, 242, 0.55) 13.43%, rgba(48, 97, 219, 0.01) 100%);
      background-size: cover;
      padding: 8px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      img {
        margin-right: 4px;
      }
      .barCodeText{
        margin-left: 4px;
      }
      .modalRightFooter{
        div{
          display: inline-block;
          cursor: pointer;
          img{
            margin-left: 8px;
          }
        }
      }
    }
    .modalContent{
      padding: 25px;
    }
  }

  #tableInnerField{
    :global{
      .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper label .c7n-pro-input-number{
        border: auto !important;
      }
      .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper.c7n-pro-input-number-required-colors .c7n-pro-input-number{
        border-color: rgb(251, 173, 0);
      }
      .c7n-pro-input-wrapper.c7n-pro-input-wrapper label .c7n-pro-input{
        border: auto !important;
      }
    }
  }
}

.decisionValue{
  display: flex;
  align-items: center;
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  background-color: rgba(59, 134, 174, 1);
  &.requiredValue{
    border-color: rgb(251, 173, 0);
  }
  div{
    width: 50%;
    height: 32px;
    text-align: center;
    line-height: 32px;
    cursor: pointer;
    &.trueTag{
      background-color: rgba(17, 194, 207, 1);
    }
    &.falseTag{
      background-color: rgba(242, 58, 80, 1);
    }
  }
}
