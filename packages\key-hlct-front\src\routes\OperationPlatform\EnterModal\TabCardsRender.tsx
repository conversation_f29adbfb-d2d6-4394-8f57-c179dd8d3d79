import React from 'react';
import styles from './index.module.less';

const TabCardsRender = ({ cardsList, selectedCardId, primaryCardId, cardCode, cardName, handleSelectCard }) => {
  return (
    <div className={styles.tabContent}>
      {
        cardsList.map(item => {
          let _className;
          const _cardId = String(item[primaryCardId])
          if (selectedCardId === _cardId) {
            _className = styles.previewImgCardSelected
          } else {
            _className = styles.previewImgCard
          }
          return (
            <div
              className={_className}
              onClick={() => handleSelectCard(item)}
              style={{ height: cardCode ? '50px' : '25px' }}
            >
              {item[cardCode] && (<div className={styles.cardName}>
                {item[cardCode]}
              </div>)}
              <div className={styles.cardName}>
                {item[cardName]}
              </div>
              {
                selectedCardId === _cardId && (
                  <div className={styles.cardSelect}>
                    <div className={styles.squareOne}>
                      <div className={styles.squareTwo}></div>
                    </div>
                  </div>
                )
              }
            </div>
          )
        })
      }
    </div>
  )
}

export default TabCardsRender;
