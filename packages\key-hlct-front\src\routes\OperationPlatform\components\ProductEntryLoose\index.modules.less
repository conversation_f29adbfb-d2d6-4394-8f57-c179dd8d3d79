.NewProcessMachinedPartModal {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content .c7n-pro-table-row {
      height: 45px !important;
    }

    .icon-refresh {
      background-color: rgb(91, 136, 160) !important;
      color: white !important;
    }

    .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      border: none !important;
      font-size: 17px !important;
    }

    .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      font-size: 17px !important;
    }

    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .c7n-pro-btn-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .icon {
      color: white !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
    }
  }
}

.customTitle {
  width: 100%;
  font-size: 20px;
  background: rgba(42, 99, 130, 1);
  height: 36px;
  line-height: 36px;
}

.materialLotContent{
  display: flex;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  overflow: auto;
}
.cardFooter{
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 50px;
  // margin-top: 55px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 16px;
  .completeText{
    display: flex;
    align-items: center;
    color: rgba(51, 241, 255, 0.85);
    // font-size: 16px;
    font-family: auto;
    min-width: 130px;
    text-align: right;
    .completeValue{
      color: #fff;
      // font-size: 26px;
      font-weight: 800;
      padding-left: 8px;
    }
    .completeValueAdd{
      color: #fff;
      // font-size: 16px;
      padding-left: 8px;
    }
  }
  .printButton{
    width: 50%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 8px;
    color: #33F1FF;
    .buttonContent{
      width: 50%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      img {
        margin-right: 4px;
      }
      div{
        cursor: pointer;
      }
    }
  }
}
