/* eslint-disable jsx-a11y/alt-text */
// 产品进站-松散
import React, { useState, useMemo } from 'react';
import { TextField, DataSet, Modal, Table, Form, Output, Select } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import intl from 'utils/intl';
import moment from 'moment';
import position from '@/assets/operationPlatformCard/position.png';
import arrowRight from '@/assets/operationPlatformCard/arrow-right.png';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import onSite from '@/assets/operationPlatformCard/onSite.svg';
import codePrint from '@/assets/operationPlatformCard/codePrint.svg';
import { useOperationPlatform } from '../../contextsStore';
import { useResizeObserver } from '../../useResizeObserver';
import { detailDS, tableDs } from './stores/MachinedPartDS';
import { CardLayout, ONotification, NumberKeyboard } from '../commonComponents';
import MachinedPartPrint from '../commonComponents/MachinedPartPrint';
import OnSiteProduction from '../commonComponents/OnSiteProduction';
import styles from './index.modules.less';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.operationPlatform';

const MachinedPartCard = props => {
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );

  const tableDataSet = useMemo(
    () =>
      new DataSet({
        ...tableDs(),
      }),
    [],
  );

  const { enterInfo, workOrderData, dispatch, containerDetail, cardMode } = useOperationPlatform();
  const [printModalShow, setPrintModalShow] = useState(false); // 是否展示打印弹框
  const [siteModalShow, setSiteModalShow] = useState(false); // 是否展示站内弹框
  const [loading, setLoading] = useState(false); // 工单loading
  const [formColumns, setFormColumns] = useState(1); // 工单信息列数

  useResizeObserver((element, size) => {
    // console.log('Element size:', size, element);
    if (size.width <= 480) {
      setFormColumns(1);
    } else if (size.width > 480 && size.width <= 780) {
      setFormColumns(1);
    } else if (size.width > 780 && size.width <= 1090) {
      setFormColumns(2);
    } else {
      setFormColumns(2);
    }
    // 在这里执行你需要的操作，例如重新布局、更新状态等。
  }, document.querySelector('.ProcessWorkorderMachinedPartForm'));

  // 清空数据
  const cleanData = () => {
    dispatch({
      type: 'update',
      payload: {
        workOrderData: {},
      },
    })
    detailDs.loadData([]);
  };

  const columns = [
    {
      name: 'operationName',
    },
    {
      name: 'nextOperationName',
    },
    {
      name: 'routerStepName',
    },
  ];

  const handleConfirm = value => {
    if (tableDataSet.selected.length === 0) {
      ONotification.error({ message: intl.get(`${modelPrompt}.notification.operationRequired`).d('请选择工艺') });
      return false;
    }
    onFetchProcessed(value);
    return true;
  };

  // 扫描在制品
  const onFetchProcessed = value => {
    if (value) {
      setLoading(true);
      const params = {
        identification: value,
        workcellId: enterInfo?.workStationId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        workcellCode: enterInfo?.workStationCode,
        workcellName: enterInfo?.workStationName,
        productionLineId: enterInfo?.productionLineId,
        productionLineCode: enterInfo?.productionLineCode,
        productionLineName: enterInfo?.productionLineName,
        operationId: enterInfo?.selectOperation?.operationId,
        operationName: enterInfo?.selectOperation?.operationName,
        operationDesc: enterInfo?.selectOperation?.description,
        dto3: tableDataSet.selected.length > 0
          ? tableDataSet.selected[0].data
          : null,
        containerInfo: {
          containerId: containerDetail?.containerId,
          containerCode: containerDetail?.containerCode,
          containerTypeId: containerDetail?.containerTypeId,
          capacityQty: containerDetail?.capacityQty,
          personalCapacityQty: containerDetail?.personalCapacityQty,
        },
      };
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-working-part-new/eo-scan/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && !res.failed) {
          detailDs.loadData([res]);
          dispatch({
            type: 'update',
            payload: {
              workOrderData: res,
            },
          });
          setLoading(false);
          if (res?.containerLoadErrorMsg) {
            ONotification.warning({
              message: res?.containerLoadErrorMsg,
            });
          }
          if (res.popoverOrNot === 'Y' && res.partNewDTO3List.length > 0) {
            tableDataSet.loadData(res.partNewDTO3List);
            Modal.open({
              // title: '工艺选择',
              title: intl.get(`${modelPrompt}.operation.select`).d('工艺选择'),
              destroyOnClose: true,
              // closable: true,
              contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
              className: styles.NewProcessMachinedPartModal,
              children: (
                <Table dataSet={tableDataSet} columns={columns} searchCode="InspectionPlatform" />
              ),
              onOk: () => handleConfirm(value),
              onCancel: () => {
                tableDataSet.unSelectAll();
              },
            });
          } else {
            props.handleAddRecords({
              cardCode: props.cardCode,
              messageType: 'SUCCESS',
              message: `扫描在制品${res.identification}成功`,
            });
            props.nextPriority();
            if (res.inType === 'INSPECT' || res.inType === 'IN') {
              dispatch({
                type: 'update',
                payload: {
                  workOrderData: { ...res, cardWorkpiece: 'Y' },
                },
              })
            }
          }
        } else {
          setLoading(false);
          ONotification.error({ message: res.message });
          setTimeout(() => {
            document
              .querySelector(
                `#operationPlatformInput${props.priorityLayout?.filter(item => item.i === '19')[0]?.priority
                }`,
              )
              .focus();
            document
              .querySelector(
                `#operationPlatformInput${props.priorityLayout?.filter(item => item.i === '19')[0]?.priority
                }`,
              )
              .select();
          }, 100);
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'FAIL',
            message: `扫描在制品${value}失败`,
          });
        }
      });
    } else {
      cleanData();
    }
  };

  // 进出站
  const onCompleteWo = value => {
    if (loading) {
      return;
    }
    if (!value || !workOrderData.workOrderId || Number(value) <= 0) {
      return;
    }
    setLoading(true);
    const params = {
      workcellId: enterInfo?.workStationId,
      shiftCode: enterInfo?.shiftCode,
      shiftDate: enterInfo?.shiftDate,
      workcellCode: enterInfo?.workStationCode,
      workcellName: enterInfo?.workStationName,
      productionLineId: enterInfo?.productionLineId,
      productionLineCode: enterInfo?.productionLineCode,
      productionLineName: enterInfo?.productionLineName,
      operationId: enterInfo?.selectOperation?.operationId,
      operationName: enterInfo?.selectOperation?.operationName,
      operationDesc: enterInfo?.selectOperation?.description,
      eoId: workOrderData?.eoId,
      identification: workOrderData?.identification,
      workOrderId: workOrderData?.workOrderId,
      materialId: workOrderData?.materialId,
      routerStepId: workOrderData?.routerStepId,
      currentProcessId: workOrderData?.currentProcessId,
      nextStepId:
        tableDataSet.selected.length > 0
          ? tableDataSet.selected[0].data.routerStepId
          : workOrderData?.firstStepPitStop === 'Y'
            ? null
            : workOrderData?.nextStepId,
      dto3: tableDataSet.selected.length > 0 ? tableDataSet.selected[0].data : null,
      executeType: 'OUTBOUND',
      containerInfo: containerDetail || {},
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-working-part-new/execute/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        if (res.popoverOrNot === 'Y' && res.partNewDTO3List.length > 0) {
          tableDataSet.loadData(res.partNewDTO3List);
          Modal.open({
            // title: '工艺选择',
            title: intl.get(`${modelPrompt}.operation.select`).d('工艺选择'),
            destroyOnClose: true,
            // closable: true,
            contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
            className: styles.NewProcessMachinedPartModal,
            children: (
              <Table dataSet={tableDataSet} columns={columns} searchCode="InspectionPlatform" />
            ),
            onOk: () => handleConfirm(value),
            onCancel: () => {
              tableDataSet.unSelectAll();
            },
          });
        } else {
          ONotification.success();
          setLoading(false);
          dispatch({
            type: 'update',
            payload: {
              workOrderData: {
                containerRefreshFlag: res.containerRefreshFlag,
                // completedQty: workOrderData.completedQty + (trendsNumRef.current ? Number(trendsNumRef.current) : 0),
              },
            },
          })
          if (res?.containerLoadErrorMsg) {
            ONotification.warning({
              message: res?.containerLoadErrorMsg,
            });
          }
          // setComputingTime(false);
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'SUCCESS',
            message: `在制品${workOrderData?.identification}${value === 'OUTBOUND' ? '出站' : '过站'}成功`,
          });
        }
      } else {
        ONotification.error({ message: res.message });
        setLoading(false);
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'FAIL',
          message: `在制品${workOrderData?.identification}${value === 'OUTBOUND' ? '出站' : '过站'}失败`,
        });
      }
    });
  };

  // 退回
  const onReturnWo = (value) => {
    if ((!value || !workOrderData.workOrderId) && workOrderData.doneStepFlag !== 'Y') {
      return false;
    }
    const params = {
      workcellId: enterInfo?.workStationId,
      shiftCode: enterInfo?.shiftCode,
      shiftDate: enterInfo?.shiftDate,
      operationId: enterInfo?.selectOperation?.operationId,
      eoId: workOrderData?.eoId,
      routerStepId: workOrderData?.routerStepId,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/step/return/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        ONotification.success();
        // onFetchProcessed(workOrderData?.identification);
        // dispatch({
        //   type: 'update',
        //   payload: {
        //     workOrderData: {
        //       ...workOrderData,
        //       cardClear: 'Y',
        //     },
        //   },
        // })
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'SUCCESS',
          message: `退回${workOrderData.workOrderNum}成功`,
        });
      } else {
        ONotification.error({ message: res.message });
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'FAIL',
          message: `退回${workOrderData.workOrderNum}失败`,
        });
      }
    });
  };

  const handleOpenPrint = async () => {
    if (!workOrderData.workOrderId || printModalShow) {
      return;
    }
    setPrintModalShow(true)
  }

  const handleCloseStepModal = () => {
    setPrintModalShow(false)
  }

  const machinedPartPrintProps = {
    workOrderData,
    enterInfo,
    handleCloseStepModal,
    printModalShow,
    contentClassName: 'ProcessWorkorderMachinedPartHead',
  }

  const handleOpenSite = async () => {
    setSiteModalShow(true)
  }
  const handleCloseSiteModal = () => {
    setSiteModalShow(false)
  }

  const handleChange = value => {
    detailDs.current?.set('printedSurface', value);
  };

  const onSiteProductionProps = {
    catdType: 'NewProcessMachinedPart',
    enterInfo,
    handleCloseSiteModal,
    siteModalShow,
    contentClassName: 'ProcessWorkorderMachinedPartHead',
    onFetchProcessed,
  }

  const numberKeyboardProps = {
    materialLotTop: detailDs.current?.get('materialLot'),
    checkedMaterialLot: false,
    workOrderData,
    uomName: 'PCS',
  }

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <CardLayout.Layout spinning={loading}>
        <CardLayout.Header
          className='ProcessWorkorderMachinedPartHead'
          // title="新加工件"
          title='产品进站-松散'
          help={props?.cardUsage?.remark}
          content={
            <TextField
              dataSet={detailDs}
              // placeholder="请扫描EO"
              placeholder={intl.get(`${modelPrompt}.placeholder.eo`).d('请扫描EO')}
              id={`operationPlatformInput${props.priorityLayout?.filter(item => item.i === '19')[0]?.priority}`}
              name="identificationField"
              onEnterDown={e => onFetchProcessed(e.target.value)}
              onChange={value => (value ? null : onFetchProcessed(null))}
              prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />}
            />
          }
          addonAfter={
            <>
              <Select
                placeholder={intl.get(`${modelPrompt}.model.execute.printedSurface`).d('请选择印刷面')}
                disabled={!detailDs.current?.get('eoId')}
                value={detailDs.current?.get('printedSurface')}
                onChange={handleChange}
                getPopupContainer={() => document.getElementById('operationPlatform') || document.body}
              >
                <Select.Option value="face">
                  {intl.get(`${modelPrompt}.face`).d('正面')}
                </Select.Option>
                <Select.Option value="back">
                  {intl.get(`${modelPrompt}.back`).d('反面')}
                </Select.Option>
              </Select>
            </>
          }
        />
        <CardLayout.Content className='ProcessWorkorderMachinedPartForm'>
          <div
            style={{
              display: workOrderData.currentProcess || workOrderData.nextProcess ? 'block' : 'none',
            }}
            className={styles.customTitle}
          >
            &nbsp;&nbsp;
            <img src={position} alt="" />
            <span style={{ color: 'rgba(51, 241, 255, 1)' }}> {workOrderData.currentProcess}</span>
            &nbsp;&nbsp;
            <img src={arrowRight} alt="" />
            &nbsp;&nbsp;
            <span style={{ color: 'rgba(255, 255, 255, 0.85)' }}>{workOrderData.nextProcess}</span>
          </div>
          <div className={styles.materialLotContent}>
            <div style={{ marginBottom: '8px' }}>
              <Form dataSet={detailDs} labelWidth={130} columns={cardMode === 'Tile' ? formColumns : 3}>
                <Output name='workOrderNum' />
                <Output name='identification' />
                <Output name='materialCode' />
                {/* <Output name='opProcess' /> */}
                {/* <Output
                  name='pitStopDate'
                  renderer={({ value }) => {
                    return value ? moment(value).format('YYYY-MM-DD HH:mm') : null;
                  }}
                /> */}
                {/* <Output name='customerDesc' /> */}
                <Output name='materialName' />
                <Output
                  name='qty'
                  renderer={({ record }) => {
                    return  `${record.get('completedQty')  || 0}  / ${record?.get('eoQty')?.substring(0, record?.get('eoQty')?.indexOf('.')) || 0}`
                  }}
                />
              </Form>
            </div>
            <NumberKeyboard keyboardWidth={256} onOk={onCompleteWo} onReturn={onReturnWo} numberKeyboardProps={numberKeyboardProps} />
          </div>

          <div className={styles.cardFooter}>
            <div className={styles.printButton}>
              <div className={styles.buttonContent}>
                <img src={onSite} alt='' />
                <div onClick={handleOpenSite}>{intl.get(`${modelPrompt}.button.onSite`).d('站内在制')}</div>
              </div>
              <div className={styles.buttonContent}>
                <img src={codePrint} alt='' />
                <div onClick={handleOpenPrint}>{intl.get(`${modelPrompt}.button.codePrint`).d('条码打印')}</div>
              </div>
            </div>
          </div>
        </CardLayout.Content>
      </CardLayout.Layout>
      <MachinedPartPrint {...machinedPartPrintProps} />
      <OnSiteProduction {...onSiteProductionProps} />
    </div>
  );
};

export default MachinedPartCard;
