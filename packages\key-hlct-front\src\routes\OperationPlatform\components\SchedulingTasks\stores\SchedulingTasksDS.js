/**
 * @Description: 数据收集组DS
 * @Author: <EMAIL>
 * @Date: 2023-03-14 10:19:56
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.hmes.ProcessWorkorderMachinedPart';

// tableDS
const tableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  autoQueryAfterSubmit: false,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'dispatchNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispatchNumber`).d('调度任务'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码/型号'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('所属工单'),
    },
    {
      name: 'priority',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.priority`).d('优先级'),
    },
    {
      name: 'dispatchDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispatchDate`).d('计划开始-结束'),
    },
    {
      name: 'dispatchStartDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispatchStartDate`).d('计划开始时间'),
    },
    {
      name: 'dispatchEndDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispatchEndDate`).d('计划结束时间'),
    },
    {
      name: 'attributeDispatchQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.attributeDispatchQty`).d('完工/派工/单位'),
    },
    {
      name: 'dispatchQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.dispatchQty`).d('数量/单位'),
    },
    {
      name: 'completedQty',
      type: FieldType.string,
    },
  ],
});

const formDS = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: true,
  fields: [
    {
      name: 'shiftDateCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.shiftDateCode`).d('班次日期/编码'),
      // lookupUrl: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-material-traces/calendar-shift/query/for/ui`,
    },
    {
      name: 'shiftCode',
    },
    {
      name: 'shiftDate',
    },
  ],
});

export { tableDS, formDS };
