
@font-face {
  font-family: 'Seven Segment';
  src: url('./front/Digital.ttf') format('truetype');
}

:global {
  #BatchProcessingTable {
    .c7n-pro-table.c7n-pro-table .c7n-pro-table-tbody .c7n-pro-table-row .c7n-pro-table-cell {
      background-color: #38708F !important;
      border: 1px solid rgba(255, 255, 255, 0.5) !important;
    }

    .c7n-pro-table:not(.c7n-pro-table-bordered) .c7n-pro-table-tbody {
      border: 1px solid rgba(255, 255, 255, 0.5) !important;
    }

    .c7n-pro-table:not(.c7n-pro-table-bordered) .c7n-pro-table-thead.c7n-pro-table-thead .c7n-pro-table-cell {
      background-color: #2A6382 !important;
      color: #33F1FF !important;
      border: 1px solid rgba(255, 255, 255, 0.5) !important;
      font-size: 1vw !important;
    }
  }
}
.tabContent {
  padding: 5px;
  width: 100%;
  flex: 1;
  display: flex;

  align-items: flex-start;
  flex-wrap: wrap;
  overflow: hidden auto;
  color: #fff;

  .content {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    background-color: #61849C;
    border: 2px solid #59BFCD;
    border-radius: 16px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    margin-bottom: 20px;
    margin-right: 20px;
    .cardSelect {
      width: 40px;
      height: 40px;
      background-color: #4be3ee;
      border-radius: 100%;
      position: absolute;
      top: -19px;
      right: -12px;

      .squareOne {
        width: 11px;
        height: 7px;
        background-color: #fff;
        position: absolute;
        bottom: 9px;
        left: 10px;
        border-radius: 2px;
        transform: rotateZ(-45deg);

        .squareTwo {
          width: 17px;
          height: 12px;
          background-color: #4be3ee;
          position: absolute;
          bottom: 3px;
          left: 3px;
          border-radius: 2px;
        }
      }
    }

    .top {
      height: 60px;
      display: flex;
      justify-content: space-between;
      border-bottom: 2px solid rgba(255, 255, 255, 0.82);
      padding: 5px;

      .topRight {
        width: 45%;
        font-size: 18px;
        font-weight: bold;
      }

      .topLeft {
        font-family: 'Seven Segment', monospace;
        color: #fff;
        width: 55%;
        font-size: 36px;
        line-height: 50px;
        text-align: right;
      }
    }

    .down {
      padding: 5px;
      height: 130px;
      overflow-x: hidden;
      overflow-y: scroll;
      .downList {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 14px;
        div:first-child{
          width: 40%;
        }
        div:nth-child(2){
          width: 10%;
          text-align: center;
        }
        div:nth-child(3){
          width: 50%;
        }
      }
    }
  }

}

.formParent{
  position: relative;
  .floatSelect{
    position: absolute;
    color: #fff;
    right: 10px;
    top: 10px;
  }
}
