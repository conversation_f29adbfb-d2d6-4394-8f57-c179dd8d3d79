.processComponents {
  :global {
    .c7n-tree {
      background: rgba(56, 112, 143, 1) !important;
      color: white !important;
    }

    .c7n-tree.c7n-tree i.icon.icon-arrow_drop_down.c7n-tree-switcher-icon {
      color: white !important;
    }

    .c7n-progress-text {
      color: white !important;
    }
    .c7n-pro-table-wrapper.c7n-pro-table-wrapper .c7n-pro-table .c7n-pro-table-content table {
      .c7n-pro-table-expand-icon{
        border-color: rgb(0 210 255) !important;
      }
      .c7n-pro-table-expand-icon::before{
        color: rgb(2 213 255) !important;
      }
      .c7n-pro-table-expand-icon.c7n-pro-table-expand-icon-expanded::after {
        border-top: 2px solid rgb(2 213 255) !important;
      }
      .c7n-pro-table-expand-icon.c7n-pro-table-expand-icon-expanded {
        border: 1px solid rgb(7 209 241) !important;
      }

    }
  }

  .parentNodes {
    border: 1px solid #ffffff4d !important;
  }
}

.qty-content{
  display: flex;
  justify-content: space-between;
  align-items: center;
  .qty-box {
    width: 80%;
    position: relative;
    height: 30px;
    :global {
      .qty-bg {
        padding: 5px 0;
        width: 100%;
        height: 100%;
        .qty-bg-box {
          width: 100%;
          height: 100%;
          overflow: hidden;
          border-radius: 99px;
          background-color: rgba(255, 255, 255, 0.5);
          .qty-bg-inner {
            background-color: #11d954;
            height: 100%;
          }
        }
      }
      .qty-front {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        z-index: 1;
      }
    }
  }
}


// #ProcessComponents{
//   :global{
//     .icon{
//       font-size: 0.8vw !important;
//     }
//   }
// }
