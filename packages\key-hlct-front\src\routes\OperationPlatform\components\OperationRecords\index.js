/**
 * @Description: 操作记录卡片
 * @Author: <<EMAIL>>
 * @Date: 2023-07-11 09:39:58
 * @LastEditTime: 2023-07-11 14:02:06
 * @LastEditors: <<EMAIL>>
 */
import React, { useEffect, useRef } from 'react';
import { Row, Col, Tooltip } from 'choerodon-ui/pro';
import { Timeline } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import {  useOperationPlatform } from '../../contextsStore';
import { CardLayout } from '../commonComponents';
import styles from './index.modules.less';

const DataAcquisition = () => {

  const { operationRecords, enterInfo } = useOperationPlatform();
  const tileLine = useRef(null);
  // console.log('operationRecords', operationRecords);
  useEffect(() => {
    document.querySelector('#OperationRecordsTimelineScor').scrollTop = 0;
  }, [operationRecords]);

  return (
    <CardLayout.Layout>
      <CardLayout.Header
        className='OperationRecordsHead'
        title='操作记录'
      />
      <CardLayout.Content className='OperationRecordsForm'>
        <Timeline
          id='OperationRecordsTimelineScor'
          ref={tileLine}
          className={styles.OperationRecordsTimeline}
          // pending="持续记录中..."
          reverse
        >
          {
            operationRecords.map(item => {
              return (
                <Timeline.Item key={item.uuid} color={item.messageType === 'SUCCESS' ? 'green' : 'red'}>
                  <Row>
                    <Col span={6}>
                      <Tooltip placement="topLeft" title={item.creationDate}>
                        {item.creationDate}
                      </Tooltip>
                    </Col>
                    <Col span={4}>
                      <Tooltip placement="topLeft" title={item.creationDate}>
                        {enterInfo.userName}
                      </Tooltip>
                    </Col>
                    <Col span={14}>
                      <Tooltip placement="topLeft" title={item.cardName}>
                        {item.cardName}-
                        <span style={{fontWeight: 700}}>{item.message.slice(0, item.message.length-2)}</span>
                        {
                          item.messageType === 'SUCCESS' && (
                            <span style={{color: 'rgba(100, 222, 163, 1)', fontWeight: 700}}>成功</span>
                          )
                        }
                        {
                          item.messageType === 'FAIL' && (
                            <span style={{color: 'rgba(255, 77, 79, 1)', fontWeight: 700}}>失败</span>
                          )
                        }
                      </Tooltip>
                    </Col>
                  </Row>
                </Timeline.Item>
              )
            })
          }
        </Timeline>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(DataAcquisition);
