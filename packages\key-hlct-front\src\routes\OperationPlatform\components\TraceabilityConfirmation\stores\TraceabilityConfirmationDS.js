// 追溯确认DS
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const treeDS = () => ({
  autoQuery: false,
  autoCreate: false,
  parentField: 'parentId',
  idField: 'id',
  // primaryKey: 'bomComponentId',
  transport: {
    read: ({ data }) => {
      return {
        data,
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-material-traces/left-material-list/ui`,
        method: 'GET',
      };
    },
  },
  // data: [
  //   {
  //     id: 1,
  //     categoryCode: '11',
  //     unitQty: '10',
  //     bomMaterialName: '111',
  //     bomMaterialCode: '1111',
  //   },
  //   {
  //     id: 2,
  //     parentBomMaterialCode: 1,
  //     seqNum: 1,
  //     materialLotCode: '222',
  //     lotCode: '2222',
  //   },
  //   {
  //     id: 3,
  //     parentBomMaterialCode: 1,
  //     seqNum: 2,
  //     materialLotCode: '333',
  //     lotCode: '3333',
  //   },
  //   {
  //     id: 4,
  //     categoryCode: '44',
  //     unitQty: '40',
  //     bomMaterialName: '444',
  //     bomMaterialCode: '4444',
  //   },
  //   {
  //     id: 5,
  //     parentBomMaterialCode: 4,
  //     seqNum: 1,
  //     materialLotCode: '555',
  //     lotCode: '5555',
  //   },
  // ],
});

const formAddDS = () => ({
  autoQuery: false,
  autoCreate: true,
  fields: [
    {
      name: 'materialCode', // 物料编码
      type: 'string',
    },
  ],
}); // 新增表单DS

// const treeDS = () => ({
//   primaryKey: 'bomComponentId',
//   queryUrl: '/tree.mock',
//   autoQuery: true,
//   parentField: 'parentId',
//   expandField: 'expand',
//   idField: 'bomComponentId',
//   fields: [
//     { name: 'bomComponentId', type: 'number' },
//     { name: 'expand', type: 'boolean' },
//     { name: 'parentId', type: 'number' },
//   ],
//   events: {
//     select: ({ record, dataSet }) => console.log('select', record, dataSet),
//     unSelect: ({ record, dataSet }) =>
//       console.log('unSelect', record, dataSet),
//   },
// });

export { treeDS, formAddDS };
