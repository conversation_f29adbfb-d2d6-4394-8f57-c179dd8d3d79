/* eslint-disable jsx-a11y/alt-text */
import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { Button, DataSet, Form, Output, TextField } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import formatterCollections from 'utils/intl/formatterCollections';
import { ButtonColor } from "choerodon-ui/pro/lib/button/enum";
import request from 'utils/request';
import { BASIC } from '@utils/config';

import { useResizeObserver } from '../../useResizeObserver';
import { DispatchType, useOperationPlatform } from '../../contextsStore';
import { indexDS, scanModalDS } from './stores';
import { CardLayout, ONotification, useRequest } from '../commonComponents';
import scanIcon from "../../../../assets/operationPlatformCard/scanIcon.svg";
import { ConfirmInSite, DischargeFromFurnace } from "./services";
import C7nModal from '../../C7nModal';
import ScanDetailModal from "./ScanDetailModal";
import ChooseList from "./ChooseList";
import cardSvg from "../../../../assets/icons/operation.svg";
import styles from './index.module.less';

const tenantId = getCurrentOrganizationId();
const Modal = C7nModal

const BatchProcessing = props => {
  const containerCodeInput = useRef()
  const indexDs = useMemo(() => new DataSet(indexDS()), []);
  const scanModalDs = useMemo(() => new DataSet(scanModalDS()), [])
  const [selectedCardIds, setSelectedCardIds] = useState([])
  const { workOrderData, cardMode, enterInfo, dispatch } = useOperationPlatform();
  const [loading, setLoading] = useState(false);
  const [formColumns, setFormColumns] = useState(4); // 表单信息列数
  const [cardsList, setCardsList] = useState([]);
  const { run: confirmInSite, loading: confirmInSiteCodeLoading } = useRequest(ConfirmInSite(), {
    manual: true,
    needPromise: true
  });
  const { run: dischargeFromFurnace, loading: dischargeFromFurnaceLoading } = useRequest(DischargeFromFurnace(), {
    manual: true,
    needPromise: true
  });

  useResizeObserver((element, size) => {
    // console.log('Element size:', size, element);
    if (size.width <= 480) {
      setFormColumns(1);
    } else if (size.width > 480 && size.width <= 780) {
      setFormColumns(2);
    } else if (size.width > 780 && size.width <= 1090) {
      setFormColumns(3);
    } else {
      setFormColumns(4);
    }
    // 在这里执行你需要的操作，例如重新布局、更新状态等。
  }, document.querySelector('.BatchProcessingForm'));

  useEffect(() => {
    setLoading(props.spin);
  }, [props.spin]);

  // 查询
  useEffect(() => {
    handleQuery();
  }, [enterInfo]);

  /**
   * table查询
   */
  const handleQuery = () => {
    setLoading(true);
    indexDs.setQueryParameter('operationId', enterInfo.operationList[0]?.operationId)
    indexDs.setQueryParameter('workcellId', enterInfo.workStationId)
    indexDs.query().then(res => {
      setLoading(false);
      if (res && res.success) {
        if (res.rows) {
          setCardsList(res.rows?.containers)
        }
      } else {
        ONotification.error({ message: res.message || '查询失败' })
      }
    })
  };

  const handleSelectCard = (id) => {
    if (selectedCardIds.includes(id)) {
      setSelectedCardIds(prevState => prevState.filter(item => item !== id))
      return
    }
    setSelectedCardIds((prevState) => prevState.concat([id]))
  }

  const onScanContainerCode = useCallback(
    (e) => {
      const _value = e.target.value.trim();
      if (!_value) {
        return;
      }
      console.log(cardsList);
      const existingFlag = cardsList.findIndex(item=>item.containerCode === _value)
      if(existingFlag>=0){
        ONotification.error({message: '当前容器已经进站，请勿重复扫描！'})
        return;
      }
      const { workStationId, productionLineId, productionLineCode, selectOperation, shiftCode, shiftDate } = enterInfo
      scanModalDs.setQueryParameter('workcellId', workStationId)
      scanModalDs.setQueryParameter('productionLineId', productionLineId)
      scanModalDs.setQueryParameter('productionLineCode', productionLineCode)
      scanModalDs.setQueryParameter('operationId', selectOperation.operationId)
      scanModalDs.setQueryParameter('containerCode', _value)
      scanModalDs.setQueryParameter('tenantId', tenantId)
      const params = {
        workcellId:workStationId,
        productionLineId,
        productionLineCode,
        operationId:selectOperation.operationId,
        containerCode: _value,
        tenantId,
      }
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-batch-processing/scan-container/for-ui`, {
        method: 'GET',
        query: params,
      }).then(async res => {
        if (res && res.success && res?.rows?.detailInfo.content.length) {
            scanModalDs.loadData(res.rows.detailInfo.content, res.rows.detailInfo.totalElements)
            Modal.open({
              header: (
                <div style={{ display: 'flex' }}>
                  <img src={cardSvg} alt="" className="titleIcon" />
                  <div className="c7n-pro-modal-title">{`容器信息  ${_value}`}</div>
                </div>
              ),
              destroyOnClose: true,
              key: Modal.key(),
              style: { width: '900px' },
              bodyStyle: { padding: '0 16', },
              contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
              children: (
                <ScanDetailModal detailInfo={{
                  materialQty: res.rows.materialQty,
                  detailQty: res.rows.detailQty,
                }} scanModalDs={scanModalDs} />
              ),
              onOk: async () => {
                setCardsList([])
                setSelectedCardIds([])
                const response = await confirmInSite({
                  params: {
                    tenantId,
                    operationId: selectOperation.operationId,
                    workcellId: workStationId,
                    productionLineId,
                    productionLineCode,
                    containerId: res.rows.containerId,
                    containerCode: res.rows.containerCode,
                    shiftCode,
                    shiftDate,
                  }
                });
                if (response && response.success) {
                  handleQuery();
                  ONotification.success()
                } else {
                  return false
                }
              }
            })
        } else {
          ONotification.error({ message: res.message || '查询失败' });
          return false;
        }
      });
    },
    [enterInfo, cardsList],
  )

  const dischargeFromFurnaceBtn = async () => {
    const { workStationId, productionLineId, productionLineCode, selectOperation, shiftCode, shiftDate } = enterInfo
    if (!selectedCardIds.length) {
      ONotification.error({ message: '请选择容器！' })
      return
    }
    const params = {
      operationId: selectOperation.operationId,
      workcellId: workStationId,
      productionLineId,
      productionLineCode,
      shiftCode,
      shiftDate,
      containerIds: selectedCardIds
    }
    const res = await dischargeFromFurnace({ params })
    if (res && res.success) {
      setSelectedCardIds([]);
      setCardsList([])
      handleQuery();
      res.rows.forEach(item => {
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: item.success ? 'SUCCESS' : 'FAIL',
          message: `容器${item.containerCode}出站`,
        });
      })
    }

  }

  // 容器卡片查询容器信息
  const queryContainerDetail = (containerCode) => {
    const params = {
      // @ts-ignore
      containerCode: containerCode,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-encasement/container/scan/ui`, {
      method: 'GET',
      query: params,
    }).then(async res => {
      if (res && res.success) {
        dispatch({
          type: DispatchType.update,
          payload: {
            containerDetail: res.rows,
          },
        });
      } else {
        ONotification.error({ message: res.message });
        return false;
      }
    });
  };

  return (
    <CardLayout.Layout spinning={loading || confirmInSiteCodeLoading}>
      <CardLayout.Header
        title='批量加工'
        help={props?.cardUsage?.remark}
        content={
          <>
            <TextField
              name="containerCode"
              placeholder="请扫描容器"
              disabled={!enterInfo.workStationId}
              // @ts-ignore
              ref={containerCodeInput}
              onEnterDown={onScanContainerCode}
              prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />}
              style={{
                width: '256px',
                marginRight: '-18px',
              }}
            />
          </>
        }
        addonAfter={
          <Button
            style={{ marginLeft: 20 }}
            color={ButtonColor.primary}
            onClick={() => {
              dischargeFromFurnaceBtn()
            }}
          >
            出炉
          </Button>
        }
      />
      <CardLayout.Content className='BatchProcessingForm'>
        <div className={styles.formParent}>
          <Form dataSet={indexDs} labelWidth={150} columns={cardMode === 'Tile' ? formColumns : 4}
            style={{ flex: 1, background: 'rgba(42, 99, 130, 1)' }}>
            <Output name='containerQty' />
            <Output name='eoTotalQty' />
          </Form>
          <div className={styles.floatSelect}>{`已选: ${selectedCardIds.length}`}</div>
        </div>

        <div id='BatchProcessingTable'>
          <ChooseList selectedIds={selectedCardIds} cardsList={cardsList} handleSelectCard={handleSelectCard} queryContainerDetail={queryContainerDetail} />
        </div>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(BatchProcessing);
