.containerLoadingDetail {
  :global {
    .c7n-divider.c7n-divider-horizontal {
      margin: 0 0 !important;
    }

    .c7n-card-body {
      padding: 0 !important;
      background: #395470;
    }

    .c7n-pro-btn.c7n-pro-btn-default.c7n-pro-btn-raised.c7n-pro-btn-disabled, .c7n-pro-btn.c7n-pro-btn-default.c7n-pro-btn-raised:disabled{
      background: rgb(75, 75, 76) !important;
      color: white;
      border-color: rgb(75, 75, 76) !important;
    }
  }
}

.titleMater {
  flex-grow: 0;
  flex-shrink: 0;
  color: white;
  background: #45acf140;
  margin: 0.08rem;
  width: 60px;
  /* text-align: center; */
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  flex-direction: column;

  .Alternative {
    background: #ffffff40;
    border-radius: 10px;
    padding: 3px;
  }
}

.hzero-draggable-card-content {
  background: #38708f !important;
}

.numberModal {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .icon-close {
      color: white !important;
      top: 0 !important;
    }
  }
}

.modalForm {
  :global {
    .c7n-pro-field-label {
      color: #1cdbef !important;
      font-size: 18px;
    }

    .c7n-pro-output {
      color: white !important;
    }

    .c7n-pro-select-wrapper {
      background: #50819c !important;
      color: white !important;
    }
    .c7n-pro-output-wrapper {
      color: white !important;
    }
    .c7n-pro-input {
      color: white !important;
      // border-color: #50819c !important;
    }
    .c7n-pro-input-wrapper {
      color: white !important;
      background: #50819c !important;
    }
    .c7n-pro-input-number-wrapper.c7n-pro-input-number-wrapper label .c7n-pro-input-number {
      // border: 1px solid rgba(255, 255, 255, 0.5) !important;
      background: #50819c !important;
      color: #fff !important;
    }

    .c7n-pro-radio-wrapper {
      color: white !important;
      background: #50819c !important;
    }
    .c7n-pro-radio-inner {
      color: white !important;
      background: #50819c !important;
      border-color: white !important;
      &::after {
        color: #00d4cd !important;
      }
    }
    .c7n-pro-radio-label {
      color: white !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-radio:checked + .c7n-pro-radio-inner {
      color: #00d4cd !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper.c7n-pro-radio-button:not(.c7n-pro-table-customization-select-view-option)
      .c7n-pro-radio:checked
      + .c7n-pro-radio-inner {
      color: #00d4cd !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-button .c7n-pro-checkbox-label {
      background: #50819c !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper {
      color: #fff !important;
      margin-right: 16px !important;
      background: #50819c !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper:not(.c7n-pro-checkbox-button)
      .c7n-pro-checkbox:checked
      + .c7n-pro-checkbox-inner {
      color: #00d4cd !important;
      background: #50819c !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper.c7n-pro-checkbox-button
      .c7n-pro-checkbox:checked:disabled
      + i
      + .c7n-pro-checkbox-label {
      color: #fff !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper.c7n-pro-checkbox-button
      .c7n-pro-checkbox:checked:disabled
      + i
      + .c7n-pro-checkbox-label::after {
      color: #00d4cd !important;
    }

    .c7n-pro-checkbox-inner {
      background-color: #50819c !important;
      border-color: #00d4cd !important;
    }
  }
}

#failedTable{
  :global{
    .c7n-pro-table-tbody .c7n-pro-table-row.c7n-pro-table-row-current .c7n-pro-table-cell{
      background-color: rgba(17, 194, 207, 0.6) !important;
    }
    .c7n-pro-table-tbody .c7n-pro-table-row.c7n-pro-table-row-current{
      background-color: rgba(17, 194, 207, 0.6) !important;
    }
  }
}
.MachineMaterialsModal {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }
  }

  .icon-refresh {
    background-color: rgb(91, 136, 160) !important;
    color: white !important;
  }

  .c7n-pro-modal-content
    .c7n-pro-modal-body
    .c7n-spin-nested-loading
    .c7n-spin-container
    .c7n-pro-table-content
    .c7n-pro-table-thead
    .c7n-pro-table-cell {
    background-color: #3c87ad !important;
    color: white !important;
    border: none !important;
  }

  .c7n-pro-modal-content
    .c7n-pro-modal-body
    .c7n-spin-nested-loading
    .c7n-spin-container
    .c7n-pro-table-content
    .c7n-pro-table-tbody
    .c7n-pro-table-cell {
    background-color: #3c87ad !important;
    color: white !important;
  }

  .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
    background-color: #3c87ad !important;
    color: white !important;
  }

  .c7n-pro-btn-wrapper {
    background-color: #3c87ad !important;
    color: white !important;
  }

  .icon {
    color: white !important;
  }

  .c7n-pro-pagination-perpage {
    color: white !important;
  }

  .c7n-pro-pagination-page-info {
    color: white !important;
  }
}

.laneTitle {
  display: inline-flex;
  width: 100%;

  .materialInfo {
    color: white;
    margin-left: 10px;
    flex-grow: 1;
    width: 50%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .numberInfo {
    .materialInfo;
    text-align: right;
    margin-left: 0;
  }
}

.c7nProModalheader {
  background-color: #3c87ad !important;
  padding: 8px 16px 8px !important;
  display: flex;
}
.c7nProModalTitle {
  color: white !important;
  margin-left: 8px;
}

.printLightRow{
  td:last-child{
    position: relative;
    &::before{
      content: '';
      width: 34px;
      height: 34px;
      position: absolute;
      //background: url('../../../../assets/operationPlatformCard/materialLot.png') no-repeat;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}

.againLightRow{
  td:last-child{
    position: relative;
    &::before{
      content: '1241';
      width: 34px;
      height: 34px;
      position: absolute;
      //background: url('../../../../assets/operationPlatformCard/executeHomework.png') no-repeat;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}

.columnRender{
  :global {
  .box {
    width: 40px;
    height: 40px;
    background: #38708F;
    //box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
    //box-shadow: 0px 0px 8px rgba(0, 0, 0, 0.3);
    position: relative;
    //z-index: 90;
  }
  .box .box-con{
    width: 85px;
    height: 88px;
    overflow: hidden;
    position: absolute;
    top: -3px;
    right: -3px;
  }
  .box .box-text {
    font-size: 8px;
    color: white;
    text-align: center;
    transform: rotate(30deg);
    position: relative;
    left: 25px;
    top: 0px;
    width: 90px;
    height: 20px;
    // background-color: #00801C;
    // background-image: linear-gradient(top, #BFDC7A, #8EBF45);
    // box-shadow: 0px 0px 3px rgba(0, 0, 0, 0.3);
  }
  .box .box-text:before, .box-text:after {
    content: "";
    position: absolute;
    bottom: -3px;
  }
  .box .box-text:before {
    left: 0;
  }
  .box .box-text:after {
    right: 0;
  }
}
}

.orangeButton {
  background-color: #f79901 !important;
}


.eoTag{
  td:last-child{
    position: relative;
    &::before{
      content: '';
      width: 34px;
      height: 34px;
      position: absolute;
      background: url('../../../../assets/operationPlatformCard/eo.svg') no-repeat;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}

.materialLotCodeTag{
  td:last-child{
    position: relative;
    &::before{
      content: '';
      width: 34px;
      height: 34px;
      position: absolute;
      background: url('../../../../assets/operationPlatformCard/materialLotCode.svg') no-repeat;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}

.containerTag{
  td:last-child{
    position: relative;
    &::before{
      content: '';
      width: 34px;
      height: 34px;
      position: absolute;
      background: url('../../../../assets/operationPlatformCard/container.svg') no-repeat;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}


