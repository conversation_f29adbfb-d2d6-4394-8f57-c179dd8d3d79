/**
 * @Description: 列拖拽
 * @Author: <<EMAIL>>
 * @Date: 2021-09-14 16:17:56
 * @LastEditTime: 2022-05-17 15:11:54
 * @LastEditors: <<EMAIL>>
 */
import React, { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd-9.3.4';
import { Row, Col } from 'choerodon-ui/pro';
import { Card, Divider, Icon } from 'choerodon-ui';
import ItemTypes from './itemTypes';
import styles from './index.module.less';

const TopicList = ({
  id,
  listItem,
  canEdit,
  countQuantityADD,
  handleSelectCard,
  openNumberModal,
  currentItem,
  selected,
  index,
  moveCard,
  previewList,
  draggingState,
  draggingStateChange,
}) => {
  const ref = useRef(null);

  const [, drop] = useDrop({
    // 定义拖拽的类型
    accept: ItemTypes.TOPIC,
    hover(item, monitor) {
      // 异常处理判断
      if (!ref.current) {
        return;
      }
      // 拖拽目标的Index
      const dragIndex = item.index;
      const dragId = item.id;
      if(!canEdit){
        return;
      }
      // let canMove = false;
      // // 确认包含该元素
      // previewList.forEach(_item => {
      //   if (_item.questionTuid === dragId) {
      //     canMove = true;
      //   }
      // });
      // if (!canMove) {
      //   return;
      // }
      // const targetLine = previewList[dragIndex].id;
      // const targetIndex = previewList.findIndex(_item => _item.questionTuid === targetLine);
      // console.log('targetLine', targetLine, targetItem, index, previewList);
      // if(targetIndex !== index){
      //   return;
      // }

      // 放置目标Index
      const hoverIndex = index;
      // 如果拖拽目标和放置目标相同的话，停止执行
      if (dragIndex === hoverIndex) {
        return;
      }
      // 如果不做以下处理，则卡片移动到另一个卡片上就会进行交换，下方处理使得卡片能够在跨过中心线后进行交换.
      // 获取卡片的边框矩形
      const hoverBoundingRect = ref.current.getBoundingClientRect();
      // 获取X轴中点
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      // 获取拖拽目标偏移量
      const clientOffset = monitor.getClientOffset();
      // console.log(clientOffset)
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      // 从上往下放置
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      // 从下往上放置
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      // 调用方法完成交换
      moveCard(dragIndex, hoverIndex, dragId);
      // 重新赋值index，否则会出现无限交换情况
      /* eslint-disable-next-line */
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    item: { type: ItemTypes.TOPIC, id, index },
    collect: monitor => {
      draggingStateChange(monitor.isDragging());
      return {
        isDragging: monitor.isDragging(),
      };
    },
  });

  const opacity = isDragging ? 0 : 1;

  drag(drop(ref));
  return (
    <div
      ref={ref}
      style={{ opacity }}
      className={draggingState ? styles['row-dragging-item'] : styles['row-drag-item']}
    >
      <div className={styles['row-drag-item-left']}>
        <Card
          selected={selected.some(
            selectedItem => selectedItem.materialLotId === listItem.materialLotId &&
              selectedItem.materialId === listItem.materialId &&
              selectedItem.assemblePointCode ===
              listItem.assemblePointCode,
          )}
          style={{
            width: 190,
            // border: listItem.materialType === 'C' ||
            //   (countQuantityADD(currentItem) > listItem?.unitQty &&
            //     (listItem.calculateQty === 0
            //       ? false
            //       : listItem.calculateQty ||
            //       listItem.inputQty ||
            //       listItem.expectQty))
            //   ? '1.5px solid yellow'
            //   : '1.5px solid white',
            marginLeft: '0',
            borderRadius: '5px',
          }}
        >
          <Row
            onClick={listItem.materialType === 'A' ||
              listItem.materialType === 'B'
              ? null
              : () => handleSelectCard(listItem)}
            style={{
              background: listItem.materialType === 'B' ? '#d43030' : '#578aa7',
              padding: '0 0 0 4px',
              borderTopLeftRadius: '5px',
              borderTopRightRadius: '5px',
              borderBottom: '1px solid rgba(255, 255, 255, 0.35)',
            }}
          >
            <span style={{ color: 'white' }}>
              <Col
                span={24}
                style={{
                  color: listItem.materialType === 'C' ||
                    (countQuantityADD(currentItem) > listItem?.unitQty &&
                      (listItem.calculateQty === 0
                        ? false
                        : listItem.calculateQty ||
                        listItem.inputQty ||
                        listItem.expectQty))
                    ? 'yellow'
                    : 'white',
                }}
              >
                {listItem.materialLotCode}
              </Col>
              {/* <Col span={1}>
                <Divider type="vertical" />
              </Col> */}
            </span>
          </Row>
          {/* <Divider
            onClick={listItem.materialType === 'A' ||
              listItem.materialType === 'B'
              ? null
              : () => handleSelectCard(listItem)} /> */}
          <Row
            style={{
              background: listItem.materialType === 'B' ? '#d43030' : '#578aa7',
              padding: '0 0 0 4px',
            }}
          >
            <span style={{ color: 'white' }}>
              <Col
                onClick={listItem.materialType === 'A' ||
                  listItem.materialType === 'B'
                  ? null
                  : () => handleSelectCard(listItem)}
                span={18}
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                L:{listItem.lot}
              </Col>
              <Col span={6} style={{ textAlign: 'center', overflow: 'hidden', textOverflow: 'ellipsis', padding: '0 4px', borderLeft: '1px solid rgba(255, 255, 255, 0.35)' }}>
                {listItem.primaryUomQty}
              </Col>
            </span>
          </Row>
          <Row
            style={{
              background: listItem.materialType === 'B' ? '#d43030' : '#578aa7',
              padding: '0 0 0 4px',
              borderBottomLeftRadius: '5px',
              borderBottomRightRadius: '5px',
            }}
          >
            <span style={{ color: 'white' }}>
              <Col
                onClick={listItem.materialType === 'A' ||
                  listItem.materialType === 'B'
                  ? null
                  : () => handleSelectCard(listItem)}
                span={18}
                style={{
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {/* L:{listItem.lot}
                <br /> */}
                S:{listItem.supplierName}
              </Col>
              {/* <Col
                onClick={listItem.materialType === 'A' ||
                  listItem.materialType === 'B'
                  ? null
                  : () => handleSelectCard(listItem)}
                span={3}
              >
                <Divider style={{ height: '40px' }} type="vertical" />
              </Col> */}
              <Col
                onClick={listItem.materialType === 'A'
                  ? null
                  : () => openNumberModal(listItem, currentItem)
                }
                span={6}
                style={{
                  background: listItem.materialType === 'B' ? '#d43030' : '#29BECE',
                  // padding: '10px',
                  textAlign: 'center',
                  borderLeft: '1px solid #fff',
                }}
              >
                {listItem.calculateQty === 0
                  ? 0
                  : listItem.calculateQty ||
                  listItem.inputQty ||
                  listItem.expectQty ||
                  0}
              </Col>
              {/* <Col span={6} style={{ textAlign: 'right', overflow: 'hidden', textOverflow: 'ellipsis', padding: '0 4px', borderLeft: '1px solid #fff' }}>
                {listItem.primaryUomQty}
              </Col> */}
            </span>
          </Row>
        </Card>
      </div>
    </div>
  );
};
export default TopicList;
