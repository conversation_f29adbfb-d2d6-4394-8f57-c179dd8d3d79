import { notification } from 'choerodon-ui/pro';

interface ONotificationType {
  top?: number | 24,
  bottom?: number | 24,
  duration?: number | 4.5,
  getContainer?: () => HTMLElement,
  placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight',
  message?: string,
  description?: string,
}

const ONotification = {
  success: (_config: ONotificationType = {}) => {
    return notification.success({
      ..._config,
      // @ts-ignore
      getContainer: () => document.getElementById('operationPlatform') || document.body,
      bottom: _config.bottom || 24,
      duration: _config.duration || 4.5,
      placement: _config.placement || 'bottomRight',
      message: _config.message || '操作成功',
      description: _config.description,
    })
  },
  error: (_config: ONotificationType = {}) => {
    return notification.error({
      ..._config,
      // @ts-ignore
      getContainer: () => document.getElementById('operationPlatform') || document.body,
      bottom: _config.bottom || 24,
      duration: _config.duration || 4.5,
      placement: _config.placement || 'bottomRight',
      message: _config.message || '操作失败',
      description: _config.description,
    })
  },
  warning: (_config: ONotificationType = {}) => {
    return notification.warning({
      ..._config,
      // @ts-ignore
      getContainer: () => document.getElementById('operationPlatform') || document.body,
      bottom: _config.bottom || 24,
      duration: _config.duration || 4.5,
      placement: _config.placement || 'bottomRight',
    })
  },
  info: (_config: ONotificationType = {}) => {
    return notification.info({
      ..._config,
      // @ts-ignore
      getContainer: () => document.getElementById('operationPlatform') || document.body,
      bottom: _config.bottom || 24,
      duration: _config.duration || 4.5,
      placement: _config.placement || 'bottomRight',
    })
  },
}

export default ONotification;
