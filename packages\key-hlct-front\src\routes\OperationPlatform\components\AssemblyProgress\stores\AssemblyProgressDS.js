/**
 * @Description: 数据收集组DS
 * @Author: <EMAIL>
 * @Date: 2023-03-14 10:19:56
 */

import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';

const modelPrompt = 'tarzan.operationPlatform.assemblyProgress';

// tableDS
const tableDS = () => ({
  autoQuery: false,
  autoCreate: false,
  autoQueryAfterSubmit: false,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'materialShowStr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialShowStr`).d('需求物料编码/版本'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('需求物料编码'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('需求物料描述'),
    },
    {
      name: 'demandQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.demandQty`).d('需求数量'),
    },
    {
      name: 'perQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.perQty`).d('单位用量'),
    },
    {
      name: 'assembleProgress',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.assembleProgress`).d('装配进度'),
    },
    {
      name: 'assembleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.assembleQty`).d('装配数量'),
    },
    {
      name: 'scrappedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.scrappedQty`).d('报废数量'),
    },
    {
      name: 'assembleMaterialShowStr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMaterialShowStr`).d('装配物料编码/版本'),
    },
    {
      name: 'assembleMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMaterialCode`).d('装配物料编码'),
    },
    {
      name: 'assembleMaterialRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMaterialRevisionCode`).d('装配物料版本'),
    },
    {
      name: 'assembleMaterialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.assembleMaterialName`).d('装配物料描述'),
    },
  ],
});

const formDS = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: true,
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('生产指令编码'),
    },
    {
      name: 'woMaterialShowStr',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woMaterialShowStr`).d('物料/版本'),
    },
    {
      name: ' woMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}. woMaterialCode`).d('物料'),
    },
    {
      name: 'woRevisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woRevisionCode`).d('版本'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
    },
    {
      name: 'kitQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.kitQty`).d('齐套数量'),
    },
  ],
});

export { tableDS, formDS };
