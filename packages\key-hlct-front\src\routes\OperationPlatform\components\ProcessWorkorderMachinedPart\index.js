/* eslint-disable jsx-a11y/alt-text */
// 加工件（工单+在制品标识）进出站（赋标识）
import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Form, TextField, Button, DataSet, Modal, Select, Tooltip, Output, Table } from 'choerodon-ui/pro';
// import { Icon } from 'hzero-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import position from '@/assets/operationPlatformCard/position.png';
import arrowRight from '@/assets/operationPlatformCard/arrow-right.png';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import lovChoose from '@/assets/operationPlatformCard/lovChoose.svg';
import cardSvg from '@/assets/icons/operation.svg';
import moment from 'moment';
import onSite from '@/assets/operationPlatformCard/onSite.svg';
import codePrint from '@/assets/operationPlatformCard/codePrint.svg';
// import { TemplatePrintButton } from '../../../../components/tarzan-ui';
// import NumberCharts from './App';
import { detailDS, workOrderLovDS } from './stores/MachinedPartDS';
import { useOperationPlatform } from '../../contextsStore';
import { useResizeObserver } from '../../useResizeObserver';
import { CardLayout, ONotification, useRequest } from '../commonComponents';
import { QueryCardList } from '../../services';
import styles from './index.modules.less';
import C7nModal from '../../C7nModal';
import MachinedPartPrint from '../commonComponents/MachinedPartPrint';
import OnSiteProduction from '../commonComponents/OnSiteProduction';


const tenantId = getCurrentOrganizationId();
let selectModal; // 选择工艺步骤弹框
let locatorModal; // 工单弹框
const MachinedPartCard = props => {
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  const timer = useRef(null);
  const [time, setTime] = useState(0);
  const workOrderLovDs = useMemo(() => new DataSet(workOrderLovDS()), []);
  const { enterInfo, workOrderData, dispatch, containerDetail, cardMode } = useOperationPlatform();
  const [workOrderParams, setWorkOrderParams] = useState({}); // 工单参数
  // const [computingTime, setComputingTime] = useState(false); // 是否计算时间
  // const [renderProps, setRenderProps] = useState([]);
  const [trendsNum, setTrendsNum] = useState('');// 趋势数量
  const trendsNumRef = useRef(trendsNum);
  const [formColumns, setFormColumns] = useState(1); // 工单信息列数
  const [printModalShow, setPrintModalShow] = useState(false); // 是否展示打印弹框
  const [siteModalShow, setSiteModalShow] = useState(false); // 是否展示站内弹框

  useEffect(() => {
    trendsNumRef.current = trendsNum;
  }, [trendsNum])

  const { run: queryCardList, loading: queryCardListLoading } = useRequest(QueryCardList(), {
    manual: true,
    needPromise: true,
  });

  useResizeObserver((element, size) => {
    // console.log('Element size:', size, element);
    if (size.width <= 480) {
      setFormColumns(1);
    } else if (size.width > 480 && size.width <= 780) {
      setFormColumns(2);
    } else if (size.width > 780 && size.width <= 1090) {
      setFormColumns(3);
    } else {
      setFormColumns(4);
    }
    // 在这里执行你需要的操作，例如重新布局、更新状态等。
  }, document.querySelector('.ProcessWorkorderMachinedPartForm'));

  // useEffect(() => {
  //   queryCardList({
  //     params: {
  //       cardCode: '3',
  //     },
  //   }).then(res => {
  //     if (res?.length) {
  //       try {
  //         const cardConfig = Object.entries(JSON.parse(res[0].cardConfiguration));
  //         setRenderProps(cardConfig);
  //       } catch (error) {
  //         ONotification.error({ message: 'cardConfiguration字段配置错误！' });
  //       }
  //     }
  //   });
  // }, []);

  // 工单lov传参
  useEffect(() => {
    detailDs.getField('workOrderObj').setLovPara('workcellId', enterInfo?.workStationId);
    detailDs
      .getField('workOrderObj')
      .setLovPara('operationId', enterInfo?.selectOperation?.operationId);
    detailDs
      .getField('workOrderObj')
      .setLovPara('operationName', enterInfo?.selectOperation?.operationName);
    detailDs
      .getField('workOrderObj')
      .setLovPara('operationDesc', enterInfo?.selectOperation?.description);
  }, []);

  useEffect(() => {
    if (workOrderData?.workOrderDataType === 'SchedulingTasks') {
      console.log('workOrderData', workOrderData);
      detailDs.current.set('workOrderObj', workOrderData);
      onFetchWorkOrder(workOrderData)
    }
  }, [workOrderData]);

  // 选择工艺步骤
  const changeSelectValue = value => {
    window.localStorage.setItem('selectKey', value);
  };

  // 清空数据
  const cleanData = () => {
    dispatch({
      type: 'update',
      payload: {
        workOrderData: {},
      },
    });
    setWorkOrderParams({});
    // detailDs.loadData([]);
    // setComputingTime(false);
  };

  const columnWo = [
    {
      name: 'workOrderNum',
    },
    {
      name: 'woRenderQty',
      renderer: ({ record }) => {
        return `${record?.get('completedQty')}/${record?.get('qty')}`;
      },
    },
    {
      name: 'materialInfo',
    },
    {
      name: 'materialName',
    },
    {
      name: 'plantTime',
      width: 300,
      // renderer: ({ record }) => {
      //   return `${moment(record?.get('planStartTime')).format('YYYY-MM-DD HH:mm')}-${moment(record?.get('planEndTime')).format('YYYY-MM-DD HH:mm')}`;
      // },
    },
  ];

  const onRow = ({ record }) => {
    return {
      onClick: () => {
        workOrderLovDs.data.forEach(item => {
          item.isSelected = false;
        });
        record.isSelected = true;
      },
      onDoubleClick: () => {
        locatorModal.close();
        onFetchWorkOrder(record.data);
      },
    };
  };

  const handleFetchLovData = (value, type, ident) => {
    workOrderLovDs.queryDataSet?.current?.reset();
    if (timer.current) {
      clearInterval(timer.current);
      setTime(0);
    }
    if (value === 'click') {
      workOrderLovDs.setQueryParameter('workOrderNum', null);
    } else if (value) {
      workOrderLovDs.setQueryParameter('workOrderNum', value);
    } else {
      workOrderLovDs.setQueryParameter('workOrderNum', null);
      detailDs.loadData([]);
      return;
    }
    workOrderLovDs.setQueryParameter('operationId', enterInfo?.selectOperation?.operationId);
    workOrderLovDs.setQueryParameter('operationName', enterInfo?.selectOperation?.operationName);
    workOrderLovDs.setQueryParameter('operationDesc', enterInfo?.selectOperation?.description);
    workOrderLovDs.setQueryParameter('workcellId', enterInfo?.workStationId);
    workOrderLovDs.query().then(res => {
      if (res && !res.failed) {
        if (res.content.length === 1) {
          onFetchWorkOrder(res.content[0], type, ident);
          return
        }
        if (res.content.length === 0) {
          return ONotification.error({ message: "未查询到WO" });
        }
        locatorModal = C7nModal.open({
          header: (
            <div style={{ display: 'flex' }}>
              <img src={cardSvg} alt="" className="titleIcon" />
              <div className="c7n-pro-modal-title">工单</div>
            </div>
          ),
          destroyOnClose: true,
          closable: false,
          style: {
            width: '95%',
          },
          mask: true,
          contentStyle: {
            background: '#38708F',
          },
          className: styles.workOrderModals,
          children: <Table dataSet={workOrderLovDs} columns={columnWo} onRow={onRow} />,
          okProps: {
            style: {
              background: '#00D4CD !important',
              color: 'white !important',
              borderColor: '#00d4cd !important',
            },
          },
          onOk: () => {
            onFetchWorkOrder(workOrderLovDs.selected[0].data);
          },
        })
      }
    });
  }

  // 扫描工单
  const onFetchWorkOrder = (value, type, ident) => {
    if (timer.current) {
      clearInterval(timer.current);
      setTime(0);
    }
    if (value) {
      const params = {
        ...value,
        workcellId: enterInfo?.workStationId,
        operationId: enterInfo?.selectOperation?.operationId,
        operationName: enterInfo?.selectOperation?.operationName,
        operationDesc: enterInfo?.selectOperation?.description,
      };
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/card-work-order-wip/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && !res.failed) {
          setTime(res.processedTime || 0)
          timer.current = setInterval(() => {
            setTime((prev) => prev + 1);
          }, 1000);
          if (res.routerStepVO5List && res.routerStepVO5List[0]) {
            selectModal = Modal.open({
              title: '选择工艺步骤',
              key: Modal.key(),
              destroyOnClose: true,
              closable: false,
              mask: true,
              style: { width: '450px' },
              contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
              className: styles.machinedPartModal,
              children: (
                <Form className={styles.modalForm} labelLayout="placeholder" labelWidth={80}>
                  <Select label="工艺步骤" onChange={item => changeSelectValue(item)}>
                    {res.routerStepVO5List.map(item => {
                      return (
                        <Select.Option key={item.routerStepId} value={item.routerStepId}>
                          {item.description}/{item.stepName}
                        </Select.Option>
                      );
                    })}
                  </Select>
                </Form>
              ),
              okProps: {
                style: {
                  background: '#1b7efc',
                },
              },
              cancelProps: {
                style: {
                  background: '#50819c',
                  color: 'white',
                },
              },
              onOk: () => {
                const selectKey = Number(window.localStorage.getItem('selectKey'));
                const selectData = res.routerStepVO5List?.filter(
                  item => item.routerStepId === selectKey,
                )[0];
                if (!selectData) {
                  ONotification.error({ message: '请选择工艺步骤' });
                  return false;
                }
                const params = {
                  ...value,
                  workcellId: enterInfo?.workStationId,
                  operationId: enterInfo?.selectOperation?.operationId,
                  operationName: enterInfo?.selectOperation?.operationName,
                  operationDesc: enterInfo?.selectOperation?.description,
                  routerStepFlag: 'Y',
                  routerStepId: selectData?.routerStepId,
                  sequence: selectData?.sequence,
                  stepName: selectData?.stepName,
                };
                return request(
                  `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/card-work-order-wip/ui`,
                  {
                    method: 'POST',
                    body: params,
                  },
                ).then(res => {
                  if (res && !res.failed) {
                    setWorkOrderParams(params);
                    dispatch({
                      type: 'update',
                      payload: {
                        workOrderData: res,
                      },
                    });
                    detailDs.loadData([res]);
                    // setComputingTime(false);
                    selectModal.close();
                  } else {
                    ONotification.error({ message: res.message });
                    return false;
                  }
                });
              },
              onCancel: () => {
                window.localStorage.setItem('selectKey', null);
                selectModal.close();
                cleanData();
                setTimeout(() => {
                  document.querySelector('#workOrderObj').focus();
                }, 100);
              },
            });
          } else {
            setWorkOrderParams(params);
            dispatch({
              type: 'update',
              payload: {
                workOrderData: res,
              },
            });
            detailDs.loadData([res]);
            // setComputingTime(false);
            props.handleAddRecords({
              cardCode: props.cardCode,
              messageType: 'SUCCESS',
              message: `扫描工单${value.workOrderNum}成功`,
            });
            if (type === 'onSite') {
              onFetchProcessed(ident, res);
            }
          }
        } else {
          cleanData();
          ONotification.error({ message: res.message });
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'FAIL',
            message: `${res.message || '扫描工单失败'}`,
          });
        }
      });
    } else {
      cleanData();
    }
  };

  // 扫描在制品
  const onFetchProcessed = (value, cardVO) => {
    // setComputingTime(false);
    if (value) {
      const params = {
        identification: value,
        cardVO: cardVO || workOrderData,
        workcellId: enterInfo?.workStationId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        operationId: enterInfo?.selectOperation?.operationId,
        operationName: enterInfo?.selectOperation?.operationName,
        operationDesc: enterInfo?.selectOperation?.description,
        scanFlag: 'WO_EO',
        prodLineId: enterInfo?.productionLineId,
        containerInfo: {
          containerId: containerDetail?.containerId,
          containerCode: containerDetail?.containerCode,
          containerTypeId: containerDetail?.containerTypeId,
          capacityQty: containerDetail?.capacityQty,
          personalCapacityQty: containerDetail?.personalCapacityQty,
        },
      };
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/eo-scan/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && !res.failed) {
          setTrendsNum('')
          const detailData = detailDs.toData()[0];
          detailDs.loadData([
            {
              ...detailData,
              ...res,
              identificationField: value,
            },
          ]);
          dispatch({
            type: 'update',
            payload: {
              workOrderData: {
                ...detailData,
                ...res,
                identificationField: value,
                cardWorkpiece: 'Y',
              },
            },
          });
          // if (res.processedTime || res.processedTime === 0) {
          //   setComputingTime(true);
          // }
          if (res?.containerLoadErrorMsg) {
            ONotification.warning({
              message: res?.containerLoadErrorMsg,
            });
          }
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'SUCCESS',
            message: `扫描在制品${value}成功`,
          });
        } else {
          onRefreshWorkOrder();
          ONotification.error({ message: res.message });
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'FAIL',
            message: `扫描在制品${value}失败`,
          });
        }
      });
    }
  };

  // 加工完成
  const processCompleted = () => {
    const params = {
      workcellId: enterInfo?.workStationId,
      shiftCode: enterInfo?.shiftCode,
      shiftDate: enterInfo?.shiftDate,
      operationId: enterInfo?.selectOperation?.operationId,
      eoId: workOrderData?.eoId,
      routerStepId: workOrderData?.routerStepId,
      prodLineId: enterInfo?.productionLineId,
      containerInfo: {
        containerId: containerDetail?.containerId,
        containerCode: containerDetail?.containerCode,
        containerTypeId: containerDetail?.containerTypeId,
        capacityQty: containerDetail?.capacityQty,
        personalCapacityQty: containerDetail?.personalCapacityQty,
      },
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/completion-processing/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (!res?.failed) {
        ONotification.success();
        // setComputingTime(false);
        // onRefreshWorkOrder();
        detailDs.current?.set('identificationField', null);
        dispatch({
          type: 'update',
          payload: {
            workOrderData: {
              ...workOrderData,
              cardClear: 'Y',
              completedQty: workOrderData.completedQty + (trendsNumRef.current ? Number(trendsNumRef.current) : 0),
              containerRefreshFlag: res.containerRefreshFlag,
            },
          },
        });
        setTrendsNum(`+${detailDs.toData()[0].eoQty}`)
        if (res?.containerLoadErrorMsg) {
          ONotification.warning({
            message: res?.containerLoadErrorMsg,
          });
        }
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'SUCCESS',
          message: `加工完成${workOrderData.workOrderNum}成功`,
        });
      } else {
        ONotification.error({ message: res.message });
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'FAIL',
          message: `加工完成${workOrderData.workOrderNum}失败`,
        });
      }
    });
  };

  // 退回
  const processReturn = () => {
    const params = {
      workcellId: enterInfo?.workStationId,
      shiftCode: enterInfo?.shiftCode,
      shiftDate: enterInfo?.shiftDate,
      operationId: enterInfo?.selectOperation?.operationId,
      eoId: workOrderData?.eoId,
      routerStepId: workOrderData?.routerStepId,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/step/return/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        ONotification.success();
        // setComputingTime(false);
        onRefreshWorkOrder();
        dispatch({
          type: 'update',
          payload: {
            workOrderData: {
              ...workOrderData,
              cardClear: 'Y',
            },
          },
        });
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'SUCCESS',
          message: `退回${workOrderData.workOrderNum}成功`,
        });
      } else {
        ONotification.error({ message: res.message });
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'FAIL',
          message: `退回${workOrderData.workOrderNum}失败`,
        });
      }
    });
  };

  // 刷新工单
  const onRefreshWorkOrder = () => {
    const detailData = detailDs.toData()[0];
    detailDs.loadData([{ workOrderObj: detailData.workOrderCardVO }]);
    const params = {
      ...workOrderParams,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/card-work-order-wip/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        dispatch({
          type: 'update',
          payload: {
            workOrderData: res,
          },
        });
        detailDs.loadData([res]);
        // setComputingTime(false);
      } else {
        ONotification.error({ message: res.message });
      }
    });
  };

  const getTimes = t => {
    if (!t) {
      return;
    }
    let h = parseInt(String((t / 60 / 60) % 24), 10);
    let m = parseInt(String((t / 60) % 60), 10);
    let s = parseInt(String(t % 60), 10);
    // 三元表达式 补零 如果小于10 则在前边进行补零 如果大于10 则不需要补零
    if (t < 60) {
      s = s < 10 ? `0${s}` : s;
      return `${s}秒`;
    }
    if (t >= 60 && t < 3600) {
      m = m < 10 ? `0${m}` : m;
      s = s < 10 ? `0${s}` : s;
      return `${m}分${s}秒`;
    }
    h = h < 10 ? `0${h}` : h;
    m = m < 10 ? `0${m}` : m;
    s = s < 10 ? `0${s}` : s;
    return `${h}时${m}分${s}秒`;
  };

  const handleOpenPrint = async () => {
    if (!workOrderData.workOrderId || printModalShow) {
      return;
    }
    setPrintModalShow(true)
  }

  const handleCloseStepModal = () => {
    setPrintModalShow(false)
  }

  const machinedPartPrintProps = {
    workOrderData,
    enterInfo,
    handleCloseStepModal,
    printModalShow,
    contentClassName: 'ProcessWorkorderMachinedPartHead',
  }

  const handleOpenSite = async () => {
    setSiteModalShow(true)
  }
  const handleCloseSiteModal = () => {
    setSiteModalShow(false)
  }

  const onSiteProductionProps = {
    catdType: 'ProcessWorkorderMachinedPart',
    enterInfo,
    handleCloseSiteModal,
    siteModalShow,
    contentClassName: 'ProcessWorkorderMachinedPartHead',
    handleFetchLovData,
    onFetchProcessed,
  }
  return (
    <div style={{ width: '100%', height: '100%' }}>
      <CardLayout.Layout spinning={queryCardListLoading}>
        <CardLayout.Header
          className='ProcessWorkorderMachinedPartHead'
          title="进出站（赋标识）"
          help={props?.cardUsage?.remark}
          content={
            <>
              <TextField
                dataSet={detailDs}
                placeholder="请扫描EO"
                id="identificationField"
                name="identificationField"
                onEnterDown={e => onFetchProcessed(e.target.value)}
                prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />}
              />
            </>
          }
          addonAfter={
            <>
              <Button
                onClick={processReturn}
                disabled={!workOrderData?.eoId}
                style={{ background: 'rgba(255, 182, 1, 1)', borderColor: 'rgba(255, 182, 1, 1)' }}
              >
                退回
              </Button>
              <Button color="primary" onClick={processCompleted} disabled={!workOrderData?.eoId}>
                加工完成
              </Button>
            </>
          }
        />
        <CardLayout.Content className='ProcessWorkorderMachinedPartForm'>
          <div
            style={{
              display: workOrderData.currentProcess || workOrderData.nextProcess ? 'block' : 'none',
            }}
            className={styles.customTitle}
          >
            &nbsp;&nbsp;
            <img src={position} alt="" />
            <span style={{ color: 'rgba(51, 241, 255, 1)' }}> {workOrderData.currentProcess}</span>
            &nbsp;&nbsp;
            <img src={arrowRight} alt="" />
            &nbsp;&nbsp;
            <span style={{ color: 'rgba(255, 255, 255, 0.85)' }}>{workOrderData.nextProcess}</span>
          </div>
          {/* <CardCustomizeForm
            renderProps={renderProps}
            responseData={workOrderData}
            computingTime={computingTime}
          /> */}
          <Form dataSet={detailDs} labelWidth={130} columns={cardMode === 'Tile' ? formColumns : 4} style={{ height: '78%', overflow: 'auto' }}>
            {/* <Lov
              style={{ marginRight: '8px' }}
              placeholder='请选择WO'
              suffix={<img src={lovChoose} alt='' style={{height: '19px'}}/>}
              modalProps={{
                contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
                className: styles.workOrderModals,
                okProps: {
                  style: {
                    background: '#1b7efc',
                  },
                },
                cancelProps: {
                  style: {
                    background: '#50819c',
                    color: 'white',
                  },
                },
              }}
              id="workOrderObj"
              name="workOrderObj"
              onChange={onFetchWorkOrder}
            /> */}
            <TextField
              id="workOrderObj"
              name="workOrderNum"
              onChange={(value) => { handleFetchLovData(value) }}
              placeholder="请选择WO"
              suffix={
                <img
                  src={lovChoose}
                  alt=''
                  style={{ height: '19px' }}
                  onClick={() => { handleFetchLovData('click') }}
                />
              }
            />
            <Output name='identification' />
            <Output name='materialCode' />
            <Output name='opProcess' />
            <Output
              name='pitStopDate'
              renderer={({ value }) => {
                return value ? moment(value).format('YYYY-MM-DD HH:mm') : null;
              }}
            />
            <Output
              name='processedTime'
              renderer={() => {
                return time ? getTimes(time) : '';
              }}
            />
            <Output name='customerDesc' />
            <Output name='standardBeat' />
            <Output name='remark' />
            <Output name='materialName' />
            {/* {renderProps.map(item => (
              item[0] !== 'workOrderNum' && <Output name={item[0]}/>
            ))} */}
          </Form>
          {/* <div style={{ fontSize: '14px', display: 'flex', fontFamily: 'auto' }}>
            <span
              style={{
                color: 'rgb(112, 187, 243)',
              }}
            >
              完工数量：
              {workOrderData.completedQty}
            </span>
            <span style={{ color: '#fff', display: eoQty ? 'block': 'none' }}>+{eoQty}</span>
          </div> */}
          {/* <NumberCharts data={workOrderData} /> */}
          <div className={styles.cardFooter}>
            <div className={styles.completeText}>
              <span className={styles.completeNumObj}>完工数量:</span>
              <Tooltip title={workOrderData?.completedQty} theme="light">
                <span className={styles.completeValue}>{workOrderData?.completedQty || ''}</span>
                <span className={styles.completeValueAdd}>{trendsNumRef.current || ''}</span>
              </Tooltip>
            </div>
            <div className={styles.printButton}>
              <div className={styles.buttonContent}>
                <img src={onSite} alt='' />
                <div onClick={handleOpenSite}>站内在制</div>
              </div>
              <div className={styles.buttonContent}>
                <img src={codePrint} alt='' />
                <div onClick={handleOpenPrint}>条码打印</div>
              </div>
            </div>
            {/* <TemplatePrintButton
              printButtonCode="HME.EO_IDENTIFICATION"
              disabled={!workOrderData.workOrderId}
              style={{ color: '#1cdbef', borderColor: 'tranparent' }}
              name="条码打印"
              icon="print"
              printParams={{
                identification: workOrderData.identification,
                materialCode: workOrderData.identification,
                qty: workOrderData.identification,
              }}
            /> */}
          </div>
        </CardLayout.Content>
      </CardLayout.Layout>
      <MachinedPartPrint {...machinedPartPrintProps} />
      <OnSiteProduction {...onSiteProductionProps} />
    </div>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(MachinedPartCard);
