.OperationRecordsTimeline {
  padding: 16px;
  // border: 1px solid white;
  height: 54%;
  overflow: auto;
  font-size: 16px;

  :global {
    .c7n-timeline.c7n-timeline .c7n-timeline-item-last .c7n-timeline-item-content {
      color: white;
    }

    .c7n-timeline-item-pending .c7n-timeline-item-tail {
      display: block;
      border-left: 0.02rem dotted #e8e8e8;
    }

    .c7n-timeline-item-pending .c7n-timeline-item-head {
      background: #387090;
    }

    .c7n-timeline-item-last {
      padding: 0;

      .c7n-timeline-item-tail {
        display: none !important;
      }
    }

    .c7n-timeline-item-content {
      color: white !important;
    }

    .c7n-timeline-item{
      padding: 0 !important;
    }
    .c7n-progress-loading.c7n-progress-loading.c7n-progress-status-normal .c7n-progress-inner circle {
      stroke: white;
    }
  }

  // margin-left: 10px;
  // margin-right: 10px;
  // border: 1px solid white;
  // padding: 10px;
  // z-index: 10;
  // height: calc(100% - 40px);
  // overflow-y: scroll;

  // white-space: nowrap;
  // overflow: hidden;
  // text-overflow: ellipsis;
  // display: inline-block;
  // width: 100%;
  // color: white;
  // padding-right: 10px;
}

.operationMessageTitle{
  width: 100%;
  color: white;
  margin-bottom: 7px;
  padding-left: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.operationMessageTitleRight{
  display: flex;
  align-items: center;
  justify-content: flex-end;
  cursor: pointer;
  padding-right: 16px;
  color: rgba(17, 194, 207, 1);
  img{
    margin-left: 5px;
  }
}

.MessagePromptResult {
  .MessagePromptResultContentSuccess {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-content: center;
    align-items: center;
    flex-wrap: wrap;
    padding: 0 !important;
    :global {
      .c7n-result-icon{
        margin-bottom: 0;
        .icon{
          font-size: 70px;
        }
      }
      .c7n-result-title {
        letter-spacing: 10px;
        font-size: 70px;
        color: rgb(17, 217, 84) !important;
        margin-left: 20px;
        line-height: 1;
        font-weight: 600;
      }
      .c7n-result-subtitle{
        width: 100%;
        font-size: 26px;
        color: white !important;
        margin-top: 20px;
        line-height: 1.5;
      }
    }
  }
  .MessagePromptResultContentError{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-content: center;
    align-items: center;
    flex-wrap: wrap;
    padding: 0 !important;
    :global {
      .c7n-result-icon{
        margin-bottom: 0;
        .icon{
          font-size: 70px;
        }
      }
      .c7n-result-title {
        letter-spacing: 10px;
        font-size: 70px;
        color: rgb(242, 58, 80) !important;
        margin-left: 20px;
        line-height: 1;
        font-weight: 600;
      }
      .c7n-result-subtitle{
        width: 100%;
        font-size: 26px;
        color: white !important;
        margin-top: 20px;
        line-height: 1.5;
      }
    }
  }
}

#MessagePromptResultContent{
  width: 100%;
  height: 30%;
  display: flex;
  align-items: center;
  justify-content: center;
  :global{
    .icon{
      font-size: 70px !important;
    }
  }
}
