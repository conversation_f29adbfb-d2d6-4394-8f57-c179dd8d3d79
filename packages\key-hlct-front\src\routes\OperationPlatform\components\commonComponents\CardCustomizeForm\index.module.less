.cardCustomizeForm {
  width: 100%;
  min-height: calc(100% - 140px);

  .textStyle {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .cardRow {
    width: 100%;

    .cardCol {
      display: inline-flex;
      align-items: center;

      .label {
        position: relative;
        .textStyle;

        &:after {
          content: ':';
          position: absolute;
          right: 0;
        }
      }

      .content {
        flex: 1;
        padding-left: 8px;
        .textStyle;
      }
    }
  }
}
