/**
 * @Description: 模板打印按钮-services
 * @Author: <<EMAIL>>
 * @Date: 2023-07-31 12:56:55
 * @LastEditTime: 2023-08-08 18:42:19
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';
import request from 'utils/request';


const tenantId = getCurrentOrganizationId();

/**
 * 获取打印模板信息
 * @param printButtonCode string
 * @returns
 */
export function FetchPrintTemplate(printButtonCode: string) {
  return {
    url: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-print-functions/print/template/list/ui?printButtonCode=${printButtonCode}`,
    method: 'GET',
  };
}

/**
 * 获取H0打印模板
 * @returns
 */
export function FetchTemplate() {
  return {
    url: `${BASIC.HRPT_COMMON}/v1/${tenantId}/label-prints/view/html`,
    method: 'GET',
  };
}

/**
 * 获取UReport打印模板
 * @returns
 */
export async function fetchReportTemplate(params) {
  return request(`${BASIC.HRPT_COMMON}/v1/${tenantId}/reports/export/${params.templateUuid}/PRINT?docId=${params.docId}`, {
    method: 'POST',
    body: params,
    responseType: 'blob',
  });
}

/**
 * 记录打印次数
 * @returns
 */
export function RecordPrintRecord() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/mt-object-print-record/record`,
    method: 'POST',
  };
}
