.workOrderModals {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .icon-refresh {
      background-color: rgb(56, 112, 143) !important;
      color: white !important;
    }

    .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      border: none !important;
    }

    .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
      // background-color: #3c87ad !important;
      color: white !important;
    }

    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .c7n-pro-btn-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .icon {
      color: white !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
    }

    .c7n-pro-table-cell-fix-left, .c7n-pro-table-cell-fix-right{
      background-image: none !important;
    }
  }
}

.doneStepFlagModals{
  width: 400px;
  max-height: 80%;
  font-size: 1vw !important;
  background-color: rgba(56, 112, 143, 1);
  border: 1px solid #63F2FF;
  color: #fff;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
  z-index: 10;
  .modalTitle{
    width: 100%;
    height: 45px;
    display: flex;
    align-items: center;
    background: linear-gradient(to right, rgba(99, 242, 255, 0.74) 0%, rgba(80, 234, 242, 0.55) 13.43%, rgba(48, 97, 219, 0.01) 100%);
    background-size: cover;
    padding: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    img {
      margin-right: 4px;
    }
    .barCodeText{
      margin-left: 4px;
    }
    .modalRightFooter{
      div{
        display: inline-block;
        cursor: pointer;
        img{
          margin-left: 8px;
        }
      }
    }
    #modalRightFooter{
      :global{
        .c7n-pro-btn.c7n-pro-btn-default{
          background-color: rgba(17, 194, 207, 1) !important;
          color: #fff !important;
          border: none !important;
        }
        .c7n-pro-btn.c7n-pro-btn-default.c7n-pro-btn-disabled{
          background-color: #32617f !important;
          color: #fff !important;
          border: none !important;
        }
      }
    }
  }
  .modalContent{
    padding: 0 15px;
  }
  // 底部
  .modalFooter {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 8px;
  }

  // radio
  // :global{
  //   .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper:not(.c7n-pro-radio-button) .c7n-pro-radio:checked + .c7n-pro-radio-inner{
  //     border-color: #33F1FF !important;
  //     background-color: #fff !important;
  //   }
  //   .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper:not(.c7n-pro-radio-button) .c7n-pro-radio-label{
  //     color: #fff !important;
  //   }
  // }
}

.detailContent{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  overflow: auto;
  .materialLotContent{
    padding: 0 8px;
    margin-bottom: 8px;
    :global{
      tr{
        td{
          width: 150px !important;
        }
      }
    }
  }
  .detailContentTop{
    display: flex;
    flex-wrap: wrap;
  }
}
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  border-radius: 14px;
  background: #88a9bc;
}
.cardFooter{
  width: 100%;
  height: 50px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding: 0 16px;
  .completeText{
    // display: flex;
    // align-items: center;
    color: #fff;
    .completeTextName{
      margin-right: 4px;
      color: #33F1FF;
    }
  }
  .printButton{
    width: 50%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 8px;
    color: #33F1FF;
    img {
      margin-right: 4px;
    }
    div{
      cursor: pointer;
    }
  }
}

.printLightRow{
  td:last-child{
    position: relative;
    &::before{
      content: '';
      width: 34px;
      height: 34px;
      position: absolute;
      background: url('../../../../assets/operationPlatformCard/printed.png') no-repeat;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}

.againLightRow{
  td:last-child{
    position: relative;
    &::before{
      content: '';
      width: 34px;
      height: 34px;
      position: absolute;
      background: url('../../../../assets/operationPlatformCard/unprinted.png') no-repeat;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
}


#processReportingRecords{
  :global{
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-disabled:not(.c7n-pro-checkbox-button) .c7n-pro-checkbox:checked + .c7n-pro-checkbox-inner{
      background-color: #608da5 !important;
      border-color: transparent !important;
      // border-radius: 50% !important;
    }
    // .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper:not(.c7n-pro-checkbox-button) .c7n-pro-checkbox:checked + .c7n-pro-checkbox-inner{
    //   border-radius: 50% !important;
    // }
    .c7n-pro-checkbox-wrapper.c7n-pro-checkbox-wrapper{
      font-size: unset !important;
      color: #33F1FF !important;
    }
  }
}

.workOrderTextModals{
  :global{
    .c7n-pro-modal-header {
      display: inline-flex;
      align-items: center;
      height: 42px;
      overflow: hidden;
      width: 100%;
      padding: 0 8px !important;
      background: linear-gradient(
        172.09deg,
        rgba(99, 242, 255, 0.74) 0%,
        rgba(80, 234, 242, 0.55) 23.43%,
        rgba(75, 214, 239, 0.47) 34.52%,
        rgba(48, 97, 219, 0.01) 100%
      );
      .titleIcon {
        margin-right: 8px;
      }
      .c7n-pro-modal-title {
        display: inline-flex;
        align-items: center;
        font-size: 16px;
        color: white;
      }
    }
    .c7n-pro-modal-body {
      min-height: 400px !important;
      max-height: 550px !important;
      padding: 0 !important;
      .c7n-pro-table-professional-query-bar-button {
        margin-right: 16px;
      }
      .c7n-pro-table-pagination{
        margin-right: 16px;
      }
    }
  }
}
