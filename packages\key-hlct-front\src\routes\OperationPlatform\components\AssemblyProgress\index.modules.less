.assemblyProgress {
  .qty-box {
    position: relative;
    height: 40px;
    :global {
      .qty-bg {
        padding: 10px 0;
        width: 100%;
        height: 100%;
        .qty-bg-box {
          width: 100%;
          height: 100%;
          overflow: hidden;
          border-radius: 99px;
          background-color: rgba(255, 255, 255, 0.5);
          .qty-bg-inner {
            background-color: rgba(17, 194, 207, 1);
            height: 100%;
          }
        }
      }
      .qty-front {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        margin: auto;
        z-index: 1;
      }
    }
  }
  :global{
    #assemblyProgressTable{
      .c7n-pro-table.c7n-pro-table .c7n-pro-table-tbody .c7n-pro-table-row .c7n-pro-table-cell{
        background-color: #38708F !important;
        border: 1px solid rgba(255, 255, 255, 0.5) !important;
      }
      .c7n-pro-table:not(.c7n-pro-table-bordered) .c7n-pro-table-tbody{
        border: 1px solid rgba(255, 255, 255, 0.5) !important;
      }
      .c7n-pro-table:not(.c7n-pro-table-bordered) .c7n-pro-table-thead.c7n-pro-table-thead .c7n-pro-table-cell {
        background-color: #2A6382 !important;
        color: #33F1FF !important;
        border: 1px solid rgba(255, 255, 255, 0.5) !important;
        font-size: 1vw !important;
      }
    }
  }
}
