// 机台物料
import React, { useState, useMemo, useEffect } from 'react';
import { DataSet, Table, Row, Col, TextField, Form, Button, Switch } from 'choerodon-ui/pro';
import { Badge } from 'choerodon-ui';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { Record } from 'choerodon-ui/dataset';
import intl from 'utils/intl';
import notification from 'utils/notification';
import lovChoose from '@/assets/operationPlatformCard/lovChoose.svg';
import cardSvg from '@/assets/icons/operation.svg';
import formatterCollections from 'utils/intl/formatterCollections';
import { containerTypeDS, locatorDS } from './stores/ContainerDS';
import { CardLayout } from '../commonComponents';
import C7nModal from '../../C7nModal';
import styles from './index.modules.less';

interface queryContainerModalProps {
  queryDs: DataSet;
  enterInfo: object;
  containerTypeList: any,
  onConfirm: (record: Record) => void;
}
let locatorModal;
let containerModal;
const QueryContainerModal = (props: queryContainerModalProps) => {
  const Modal = C7nModal;
  const containerTypeDs = useMemo(() => new DataSet(containerTypeDS()), []);
  const locatorDs = useMemo(() => new DataSet(locatorDS()), []);
  const { queryDs, containerTypeList, enterInfo, onConfirm } = props;
  const [loading, setLoading] = useState(false);
  const [moreQuery, setMoreQuery] = useState(false);

  useEffect(() => {
    queryDs.queryDataSet!.current?.reset();
  }, []);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  }, []);

  const listener = flag => {
    // 列表交互监听
    if (queryDs) {
      const handlerQuery = flag
        ? queryDs.queryDataSet!.addEventListener
        : queryDs.queryDataSet!.removeEventListener;
      // 查询条件更新时操作
      handlerQuery.call(queryDs.queryDataSet, 'update', handleQueryDataSetUpdate);
    }
  };

  // 查询条件更新时操作
  const handleQueryDataSetUpdate = async () => {
    queryDs.query();
  };

  const toContainerTypeName = (value) => {
    return containerTypeList.filter((item: any) => item.containerTypeCode === value)[0]?.containerTypeDescription;
  };

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'containerCode',
        align: ColumnAlign.center,
        width: 180,
      },
      {
        name: 'containerName',
        align: ColumnAlign.center,
        width: 180,
      },
      {
        name: 'containerTypeCode',
        align: ColumnAlign.center,
        width: 150,
        renderer: ({ value }) => toContainerTypeName(value),
      },
      {
        name: 'locatorCode',
        align: ColumnAlign.center,
        width: 150,
      },
      {
        name: 'statusDesc',
        align: ColumnAlign.center,
        width: 80,
      },
      {
        name: 'createdByName',
        align: ColumnAlign.center,
        width: 120,
      },
      {
        name: 'creationDate',
        align: ColumnAlign.center,
        width: 180,
      },
      {
        name: 'emptyContainerFlag',
        align: ColumnAlign.center,
        width: 120,
        renderer: ({ value }) => (
          <Badge
            style={{ color: 'white' }}
            status={value === 'Y' ? 'success' : 'error'}
            text={
              value === 'Y'
                ? intl.get(`tarzan.common.label.yes`).d('是')
                : intl.get(`tarzan.common.label.no`).d('否')
            }
          />
        ),
      },
    ];
  }, [queryDs]);

  const columnWo = [
    {
      name: 'containerTypeCode',
    },
    {
      name: 'containerTypeDescription',
    },
  ];

  const onRow = ({ record }) => {
    return {
      onClick: () => {
        containerTypeDs.data.forEach(item => {
          item.isSelected = false;
        });
        record.isSelected = true;
      },
      onDoubleClick: () => {
        containerModal.close();
        queryDs.queryDataSet!.current?.set('containerTypeId', record.get('containerTypeId'));
        queryDs.queryDataSet!.current?.set('containerTypeCode', record.get('containerTypeCode'));
      }
    };
  };

  const handleFetchLovData = (value?) => {
    containerTypeDs.queryDataSet?.current?.reset();
    if(value === 'click'){
      containerTypeDs.setQueryParameter('containerTypeCode', null);
    }else if(value){
      containerTypeDs.setQueryParameter('containerTypeCode', value);
    }else{
      containerTypeDs.setQueryParameter('containerTypeCode', null);
      queryDs.queryDataSet!.current?.set('containerTypeId', null);
      queryDs.queryDataSet!.current?.set('containerTypeCode', null);
      return;
    }
    containerTypeDs.query().then(res => {
      if(res && !res.failed){
        if(res.content.length === 1){
          queryDs.queryDataSet!.current?.set('containerTypeId', res.content[0].containerTypeId);
          queryDs.queryDataSet!.current?.set('containerTypeCode', res.content[0].containerTypeCode);
          return
        }
        if(res.content.length === 0){
          return notification.error({ 
            message: intl.get(`tarzan.common.notification.noContainerTypeFound`).d('未查询到容器类型')
          });
        }
        containerModal = Modal.open({
          header: (
            <div style={{ display: 'flex' }}>
              <img src={cardSvg} alt="" className="titleIcon" />
              <div className="c7n-pro-modal-title">
                {intl.get(`tarzan.common.label.containerType`).d('容器类型')}
              </div>
            </div>
          ),
          destroyOnClose: true,
          closable: false,
          style: {
            width: '70%',
          },
          mask: true,
          contentStyle: {
            background: '#38708F',
          },
          className: styles.workOrderTextModals,
          children: <Table dataSet={containerTypeDs} columns={columnWo} onRow={onRow}/>,
          okProps: {
            style: {
              background: '#00D4CD !important',
              color: 'white !important',
              borderColor: '#00d4cd !important',
            },
          },
          onOk: () => {
            queryDs.queryDataSet!.current?.set('containerTypeId', containerTypeDs.selected[0].get('containerTypeId'));
            queryDs.queryDataSet!.current?.set('containerTypeCode', containerTypeDs.selected[0].get('containerTypeCode'));
          },
        })
      }
    });
  }

  const columnLocator = [
    {
      name: 'locatorCode',
    },
    {
      name: 'locatorName',
    },
  ];

  const onLocatorRow = ({ record }) => {
    return {
      onClick: () => {
        locatorDs.data.forEach(item => {
          item.isSelected = false;
        });
        record.isSelected = true;
      },
      onDoubleClick: () => {
        locatorModal.close();
        queryDs.queryDataSet!.current?.set('locatorId', record.get('locatorId'));
        queryDs.queryDataSet!.current?.set('locatorCode', record.get('locatorCode'));
      }
    };
  };

  const handleFetchLocatorLovData = (value?) => {
    locatorDs.queryDataSet?.current?.reset();
    if(value === 'click'){
      locatorDs.setQueryParameter('locatorCode', null);
    }else if(value){
      locatorDs.setQueryParameter('locatorCode', value);
    }else{
      locatorDs.setQueryParameter('locatorCode', null);
      queryDs.queryDataSet!.current?.set('locatorId', null);
      queryDs.queryDataSet!.current?.set('locatorCode', null);
      return;
    }
    locatorDs.setQueryParameter('workcellId', enterInfo?.workStationId);
    locatorDs.setQueryParameter('siteId', enterInfo?.siteId);
    locatorDs.setQueryParameter('organizationId', enterInfo?.productionLineId);
    locatorDs.query().then(res => {
      if(res && !res.failed){
        if(res.content.length === 1){
          queryDs.queryDataSet!.current?.set('locatorId', res.content[0].locatorId);
          queryDs.queryDataSet!.current?.set('locatorCode', res.content[0].locatorCode);
          return
        }
        if(res.content.length === 0){
          return notification.error({ message: intl.get(`tarzan.common.notification.noStorageLocationFound`).d('未查询到库位') });
        }
        locatorModal =Modal.open({
          header: (
            <div style={{ display: 'flex' }}>
              <img src={cardSvg} alt="" className="titleIcon" />
              <div className="c7n-pro-modal-title">
                {intl.get(`tarzan.common.label.storageLocation`).d('货位')}
              </div>
            </div>
          ),
          destroyOnClose: true,
          closable: false,
          style: {
            width: '70%',
          },
          mask: true,
          contentStyle: {
            background: '#38708F',
          },
          className: styles.workOrderTextModals,
          children: <Table dataSet={locatorDs} columns={columnLocator} onRow={onLocatorRow}/>,
          okProps: {
            style: {
              background: '#00D4CD !important',
              color: 'white !important',
              borderColor: '#00d4cd !important',
            },
          },
          onOk: () => {
            queryDs.queryDataSet!.current?.set('locatorId', locatorDs.selected[0].get('locatorId'));
            queryDs.queryDataSet!.current?.set('locatorCode', locatorDs.selected[0].get('locatorCode'));
          },
        })
      }
    });
  }

  /**
   * 自定义搜索
   * @param {*} props
   * @returns ReactNode
   */
  const renderBar = queryDataSet => {
    return (
      <div style={{ display: 'flex', marginBottom: '10px', alignItems: 'flex-start' }}>
        <div>
          <Row>
            <Col span={8}>
              <Form dataSet={queryDataSet}>
                <TextField name="containerCode" />
              </Form>
            </Col>
            <Col span={8}>
              <Form dataSet={queryDataSet}>
                <TextField name="containerName" />
              </Form>
            </Col>
            <Col span={8}>
              <Form dataSet={queryDataSet}>
                {/* <Lov
                  name="containerTypeLov"
                  suffix={<img src={lovChoose} alt="" />}
                  modalProps={{
                    contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
                    className: 'enterModal',
                    okProps: {
                      style: {
                        background: '#1b7efc',
                      },
                    },
                    cancelProps: {
                      style: {
                        background: '#50819c',
                        color: 'white',
                      },
                    },
                  }}
                /> */}
                <TextField
                  name="containerTypeCode"
                  onChange={(value) => {handleFetchLovData(value)}}
                  suffix={
                    <img
                      src={lovChoose}
                      alt=''
                      style={{height: '19px'}}
                      onClick={() => {handleFetchLovData('click')}}
                    />
                  }
                />
              </Form>
            </Col>
          </Row>
          <Row style={{ display: !moreQuery ? 'none' : '' }}>
            <Col span={8}>
              <Form dataSet={queryDataSet}>
                {/* <Lov
                  name="locatorLov"
                  suffix={<img src={lovChoose} alt="" />}
                  modalProps={{
                    contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
                    className: 'enterModal',
                    okProps: {
                      style: {
                        background: '#1b7efc',
                      },
                    },
                    cancelProps: {
                      style: {
                        background: '#50819c',
                        color: 'white',
                      },
                    },
                  }}
                /> */}
                <TextField
                  name="locatorCode"
                  onChange={(value) => {handleFetchLocatorLovData(value)}}
                  suffix={
                    <img
                      src={lovChoose}
                      alt=''
                      style={{height: '19px'}}
                      onClick={() => {handleFetchLocatorLovData('click')}}
                    />
                  }
                />
              </Form>
            </Col>
            <Col span={8}>
              <Form dataSet={queryDataSet}>
                <Switch name="emptyContainerFlag" />
              </Form>
            </Col>
          </Row>
        </div>
        <div style={{ flexShrink: 0, marginTop: '10px' }}>
          <Button
            onClick={() => {
              setMoreQuery(!moreQuery);
            }}
          >
            {!moreQuery ? intl.get(`tarzan.common.button.moreQuery`).d('更多查询') : intl.get(`tarzan.common.button.collapseQuery`).d('收起查询')}
          </Button>
          <Button
            onClick={() => {
              queryDataSet.current.reset();
            }}
          >
            {intl.get(`tarzan.common.button.reset`).d('重置')}
          </Button>
          <Button
            color={ButtonColor.primary}
            onClick={() => {
              queryDs.query();
            }}
          >
            {intl.get(`tarzan.common.button.query`).d('查询')}
          </Button>
        </div>
      </div>
    );
  };


  const handleDoubleClick = (record)=>{
    onConfirm(record);
  };

  return (
    <CardLayout.Layout spinning={loading} className={styles.container}>
      <CardLayout.Content style={{ position: 'relative' }}>
        <Table
          dataSet={queryDs}
          columns={columns}
          queryBar={({ queryDataSet }) => renderBar(queryDataSet)}
          onRow={({ record }) => {
            return {
              onDoubleClick: () => {
                handleDoubleClick(record);
              },
            };
          }}
        />
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ 
  code: ['model.org.monitor'] 
})(QueryContainerModal);
