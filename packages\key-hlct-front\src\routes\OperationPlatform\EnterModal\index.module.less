.operationPlatformEnter {
  display: contents;
  :global {
    .page-head {
      background-color: #38708f !important;
    }
    .page-container {
      border-radius: 0px !important;
    }
    .page-content-wrap {
      margin: 0 !important;
    }
    .page-head.page-head .page-head-title {
      color: rgba(1, 225, 239, 1) !important;
      padding-left: 47% !important;
      position: absolute !important;
      font-size: 20px !important;
      font-weight: 700 !important;
    }
    .cardBackground {
      position: absolute;
      // top: 100;
      right: 0;
      height: 450px;
      width: 800px;
    }
    .footerBackground {
      height: 95%;
      width: 100%;
    }
  }
}
.operationPlatformEnterModal {
  :global {
    // .c7n-pro-modal-header {
    //   background: linear-gradient(
    //     179.69deg,
    //     rgba(99, 242, 255, 0.74) 0%,
    //     rgba(80, 234, 242, 0.55) 23.43%,
    //     rgba(48, 97, 219, 0.01) 100%
    //   ) !important;
    //   opacity: 1;
    //   padding: 8px 16px 8px !important;
    // }
    // .c7n-pro-modal-title {
    //   color: white !important;
    //   margin-left: 8px;
    // }
    .c7n-pro-modal-header {
      display: inline-flex;
      align-items: center;
      height: 42px;
      overflow: hidden;
      width: 100%;
      padding: 0 16px;
      background: linear-gradient(
        172.09deg,
        rgba(99, 242, 255, 0.74) 0%,
        rgba(80, 234, 242, 0.55) 23.43%,
        rgba(75, 214, 239, 0.47) 34.52%,
        rgba(48, 97, 219, 0.01) 100%
      );
    }
    .c7n-pro-modal-title {
      display: inline-flex;
      align-items: center;
      font-size: 16px;
      color: white;

      .titleIcon {
        margin-right: 8px;
      }
    }
    .c7n-pro-modal-body {
      min-height: 400px !important;
    }
  }
}

.enterModalForm {
  :global {
    .c7n-pro-field-label {
      color: white !important;
      font-size: 18px;
    }
    .c7n-pro-output-wrapper {
      color: white !important;
    }
    .c7n-pro-input {
      color: white !important;
      // border-color: #50819c !important;
      padding-left: 40px !important;
    }
    .c7n-pro-input-wrapper {
      color: white !important;
      background: #50819c !important;
    }
    .c7n-pro-radio-wrapper {
      color: white !important;
      background: #50819c !important;
    }
    .c7n-pro-radio-inner {
      color: white !important;
      background: #50819c !important;
      border-color: #00d4cd !important;
      &::after {
        color: #00d4cd !important;
      }
    }
    .c7n-pro-radio-label {
      color: white !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-radio:checked + .c7n-pro-radio-inner {
      color: #00d4cd !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-radio-wrapper.c7n-pro-radio-wrapper.c7n-pro-radio-button:not(.c7n-pro-table-customization-select-view-option)
      .c7n-pro-radio:checked
      + .c7n-pro-radio-inner {
      color: #00d4cd !important;
      border-color: #00d4cd !important;
    }
    .c7n-pro-select-wrapper.c7n-pro-select-wrapper label input {
      font-size: 20px !important;
      padding-left: 40px !important;
    }
  }
}
.tabContent {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden auto;
  gap: 12px;

  .previewImgCard {
    position: relative;
    width: 120px;
    height: 50px;
    box-shadow: 0 6px 9px 0 #19567491 !important;
    cursor: pointer;
    overflow: hidden;
    background-color: rgba(69, 190, 255, 0.28);
    border-radius: 5px;

    // img {
    //   width: 100%;
    //   height: 100%;
    // }

    .cardName {
      // position: absolute;
      font-size: 12px;
      text-align: center;
      width: 100%;
      //  bottom: 0;
      z-index: 10;
      height: 25px;
      line-height: 25px;
      color: #fff;
      // background: linear-gradient(90deg, rgba(56, 112, 143, 0) 0%, rgba(29, 73, 97, 1) 49.05%, rgba(63, 117, 158, 0) 100%);
    }

    .cardSelect {
      width: 40px;
      height: 40px;
      background-color: #4be3ee;
      border-radius: 100%;
      position: absolute;
      top: -22px;
      right: -18px;

      .squareOne {
        width: 11px;
        height: 7px;
        background-color: #fff;
        position: absolute;
        bottom: 9px;
        left: 10px;
        border-radius: 2px;
        transform: rotateZ(-45deg);

        .squareTwo {
          width: 17px;
          height: 12px;
          background-color: #4be3ee;
          position: absolute;
          bottom: 3px;
          left: 3px;
          border-radius: 2px;
        }
      }
    }

    .cardSelect2 {
      width: 40px;
      height: 40px;
      background-color: rgba(255, 216, 110, 1);
      border-radius: 100%;
      position: absolute;
      top: -22px;
      right: -18px;

      .squareOne {
        width: 11px;
        height: 7px;
        background-color: #fff;
        position: absolute;
        bottom: 9px;
        left: 10px;
        border-radius: 2px;
        transform: rotateZ(-45deg);

        .squareTwo2 {
          width: 17px;
          height: 12px;
          background-color: rgba(255, 216, 110, 1);
          position: absolute;
          bottom: 3px;
          left: 3px;
          border-radius: 2px;
        }
      }
    }
  }

  .previewImgCardSelected {
    .previewImgCard();
    background-color: rgb(37, 185, 196);
  }
}

.enterModal {
  :global {
    .c7n-pro-modal-header {
      background: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .c7n-pro-table-wrapper.c7n-pro-table-wrapper
      .c7n-pro-table
      .c7n-pro-table-content
      .c7n-pro-table-row {
      height: 45px !important;
    }

    .icon-refresh {
      background-color: rgb(91, 136, 160) !important;
      color: white !important;
    }

    .c7n-pro-modal-content
      .c7n-pro-modal-body
      .c7n-spin-nested-loading
      .c7n-spin-container
      .c7n-pro-table-content
      .c7n-pro-table-thead
      .c7n-pro-table-cell {
      background-color: #3c87ad !important;
      color: white !important;
      border: none !important;
      font-size: 17px !important;
    }

    .c7n-pro-modal-content
      .c7n-pro-modal-body
      .c7n-spin-nested-loading
      .c7n-spin-container
      .c7n-pro-table-content
      .c7n-pro-table-tbody
      .c7n-pro-table-cell {
      // background-color: #3c87ad !important;
      color: white !important;
      font-size: 17px !important;
    }

    .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .c7n-pro-btn-wrapper {
      background-color: #3c87ad !important;
      color: white !important;
    }

    .icon {
      color: white !important;
    }

    .c7n-pro-pagination-perpage {
      color: white !important;
    }

    .c7n-pro-pagination-page-info {
      color: white !important;
    }
  }
}

.workOrderModals{
  :global{
    .c7n-pro-modal-header {
      display: inline-flex;
      align-items: center;
      height: 42px;
      overflow: hidden;
      padding: 0 8px !important;
      width: 100%;
      background: linear-gradient(
        172.09deg,
        rgba(99, 242, 255, 0.74) 0%,
        rgba(80, 234, 242, 0.55) 23.43%,
        rgba(75, 214, 239, 0.47) 34.52%,
        rgba(48, 97, 219, 0.01) 100%
      );
      .titleIcon {
        margin-right: 8px;
      }
      .c7n-pro-modal-title {
        display: inline-flex;
        align-items: center;
        font-size: 16px;
        color: white;
      }
    }
    .c7n-pro-modal-body {
      min-height: 400px !important;
      padding: 0 !important;
      .c7n-pro-table-professional-query-bar-button {
        margin-right: 16px;
      }
      .c7n-pro-table-pagination{
        margin-right: 16px;
      }
    }
  }
}
