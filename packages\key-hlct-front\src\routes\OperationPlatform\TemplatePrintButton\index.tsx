/**
 * @Description: 模板打印按钮-与打印模板配置配套使用
 * @Author: <<EMAIL>>
 * @Date: 2023-07-31 16:13:12
 * @LastEditTime: 2023-08-12 17:09:14
 * @LastEditors: <<EMAIL>>
 */
import React, {
  useCallback,
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { useModal, Button } from 'choerodon-ui/pro';
import { Collapse } from 'choerodon-ui';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import notification from 'utils/notification';
import { Button as PermissionButton } from 'components/Permission';
import { getCurrentOrganizationId } from 'utils/utils';
import { useRequest } from '../components/commonComponents';
import PrintElement from '../../../components/tarzan-ui/PrintButton/PrintElement';
import {
  FetchPrintTemplate,
  FetchTemplate,
  RecordPrintRecord,
  fetchReportTemplate,
} from './services';
import noData from '@/assets/icons/templatePrintButton/noData.png';
import styles from './index.module.less';

const tenantId = getCurrentOrganizationId();
const { Panel } = Collapse;

interface PrintButtonProps {
  ref?: any;
  style?: any;
  icon: string;
  // 打印按钮编码
  printButtonCode: string;
  disabled: boolean;
  // 权限按钮配置
  permissionList?: any[];
  // 打印时使用的参数
  printParams: {
    [key: string]: any;
  };
  // 记录打印次数时使用的参数，不传则不记录
  printRecordParams?: {
    printObjectIds: any;
    printObjectType: string;
  };
  // 展示名称
  name?: string;
  printCallback?: () => any;
}

interface PrintTemplateProps {
  parameter?: string[];
  printChannel: 'FR' | 'H0' | 'REPORT';
  printChannelPath: string;
  // printChannelPath: "http://***********:8087/webroot",
  printTemplateCode: string;
  printTemplateUuid: string;
  // printTemplateCode: "STANDARD/materialLot.cpt",
  printTemplateGroup: string;
  printTemplateName: string;
}

interface PrintTemplateGroupProps {
  printTemplateGroup: string;
  printTemplateList: PrintTemplateProps[];
}

interface PrintTemplateResponse {
  description: any;
  printButtonCode: string;
  printTemplateGroupList?: PrintTemplateGroupProps[] | null;
}

const _initialData: PrintTemplateResponse = {
  description: null,
  printButtonCode: '',
  printTemplateGroupList: null,
};

const PrintButton = observer(
  forwardRef((props: PrintButtonProps, ref) => {
    const {
      style,
      printButtonCode,
      disabled,
      permissionList = [],
      printParams,
      printRecordParams,
      name,
      icon,
      printCallback,
    } = props;

    const Modal = useModal();
    const modalRef = useRef<any>(null);
    const [selectedTemplate, setSelectedTemplate] = useState<PrintTemplateProps>();
    const {
      data: printTemplate,
      error: templateError,
    }: { data: PrintTemplateResponse; error: string | null } = useRequest(
      FetchPrintTemplate(printButtonCode),
      { initialData: _initialData },
    );
    const fetchTemplate = useRequest(FetchTemplate(), { manual: true, needPromise: true });
    const recordPrintRecord = useRequest(RecordPrintRecord(), { manual: true, needPromise: true });

    useEffect(() => {
      if (!selectedTemplate || !modalRef.current) {
        return;
      }
      const _printTemplateGroupList = printTemplate?.printTemplateGroupList || [];
      modalRef.current.update({
        title: intl.get('tarzan.common.button.printSelect').d('打印模板选择'),
        contentStyle: {
          background: '#38708F',
        },
        className: styles['data-contariner'],
        children: (
          <div id={styles.printButton}>
            {!!_printTemplateGroupList.length && (
              <Collapse
                collapsible="icon"
                bordered={false}
                defaultActiveKey={_printTemplateGroupList.map(item => item.printTemplateGroup)}
              >
                {_printTemplateGroupList.map(templateGroup => {
                  return (
                    <Panel
                      key={templateGroup.printTemplateGroup}
                      header={templateGroup.printTemplateGroup}
                    >
                      {templateGroup.printTemplateList.map(template => {
                        return (
                          <Button
                            className={
                              template.printTemplateCode === selectedTemplate?.printTemplateCode
                                ? styles['selected-button']
                                : ''
                            }
                            onClick={() => {
                              setSelectedTemplate(template);
                            }}
                          >
                            {template.printTemplateName}
                          </Button>
                        );
                      })}
                    </Panel>
                  );
                })}
              </Collapse>
            )}
            {!_printTemplateGroupList.length && (
              <div className={styles['nodata-contariner']}>
                <img src={noData} alt="nodata" />
                <p>{intl.get('tarzan.common.info.noData').d('暂无数据')}</p>
              </div>
            )}
          </div>
        ),
        okText: intl.get('tarzan.common.button.print').d('打印'),
        okProps: { disabled: false },
        autoCenter: true,
        style: {
          width: 680,
        },
        onOk: () => {
          return doPrint(selectedTemplate);
        },
      });
    }, [selectedTemplate]);

    useImperativeHandle(ref, () => ({
      print: () => {
        handleClickPrintBtn();
      },
    }));

    const handleClickPrintBtn = useCallback(() => {
      const _printTemplateGroupList = printTemplate?.printTemplateGroupList || [];
      if (
        _printTemplateGroupList.length === 1 &&
        _printTemplateGroupList[0].printTemplateList.length === 1
      ) {
        // 只有一条数据
        doPrint(_printTemplateGroupList[0].printTemplateList[0]);
        return;
      }
      setSelectedTemplate(undefined);
      modalRef.current = Modal.open({
        title: intl.get('tarzan.common.button.printSelect').d('打印模板选择'),
        contentStyle: {
          background: '#38708F',
        },
        className: styles['data-contariner'],
        children: (
          <div id={styles.printButton}>
            {!!_printTemplateGroupList.length && (
              <Collapse
                collapsible="icon"
                bordered={false}
                defaultActiveKey={_printTemplateGroupList.map(item => item.printTemplateGroup)}
              >
                {_printTemplateGroupList.map(templateGroup => {
                  return (
                    <Panel
                      key={templateGroup.printTemplateGroup}
                      header={templateGroup.printTemplateGroup}
                    >
                      {templateGroup.printTemplateList.map(template => {
                        return (
                          <Button
                            className={
                              template.printTemplateCode === selectedTemplate?.printTemplateCode
                                ? styles['selected-button']
                                : ''
                            }
                            onClick={() => {
                              setSelectedTemplate(template);
                            }}
                          >
                            {template.printTemplateName}
                          </Button>
                        );
                      })}
                    </Panel>
                  );
                })}
              </Collapse>
            )}
            {!_printTemplateGroupList.length && (
              <div className={styles['nodata-contariner']}>
                <img src={noData} alt="nodata" />
                <p>{intl.get('tarzan.common.info.noData').d('暂无数据')}</p>
              </div>
            )}
          </div>
        ),
        okText: intl.get('tarzan.common.button.print').d('打印'),
        okProps: { disabled: true },
        autoCenter: true,
        style: {
          width: 680,
        },
      });
    }, [Modal, printTemplate, printParams, printRecordParams]);

    const doPrint = useCallback(
      (template: PrintTemplateProps) => {
        switch (template.printChannel) {
          case 'FR':
            return doFRPrint(template);
          case 'REPORT':
            return doReportPrint(template);
          case 'H0':
            return doH0Print(template);
          default:
        }
      },
      [printParams, printRecordParams],
    );

    const doFRPrint = useCallback(
      (template: PrintTemplateProps) => {
        // 初始化帆软
        return initFRPlug(template).then(() => {
          // 帆软打印
          FRPrint(template);
        });
      },
      [printParams, printRecordParams],
    );

    /**
     * 初始化帆软打印资源
     */
    const initFRPlug = useCallback((template: PrintTemplateProps) => {
      return new Promise<void>((resolve, reject) => {
        try {
          // @ts-ignore
          if (window.FR) {
            // 已注册过帆软打印插件，不再重复注册
            resolve();
            return;
          }
          const jsUrlList = [
            `${template.printChannelPath}/decision/view/report?op=emb&resource=finereport.js`,
            `${template.printChannelPath}/decision/view/report?op=resource&resource=/com/fr/web/core/js/socket.io.js`,
            `${template.printChannelPath}/decision/view/report?op=resource&resource=/com/fr/web/core/js/jquery.watermark.js`,
          ];
          const cssUrl = `${template.printChannelPath}/decision/view/report?op=emb&resource=finereport.css`;
          for (let i = 0; i < jsUrlList.length; i++) {
            const element = jsUrlList[i];
            const script = document.createElement('script');
            script.src = element;
            script.async = true;
            document.body.appendChild(script);
          }
          const link = document.createElement('link');
          link.href = cssUrl;
          link.rel = 'stylesheet';
          link.type = 'text/css';
          document.body.appendChild(link);
          setTimeout(() => {
            resolve();
          }, 1000);
        } catch (error) {
          reject(error);
        }
      });
    }, []);

    const FRPrint = selectItem => {
      // @ts-ignore
      if (!window.FR) {
        notification.error({
          message: intl.get('tarzan.common.message.initFRPrintFailed').d('打印插件初始化失败'),
        });
        return;
      }
      const printurl = `${selectItem.printChannelPath}/decision/view/report`;

      const _printParams = {
        reportlet: selectItem.printTemplateCode,
        ...printParams,
      };

      const reportlets = JSON.stringify([_printParams]);
      // @ts-ignore
      window.FR.doURLPrint({
        printUrl: printurl,
        isPopUp: false, // 是否弹出设置窗口 true/false 弹出/不弹出，零客户端打印不弹出，本地打印弹出
        data: {
          reportlets, // 需要打印的模板列表
        },
        printType: 0, // 打印类型，0为零客户端打印，1为本地打印，暂时默认客户端打印
        // 以下为零客户端打印的参数，仅当 printType 为 0 时生效
        ieQuietPrint: true, // IE静默打印设置 true为静默，false为不静默
        // 以下为本地打印的参数，仅当 printType 为 1 时生效
        printerName: 'Microsoft Print to PDF', // 打印机名
        pageType: 0, // 打印页码类型：0：所有页，1：当前页，2：指定页
        // pageIndex: '1-3', // 页码范围。当 pageType 为 2 时有效
        // copy: 1, // 打印份数
      });
      recordPrintTimes();
      setSelectedTemplate(undefined);
    };

    const doH0Print = useCallback(
      (template: PrintTemplateProps) => {
        console.log(1111);
        return fetchTemplate
          .run({
            params: {
              tenantId,
              labelTemplateCode: template.printTemplateCode,
              labelTenantId: tenantId,
              ...printParams,
            },
          })
          .then(res => {
            if (res && !res?.failed && res?.label) {
              const pdfDiv = document.createElement('div');
              pdfDiv.id = 'hello';
              pdfDiv.style.paddingBottom = '20px';
              pdfDiv.style.overflow = 'overflow';
              pdfDiv.innerHTML = res.label.replace(/↵/gm, ''); // 去掉回车换行;
              PrintElement({
                content: pdfDiv,
              });
              setSelectedTemplate(undefined);
              recordPrintTimes();
              return true;
            }
            return false;
          });
      },
      [printParams, printRecordParams],
    );

    const doReportPrint = useCallback(
      async (template: PrintTemplateProps) => {
        const params = {
          templateUuid: template.printTemplateUuid,
          ...printParams,
        };
        const res = await fetchReportTemplate(params);
        if (res && !res?.failed) {
          if (res.type === 'application/json') {
            const file = new FileReader();
            file.readAsText(res, 'utf-8');
            file.onload = () => {
              if (typeof file.result === 'string') {
                const message = JSON.parse(file.result);
                return notification.error({ message: message.message });
              }
            };
          } else {
            const file = new Blob([res], { type: 'application/pdf' });
            const fileURL = URL.createObjectURL(file);
            const newWindow = window.open(fileURL, 'newwindow');
            if (newWindow) {
              newWindow.print();
              setSelectedTemplate(undefined);
              recordPrintTimes();
              return true;
            } else {
              notification.error({ message: '当前窗口已被浏览器拦截，请手动设置浏览器！' });
              return false;
            }
          }
        }
        return false;
      },
      [printParams, printRecordParams],
    );

    const recordPrintTimes = () => {
      if (!printRecordParams) {
        return;
      }
      console.log('打印次数');
      recordPrintRecord.run({
        params: {
          ...printRecordParams,
          printTimes: 1,
        },
        onSuccess: () => {
          if (printCallback) {
            printCallback();
          }
        },
      });
    };

    return (
      <PermissionButton
        type="c7n-pro"
        disabled={disabled || templateError}
        permissionList={permissionList}
        onClick={handleClickPrintBtn}
        style={style}
        icon={icon}
      >
        {/* {intl.get('tarzan.common.button.print').d('打印')} */}
        {name || intl.get('tarzan.common.button.print').d('打印')}
      </PermissionButton>
    );
  }),
);

export default PrintButton;
