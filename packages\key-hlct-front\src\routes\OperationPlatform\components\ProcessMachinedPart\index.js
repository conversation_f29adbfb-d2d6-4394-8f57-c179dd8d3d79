/* eslint-disable jsx-a11y/alt-text */
// 加工件（在制品标识）
import React, { useState, useMemo, useRef } from 'react';
import { TextField, Button, DataSet, Form, Output } from 'choerodon-ui/pro';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
// import NumberCharts from './App';
import moment from 'moment';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import position from '@/assets/operationPlatformCard/position.png';
import arrowRight from '@/assets/operationPlatformCard/arrow-right.png';
import codePrint from '@/assets/operationPlatformCard/codePrint.png';
// import { TemplatePrintButton } from '../../../../components/tarzan-ui';
import { useOperationPlatform } from '../../contextsStore';
import { useResizeObserver } from '../../useResizeObserver';
import { detailDS } from './stores/MachinedPartDS';
import { CardLayout, ONotification, useRequest } from '../commonComponents';
import { QueryCardList } from '../../services';
import styles from './index.modules.less';

const tenantId = getCurrentOrganizationId();

const MachinedPartCard = props => {
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  const timer = useRef(null);
  const [time, setTime] = useState(0);
  const { enterInfo, workOrderData, dispatch, cardMode } = useOperationPlatform();
  // const [computingTime, setComputingTime] = useState(false); // 是否计算时间
  // const [renderProps, setRenderProps] = useState([]);
  const [formColumns, setFormColumns] = useState(1); // 工单信息列数

  const { run: queryCardList, loading: queryCardListLoading } = useRequest(QueryCardList(), { manual: true, needPromise: true });

  useResizeObserver((element, size) => {
    // console.log('Element size:', size, element);
    if (size.width <= 480) {
      setFormColumns(1);
    } else if (size.width > 480 && size.width <= 780) {
      setFormColumns(2);
    } else if (size.width > 780 && size.width <= 1090) {
      setFormColumns(3);
    } else {
      setFormColumns(4);
    }
    // 在这里执行你需要的操作，例如重新布局、更新状态等。
  }, document.querySelector('.ProcessWorkorderMachinedPartForm'));

  // useEffect(() => {
  //   queryCardList({
  //     params: {
  //       cardCode: '4',
  //     },
  //   }).then(res => {
  //     if (res?.length) {
  //       try {
  //         const cardConfig = Object.entries(JSON.parse(res[0].cardConfiguration));
  //         setRenderProps(cardConfig);
  //       } catch (error) {
  //         ONotification.error({ message: "cardConfiguration字段配置错误！" });
  //       }
  //     }
  //   })
  // }, [])

  // 清空数据
  const cleanData = () => {
    dispatch({
      type: 'update',
      payload: {
        workOrderData: {},
      },
    })
    detailDs.loadData([]);
    // setComputingTime(false);
  };

  // 扫描在制品
  const onFetchProcessed = value => {
    // setComputingTime(false);
    if (timer.current) {
      clearInterval(timer.current);
      setTime(0);
    }
    if (value) {
      const params = {
        identification: value,
        workcellId: enterInfo?.workStationId,
        shiftCode: enterInfo?.shiftCode,
        shiftDate: enterInfo?.shiftDate,
        operationId: enterInfo?.selectOperation?.operationId,
        operationName: enterInfo?.selectOperation?.operationName,
        operationDesc: enterInfo?.selectOperation?.description,
        scanFlag: 'EO',
      };
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/eo-scan/ui`, {
        method: 'POST',
        body: params,
      }).then(res => {
        if (res && !res.failed) {
          detailDs.loadData([
            { ...res, ...res.workOrderCardVO, identificationField: res.identification },
          ]);
          dispatch({
            type: 'update',
            payload: {
              workOrderData: { ...res, ...res.workOrderCardVO, cardWorkpiece: 'Y' },
            },
          })
          setTime(res.processedTime || 0)
          timer.current = setInterval(() => {
            setTime((prev) => prev + 1);
          }, 1000)
          // if (res.processedTime || res.processedTime === 0) {
          //   setComputingTime(true);
          // }
          if (!!res?.containerLoadErrorMsg) {
            ONotification.warning({
              message: res?.containerLoadErrorMsg,
            });
          }
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'SUCCESS',
            message: `扫描在制品${value}成功`,
          });
          props.nextPriority();
        } else {
          ONotification.error({ message: res.message });
          setTimeout(() => {
            document.querySelector(`#operationPlatformInput${props.priorityLayout?.filter(item => item.i === '4')[0]?.priority}`).focus();
            document.querySelector(`#operationPlatformInput${props.priorityLayout?.filter(item => item.i === '4')[0]?.priority}`).select();
          }, 100);
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'FAIL',
            message: `扫描在制品${value}失败`,
          });
        }
      });
    } else {
      cleanData();
    }
  };

  // 加工完成
  const processCompleted = () => {
    props.changeSpin(true);
    const params = {
      workcellId: enterInfo?.workStationId,
      shiftCode: enterInfo?.shiftCode,
      shiftDate: enterInfo?.shiftDate,
      operationId: enterInfo?.selectOperation?.operationId,
      eoId: workOrderData?.eoId,
      routerStepId: workOrderData?.routerStepId,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/completion-processing/ui`, {
      method: 'POST',
      body: params,
    }).then(response => {
      if (response && !response.failed) {
        ONotification.success();
        // setComputingTime(false);
        const detailData = detailDs.toData()[0];
        detailDs.loadData([{ workOrderObj: detailData.workOrderCardVO }]);
        const params = {
          ...detailData.workOrderCardVO,
          workcellId: enterInfo.workStationId,
          operationId: enterInfo?.selectOperation?.operationId,
          operationName: enterInfo?.selectOperation?.operationName,
          operationDesc: enterInfo?.selectOperation?.description,
          routerStepFlag: workOrderData?.routerStepFlag,
          routerStepId: workOrderData?.routerStepId,
          sequence: workOrderData?.sequence,
          stepName: workOrderData?.stepName,
        };
        request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-eo-sum-results/card-work-order-wip/ui`, {
          method: 'POST',
          body: params,
        }).then(res => {
          if (res && !res.failed) {
            props.changeSpin(false);
            dispatch({
              type: 'update',
              payload: {
                workOrderData: { ...res, containerRefreshFlag: response.containerRefreshFlag },
              },
            });
            detailDs.loadData([res]);
            // setComputingTime(false);
            // setTimeout(() => {
            //   document.querySelector('#identificationPart').focus();
            //   document.querySelector('#identificationPart').select();
            // }, 100);
            if (!!response?.containerLoadErrorMsg) {
              ONotification.warning({
                message: response?.containerLoadErrorMsg,
              });
            }
            props.handleAddRecords({
              cardCode: props.cardCode,
              messageType: 'SUCCESS',
              message: `加工完成${workOrderData.workOrderNum}成功`,
            });
          } else {
            ONotification.error({ message: res.message });
            props.changeSpin(false);
            props.handleAddRecords({
              cardCode: props.cardCode,
              messageType: 'FAIL',
              message: `加工完成${workOrderData.workOrderNum}失败`,
            });
          }
        });
      } else {
        ONotification.error({ message: response.message });
        props.changeSpin(false);
      }
    });
  };

  const getTimes = t => {
    if (!t) {
      return;
    }
    let h = parseInt(String((t / 60 / 60) % 24), 10);
    let m = parseInt(String((t / 60) % 60), 10);
    let s = parseInt(String(t % 60), 10);
    // 三元表达式 补零 如果小于10 则在前边进行补零 如果大于10 则不需要补零
    if (t < 60) {
      s = s < 10 ? `0${s}` : s;
      return `${s}秒`;
    }
    if (t >= 60 && t < 3600) {
      m = m < 10 ? `0${m}` : m;
      s = s < 10 ? `0${s}` : s;
      return `${m}分${s}秒`;
    }
    h = h < 10 ? `0${h}` : h;
    m = m < 10 ? `0${m}` : m;
    s = s < 10 ? `0${s}` : s;
    return `${h}时${m}分${s}秒`;
  };

  return (
    <CardLayout.Layout
      spinning={queryCardListLoading}
    >
      <CardLayout.Header
        className='ProcessWorkorderMachinedPartHead'
        title="加工件"
        help={props?.cardUsage?.remark}
        content={
          <TextField
            dataSet={detailDs}
            placeholder="请扫描EO"
            id={`operationPlatformInput${props.priorityLayout?.filter(item => item.i === '4')[0]?.priority}`}
            name="identificationField"
            onEnterDown={e => onFetchProcessed(e.target.value)}
            onChange={value => (value ? null : onFetchProcessed(null))}
            prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />}
          />
        }
        addonAfter={
          <Button
            color="primary"
            onClick={processCompleted}
            disabled={!workOrderData?.eoId}
          >
            加工完成
          </Button>
        }
      />
      <CardLayout.Content className='ProcessWorkorderMachinedPartForm'>
        <div
          style={{
            display: workOrderData.currentProcess || workOrderData.nextProcess ? 'block' : 'none',
          }}
          className={styles.customTitle}
        >
          &nbsp;&nbsp;
          <img src={position} alt="" />
          <span style={{ color: 'rgba(51, 241, 255, 1)' }}> {workOrderData.currentProcess}</span>
          &nbsp;&nbsp;
          <img src={arrowRight} alt="" />
          &nbsp;&nbsp;
          <span style={{ color: 'rgba(255, 255, 255, 0.85)' }}>{workOrderData.nextProcess}</span>
        </div>
        {/* <CardCustomizeForm renderProps={renderProps} responseData={workOrderData} computingTime={computingTime} /> */}
        {/* <NumberCharts data={workOrderData} /> */}
        <Form dataSet={detailDs} labelWidth={130} columns={cardMode === 'Tile' ? formColumns : 4}>
          {/* {renderProps.map(item => (
            <Output name={item[0]}/>
          ))} */}
          <Output name='workOrderNum' />
          <Output name='identification' />
          <Output name='materialCode' />
          <Output name='opProcess' />
          <Output
            name='pitStopDate'
            renderer={({ value }) => {
              return value ? moment(value).format('YYYY-MM-DD HH:mm') : null;
            }}
          />
          <Output
            name='processedTime'
            renderer={() => {
              return time ? getTimes(time) : '';
            }}
          />
          <Output name='customerDesc' />
          <Output name='standardBeat' />
          <Output name='remark' />
          <Output name='materialName' />
        </Form>
        <div className={styles.cardFooter}>
          <div className={styles.printButton}>
            <img src={codePrint} alt='' />
            <div>条码打印</div>
          </div>
          {/* <TemplatePrintButton
            printButtonCode="HME.EO_IDENTIFICATION"
            disabled={!workOrderData.workOrderId}
            style={{ color: '#1cdbef' }}
            name="条码打印"
            icon="print"
            printParams={{
              identification: workOrderData.identification,
              materialCode: workOrderData.identification,
              qty: workOrderData.identification,
            }}
          /> */}
        </div>
      </CardLayout.Content>
    </CardLayout.Layout>
  )
};

export default formatterCollections({ code: ['model.org.monitor'] })(MachinedPartCard);
