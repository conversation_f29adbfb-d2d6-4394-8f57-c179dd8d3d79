
import intl from 'utils/intl';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.hmes.BatchProcessing';

const indexDS = () => ({
  autoQuery: false,
  autoCreate: false,
  autoQueryAfterSubmit: false,
  paging: false,
  dataKey: 'rows',
  selection: false,
  fields: [
    {
      name: 'containerQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerQty`).d('炉内容器数'),
    },
    {
      name: 'eoTotalQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.eoTotalQty`).d('炉内产品数'),
    },
    {
      name: 'containers',
      type: FieldType.object,
    },
  ],
  transport:{
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-batch-processing/containers/for-ui`,
        method: 'get'
      }
    }
  }
});

const scanModalDS = () => ({
  autoQuery: false,
  autoCreate: false,
  autoLocateFirst: false,
  dataKey: 'rows.detailInfo.content',
  totalKey: 'rows.detailInfo.totalElements',
  selection: false,
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('序列号'),
    },
    {
      name: 'materialCodeVersion',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCodeVersion`).d('物料编码/版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}. materialName`).d('物料描述'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
    {
      name: 'eoQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.eoQty`).d('数量'),
    },
  ],
  transport:{
    read: ()=> {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-batch-processing/scan-container/for-ui`,
        method: 'get'
      }
    }
  },
});

export { indexDS, scanModalDS };
