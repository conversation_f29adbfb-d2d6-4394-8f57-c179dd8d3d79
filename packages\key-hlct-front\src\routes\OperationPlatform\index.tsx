/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-28 11:12:04
 * @LastEditTime: 2023-08-21 15:32:29
 * @LastEditors: <<EMAIL>>
 */
// @ts-nocheck
import React, { useMemo, useState, useEffect } from 'react';
import {
  ModalProvider,
  notification as C7nNotification,
  Spin,
} from 'choerodon-ui/pro';
import moment from 'moment';
import formatterCollections from 'utils/intl/formatterCollections';
import { Header, Content } from 'components/Page';
import { BASIC } from '@utils/config';
import request from 'utils/request';
import { getCurrentOrganizationId } from 'utils/utils';
import intl from 'utils/intl';
import { observer } from 'mobx-react';
import './index.less';
import { debounce } from 'lodash';
import 'react-grid-layout/css/styles.css';
import { getActiveTabKey, openTab } from 'utils/menuTab';
import { DEBOUNCE_TIME } from 'utils/constants';
import { OperationPlatformProvider, useOperationPlatform } from './contextsStore';
import { useResizeObserver } from './useResizeObserver';
import { launchFullscreen, exitFullscreen, fullScreenEnabled } from './util';
import EnterModal from './EnterModal';
import styles from './index.module.less';
import './common.module.less';
import { CardItem } from './types';
import { QueryCardType } from './services';
import Headers from './HomePage/Headers';
import TileMode from './HomePage/TileMode';
import IndependentMode from './HomePage/IndependentMode';
import { useRequest } from './components/commonComponents';
import ProcessWorkorderMachinedPart from './components/ProcessWorkorderMachinedPart/index';
import ProcessMachinedPart from './components/ProcessMachinedPart/index';
import NewProcessMachinedPart from './components/NewProcessMachinedPart/index';
import ProductEntryLoose from './components/ProductEntryLoose/index';
import WorkorderMachinedPart from './components/WorkorderMachinedPart/index';
import MachineMaterials from './components/MachineMaterials/index';
import WorkOrderFeeding from './components/WorkOrderFeeding/index';
import TraceabilityConfirmation from './components/TraceabilityConfirmation/index';
import ProcessComponents from './components/ProcessComponents/index';
import SchedulingTasks from './components/SchedulingTasks/index';
import OperationMessage from './components/OperationMessage/index';
// import OperationRecords from './components/OperationRecords/index';
// import MessagePrompt from './components/MessagePrompt/index';
import BarcodeConversion from './components/BarcodeConversion';
import DataAcquisition from './components/DataAcquisition';
import InstructionsCard from './components/InstructionsCard';
import WorkOrderReporting from './components/WorkOrderReporting';
import ProcessReportingRecords from './components/ProcessReportingRecords';
import WeighingReportingRecords from './components/WeighingReportingRecords';
import SelfMutualInspection from './components/SelfMutualInspection';
import AssemblyProgress from './components/AssemblyProgress';
import BatchProcessing from './components/BatchProcessing';
import CardSetting from './CardSetting';
import Container from './components/Container/index';
import ContainerLoadingDetails from './components/ContainerLoadingDetails/index';

const tenantId = getCurrentOrganizationId();

const pageContentStyle = {
  // backgroundColor: 'rgb(243, 244, 245)',
  padding: '0 6px 6px 6px',
  backgroundColor: '#2a445e',
  // height: 'calc(100% - 48px)',
  // borderRadius: '0px',
};

const modelPrompt = 'tarzan.operationPlatform';

// 注意：如果需要初始布局，则命名必须是initialLayout
// 该值只在未加载到远程layout时生效
// { i: 'test', x: 0, y: 0, w: Math.floor(LAYOUT_COLS / 2), h: 1 },
/* x: 组件在x轴坐标 xaxis
  y: 组件在y轴坐标 yaxis
  w: 组件宽度 width
  h: 组件高度 height
  i: 组件key值 */
/* { w: 12, h: 1, x: 12, y: 0, i: '4', moved: false, static: false }, */
/* const initialLayout: Layout[] = [
  { w: 12, h: 1, x: 0, y: 0, i: '3', moved: false, static: false },
  { w: 12, h: 3, x: 12, y: 0, i: '1', moved: false, static: false },
]; */

const CommonDraggable = observer(props => {
  const {
    location,
    history,
    match: { path },
  } = props;
  const {
    logined,
    dispatch,
    enterInfo,
    cardMode,
    enabledCards,
    cardsList,
    itemLayout,
    selectCardsMainId,
    enabledButtonKeys,
  } = useOperationPlatform();
  const [loading, setLoading] = useState(false);
  const [activeTabFlag, setActiveTabFlag] = useState(false);
  const [editing, setEditing] = useState(false); // 头上的按钮展示
  const [spin, setSpin] = useState(false);
  const [cardShow, setCardShow] = useState(false);
  const [width, setWidth] = React.useState<number>(0);
  const [isFullFlag, setIsFullFlag] = useState(false); // 全屏
  const [priorityList, setPriorityList] = useState<any[]>([]); // 优先级列表
  const [selectPriority, setSelectPriority] = useState(null); // 当前优先级
  const [priorityLayout, setPriorityLayout] = useState<any[]>([]); // 带优先级的位置信息
  const [pageCardInfoList, setPageCardInfoList] = useState<any[]>([]); // 页面展示的卡片信息
  const pageCardInfoListRef = React.useRef<any[]>([]);
  const [selectCardsList, setSelectCardsList] = useState<any[]>([]); // 选中的卡片
  const [cardsExpand, setCardsExpand] = useState(false); // 卡片展开
  const [cardsArr, setCardsArr] = useState<any[]>([]); // 卡片
  const cardsArrRef = React.useRef<any[]>([]);
  const [cardTypes, setCardTypes] = useState<string[]>([]); // 卡片类型分类，用于Tab
  const { run: queryCardType } = useRequest(QueryCardType(), { manual: true, needPromise: true }); // 获取卡片数据
  const { run: fetchButtonList } = useRequest({ lovCode: 'HME.GENERAL_BUTTON' }, { manual: true, needPromise: true }); // 获取按钮数据

  useEffect(() => {
    pageCardInfoListRef.current = pageCardInfoList;
  }, [pageCardInfoList]);
  useResizeObserver((element, size) => {
    // console.log('Element size:', size, element);
    if (size.width <= 480) {
      document.querySelector('#operationPlatform').style.fontSize = '0.95vw';
    } else if (size.width > 480 && size.width <= 780) {
      document.querySelector('#operationPlatform').style.fontSize = '1vw';
    } else if (size.width > 780 && size.width <= 1090) {
      document.querySelector('#operationPlatform').style.fontSize = '1.05vw';
    } else {
      document.querySelector('#operationPlatform').style.fontSize = '1.1vw';
    }
    // 在这里执行你需要的操作，例如重新布局、更新状态等。
  }, document.querySelector('#operationPlatform'));

  useEffect(() => {
    cardsArrRef.current = cardsArr;
  }, [cardsArr]);
  useEffect(() => {
    if (activeTabFlag) {
      const container = document.getElementById('operationPlatform')!;
      C7nNotification.destroy();
      C7nNotification.config({
        getContainer: () => container || document.body,
        placement: 'bottomRight',
        bottom: 30,
        duration: 3,
      });
    } else {
      C7nNotification.destroy();
      C7nNotification.config({
        getContainer: () => document.body,
        placement: 'bottomRight',
        bottom: 30,
        duration: 3,
      });
    }
    return () => {
      C7nNotification.destroy();
      C7nNotification.config({
        getContainer: () => document.body,
        placement: 'bottomRight',
        bottom: 30,
        duration: 3,
      });
    };
  }, [activeTabFlag]);

  useEffect(() => {
    const container = document.querySelector('.c7n-pro-popup-container');
    if (!container) {
      return
    }
    if (!isFullFlag) {
      document.querySelector('body')?.appendChild(container);
    } else {
      document.getElementById('operationPlatform')?.appendChild(container);
    }
  }, [isFullFlag, document.querySelector('.c7n-pro-popup-container')]);

  useEffect(() => {
    if (!cardShow && cardsList.length > 0) {
      handleChangeCards(enabledCards);
    }
    setCardsExpand(false);
  }, [cardShow]);

  const handleChangeCards = async (enabledCards) => {
    const selectArr = [];
    const selectKey = [];
    await cardsArrRef.current.forEach(item => {
      if (enabledCards.includes(item.cardCode)) {
        selectArr.push(item);
        selectKey.push(`${item.cardCode}`);
      }
    });
    setSelectCardsList(selectArr);
    await dispatch({
      type: 'update',
      payload: {
        enabledCards: selectKey,
      },
    });
  };

  useEffect(() => {
    if (logined) {
      // 已登陆
      if (!isFullFlag) {
        screenFull(true);
      }
      setCardShow(false);
      fetchData();
    }
  }, [logined]);

  const fetchData = async () => {
    await initData();
    await queryCard();
  };

  /**
   * 获取按钮配置信息&卡片配置信息
   */
  const initData = async () => {
    let cardInfoList = cardsList;
    let buttonInfoList = [];
    if (!cardInfoList.length || !buttonInfoList.length) {
      [cardInfoList, buttonInfoList] = await Promise.all([getCardsList(), getCommonButtonList()])
    }
    const cardType = [...new Set(cardInfoList.map(item => item.cardType).filter(item => !!item))] as string[]
    setCardTypes(cardType);
    setCardsArr(cardInfoList);
    await dispatch({
      type: 'update',
      payload: {
        cardsList: cardInfoList,
        commonButton: buttonInfoList,
      },
    })
  }

  const getCommonButtonList = () => {
    return fetchButtonList({}).then(res => {
      if (res?.failed) {
        return [];
      }
      return res || [];
    });
  }

  const getCardsList = () => {
    return queryCardType({
      params: {
        catalogType: 'TEST_TYPE_001',
      },
    }).then(res => {
      if (res && res.failed) {
        return;
      }
      return res || []
    })
  }

  useEffect(() => {
    document.addEventListener('fullscreenchange', () => {
      setIsFullFlag(!!document.fullscreenElement || false);
    });
  }, []);

  useEffect(() => {
    const unlisten = history.listen(path => {
      if (path.pathname === '/hmes/operation-platform') {
        setActiveTabFlag(true);
        const body = document.body;
        const returnDom = document.querySelector('.returnButton');
        if (returnDom) {
          body.removeChild(returnDom);
        }
      } else {
        setActiveTabFlag(false);
      }
    });
    return () => {
      unlisten();
    };
  }, [document.querySelector('.c7n-pro-popup-container')]);

  useEffect(() => {
    window.addEventListener('resize', handleWindowResize);
    return () => {
      window.removeEventListener('resize', handleWindowResize);
    };
  }, []);

  const handleWindowResize = React.useCallback(
    debounce((): void => {
      const newActiveTabKey = getActiveTabKey();
      if (path === newActiveTabKey) {
        setTimeout(() => {
          const node = document.querySelector(`.${styles['card-content']}`);
          if (node instanceof HTMLElement) {
            setWidth(node.offsetWidth);
          }
        }, 0.5);
      }
    }, DEBOUNCE_TIME),
    [],
  );

  // 查询卡片信息和位置
  const queryCard = async () => {
    const params = {
      userId: enterInfo.userId,
      workcellId: enterInfo.workStationId,
      catalogType: 'TEST_TYPE_001',
    };
    setLoading(true);
    await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-user-card-rels`, {
      method: 'GET',
      query: params,
    }).then(async res => {
      if (res && !res.failed) {
        const newLayout: any = [];
        const newEnabledCards: any = [];
        const newPriorityList: any = [];
        setPageCardInfoList(res);
        setCardsExpand(false);
        await res.forEach(item => {
          if (item.priority) {
            newPriorityList.push(item.priority);
          }
          newLayout.push({
            i: item.cardCode,
            x: item.cardX,
            y: item.cardY,
            w: item.cardW,
            h: item.cardH,
            minH: item.cardMinH,
            minW: item.cardMinW,
            moved: false,
            static: false,
            priority: item.priority,
          });
          newEnabledCards.push(item.cardCode);
        });
        await dispatch({
          type: 'update',
          payload: {
            enabledCards: newEnabledCards,
            itemLayout: newLayout,
            cardMode: res[0]?.operationMode || 'Tile',
            selectCardsMainId: !res?.find(item => item.coreCardFlag === 'Y') ? '' : `${res?.filter(item => item.coreCardFlag === 'Y')[0]?.cardCode}`,
            enabledButtonKeys: !res[0]?.generalButton ? [] : res[0]?.generalButton?.split(','),
          },
        });
        handleChangeCards(newEnabledCards);
        await setPriorityLayout([...newLayout]);
        // 从小到大排序优先级
        const sortPriorityList = newPriorityList.sort((a, b) => {
          return a - b;
        });
        await setPriorityList([...sortPriorityList]);
        setTimeout(() => {
          (document.querySelector(
            `#operationPlatformInput${sortPriorityList[0]}`,
          ) as HTMLInputElement)?.focus();
          (document.querySelector(
            `#operationPlatformInput${sortPriorityList[0]}`,
          ) as HTMLInputElement)?.select();
        }, 100);
        setSelectPriority(sortPriorityList[0]);
      } else {
        C7nNotification.error({ message: res.message });
        return false;
      }
    });
    setLoading(false);
  };

  // 点击编辑按钮，开启编辑模式
  const handleEditLayout = () => {
    setEditing(true);
  }; // enabled

  // 取消编辑，恢复初始状态
  const handleCancelLayout = () => {
    setEditing(false);
    queryCard();
  };

  // 保存layout
  const handleSaveLayout = async () => {
    const params: any = [];
    if (cardMode === 'Tile') {
      const code2Id: any = {};
      cardsList.forEach(item => {
        code2Id[item.cardCode] = item.cardId;
      })
      itemLayout.forEach(item => {
        params.push({
          userId: enterInfo.userId,
          workcellId: enterInfo.workStationId,
          cardId: code2Id[item.i],
          cardCode: item.i,
          cardX: item.x,
          cardY: item.y,
          cardW: item.w,
          cardH: item.h,
          catalogType: 'TEST_TYPE_001',
          operationMode: cardMode,
          coreCardFlag: 'N',
          generalButton: enabledButtonKeys.join(','),
        })
      },
      );
    } else if (!cardsList.length) {
      setEditing(false);
    } else {
      cardsList.forEach((item, index) => {
        if (enabledCards.includes(item.cardCode)) {
          params.push({
            userId: enterInfo.userId,
            workcellId: enterInfo.workStationId,
            catalogType: 'TEST_TYPE_001',
            cardId: item.cardId,
            cardCode: item.cardCode,
            cardX: index === 6 ? 0 : index > 2 ? (index - 3) * 4 : 4 * index,
            cardY: 100,
            cardW: item.cardDefaultW,
            cardH: item.cardDefaultH,
            operationMode: cardMode,
            coreCardFlag: item.cardCode === selectCardsMainId ? 'Y' : 'N',
            generalButton: enabledButtonKeys.join(','),
          });
        }
      });
    }
    setLoading(true);
    await request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-user-card-rels`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        dispatch({
          type: 'update',
          payload: {
            itemLayout,
          },
        });
        setEditing(false);
        C7nNotification.success({
          message: intl.get(`${modelPrompt}.notification.success`).d('操作成功'),
          description: intl.get(`${modelPrompt}.layout.save.success`).d('布局保存成功'),
        });
        queryCard();
      } else {
        C7nNotification.error({ message: res.message });
        return false;
      }
    });
    setLoading(false);
  };

  // 打开卡片配置弹框
  const handleCardSetting = () => {
    setCardShow(true);
  };

  const changeSpin = value => {
    setSpin(value);
  };


  // 新增操作记录消息
  const handleAddRecords = value => {
    const currentRecord = {
      ...value,
      cardName: pageCardInfoListRef.current.filter(item => `${item.cardCode}` === value.cardCode)[0].cardName,
      creationDate: moment(new Date()).format('YY-MM-DD HH:mm:ss'),
    };
    dispatch({
      type: 'addMessageRecord',
      payload: currentRecord,
    });
  };

  // 光标定位下一优先级
  const nextPriority = () => {
    priorityList.forEach((item, index) => {
      if (item === selectPriority) {
        if (priorityList[index + 1]) {
          const nextPriorityValue = priorityList[index + 1];
          setTimeout(() => {
            (document.querySelector(
              `#operationPlatformInput${nextPriorityValue}`,
            ) as HTMLInputElement)?.focus();
            (document.querySelector(
              `#operationPlatformInput${nextPriorityValue}`,
            ) as HTMLInputElement)?.select();
          }, 100);
          setSelectPriority(nextPriorityValue);
        } else {
          setTimeout(() => {
            (document.querySelector(
              `#operationPlatformInput${priorityList[0]}`,
            ) as HTMLInputElement)?.focus();
            (document.querySelector(
              `#operationPlatformInput${priorityList[0]}`,
            ) as HTMLInputElement)?.select();
          }, 100);
          setSelectPriority(priorityList[0]);
        }
      }
    });
  };

  const cards = useMemo<CardItem[]>(
    () =>
      enabledCards?.length
        ? [
          // 加工件（工单+在制品标识）进出站（赋标识）
          {
            key: 'CARD_003',
            cardFlag: 'Workpiece',
            element: (
              <ProcessWorkorderMachinedPart
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_003')[0]}
                handleAddRecords={handleAddRecords}
                cardCode="CARD_003"
              />
            ),
          },
          // 加工件（在制品标识）
          {
            key: 'CARD_004',
            cardFlag: 'Workpiece',
            element: (
              <ProcessMachinedPart
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_004')[0]}
                changeSpin={changeSpin}
                handleAddRecords={handleAddRecords}
                cardCode="CARD_004"
                priorityLayout={priorityLayout}
                nextPriority={nextPriority}
              />
            ),
          },
          // 新加工件（在制品标识）进出站（带标识）
          {
            key: 'CARD_019',
            cardFlag: 'Workpiece',
            element: (
              <NewProcessMachinedPart
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_019')[0]}
                handleAddRecords={handleAddRecords}
                cardCode="CARD_019"
                priorityLayout={priorityLayout}
                nextPriority={nextPriority}
              />
            ),
          },
          // 产品进站-松散
          {
            key: 'CARD_045',
            cardFlag: 'Workpiece',
            element: (
              <ProductEntryLoose
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_045')[0]}
                handleAddRecords={handleAddRecords}
                cardCode="CARD_045"
                priorityLayout={priorityLayout}
                nextPriority={nextPriority}
              />
            ),
          },
          // 加工件（工单）进出站（无标识）
          {
            key: 'CARD_001',
            cardFlag: 'Workpiece',
            element: (
              <WorkorderMachinedPart
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_001')[0]}
                handleAddRecords={handleAddRecords}
                cardCode="CARD_001"
                priorityLayout={priorityLayout}
                nextPriority={nextPriority}
              />
            ),
          },
          // 机台物料
          {
            key: 'CARD_002',
            element: (
              <MachineMaterials
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_002')[0]}
                handleAddRecords={handleAddRecords}
                spin={spin}
                cardCode="CARD_002"
                priorityLayout={priorityLayout}
                nextPriority={nextPriority}
              />
            ),
          },
          // 追溯确认
          {
            key: 'CARD_005',
            element: (
              <TraceabilityConfirmation
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_005')[0]}
                handleAddRecords={handleAddRecords}
                spin={spin}
                cardCode="CARD_005"
              />
            ),
          },
          // 在制品工序组件
          {
            key: 'CARD_006',
            element: (
              <ProcessComponents
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_006')[0]}
                spin={spin}
                handleAddRecords={handleAddRecords}
                cardCode="CARD_006"
              />
            ),
          },
          // 调度任务
          {
            key: 'CARD_007',
            element: (
              <SchedulingTasks
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_007')[0]}
                handleAddRecords={handleAddRecords}
                spin={spin}
                cardCode="CARD_007"
              />
            ),
          },
          // 数据采集
          {
            key: 'CARD_009',
            element: (
              <DataAcquisition
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_009')[0]}
                handleAddRecords={handleAddRecords}
                cardCode="CARD_009"
              />
            ),
          },
          // 操作信息
          {
            key: 'CARD_021',
            element: (
              <OperationMessage {...props} />
            ),
          },
          // 操作记录
          // {
          //   key: 'CARD_021',
          //   element: (
          //     <OperationRecords />
          //   ),
          // },
          // 信息提示
          // {
          //   key: 'CARD_020',
          //   element: (
          //     <MessagePrompt />
          //   ),
          // },
          // 条码转换
          {
            key: 'CARD_022',
            element: (
              <BarcodeConversion
                handleAddRecords={handleAddRecords}
                priorityLayout={priorityLayout}
                nextPriority={nextPriority}
                cardCode="CARD_022"
              />
            ),
          },
          // 工单报工卡片
          {
            key: 'CARD_023',
            cardFlag: 'Workpiece',
            element: (
              <WorkOrderReporting
                loginWkcInfo={enterInfo}
                handleAddRecords={handleAddRecords}
                cardCode="CARD_023"
              />
            ),
          },
          // 作业指导书-预览
          {
            key: 'CARD_030',
            element: (
              <InstructionsCard
                cardCode="CARD_030"
              />
            ),
          },
          // 容器
          {
            key: 'CARD_031',
            element: (
              <Container
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_031')[0]}
                spin={spin}
                cardCode="CARD_031"
                priorityLayout={priorityLayout}
                handleAddRecords={handleAddRecords}
              />
            ),
          },
          // 容器装载明细
          {
            key: 'CARD_032',
            element: (
              <ContainerLoadingDetails
                spin={spin}
                cardCode="CARD_032"
                handleAddRecords={handleAddRecords}
              />
            ),
          },
          // 工序报工记录
          {
            key: 'CARD_033',
            cardFlag: 'Workpiece',
            element: (
              <ProcessReportingRecords
                handleAddRecords={handleAddRecords}
                cardCode="CARD_033"
              />
            ),
          },
          // 自互检
          {
            key: 'CARD_034',
            element: (
              <SelfMutualInspection
                cardUsage={cardsList?.filter(item => item.cardCode === 'CARD_034')[0]}
                handleAddRecords={handleAddRecords}
                cardCode="CARD_034"
              />
            ),
          },
          // 工单投料卡片
          {
            key: 'CARD_040',
            element: (
              <WorkOrderFeeding
                handleAddRecords={handleAddRecords}
                cardCode="CARD_040"
              />
            ),
          },
          // 工单装配进度
          {
            key: 'CARD_042',
            element: (
              <AssemblyProgress cardCode="CARD_042" handleAddRecords={handleAddRecords} />
            ),
          },
          // 工单装配进度
          {
            key: 'CARD_043',
            element: (
              <BatchProcessing cardCode="CARD_043" handleAddRecords={handleAddRecords} />
            ),
          },
          {
            key: 'CARD_044',
            element: (
              <WeighingReportingRecords
                handleAddRecords={handleAddRecords}
                cardCode="CARD_044"
              />
            ),
          },
        ].filter((c: CardItem) => enabledCards.includes(c.key))
        : [],
    [enabledCards],
  );

  const screenFull = async () => {
    if (fullScreenEnabled !== undefined && !fullScreenEnabled) {
      C7nNotification.warning({
        message: intl.get(`${modelPrompt}.fullScreen.failed`).d('暂不支持全屏'),
      });
      return;
    }
    const chartDom = document.getElementById('operationPlatform');
    if (!isFullFlag) {
      launchFullscreen(chartDom);
    } else {
      exitFullscreen();
    }
    setIsFullFlag(!isFullFlag);
  };

  /**
   * 给跳转的界面新增返回按钮
   */
  const handleAddReturn = () => {
    const body = document.body;
    const newNode = document.createElement('div');
    newNode.textContent = intl.get(`${modelPrompt}.return`).d('返回');
    newNode.className = 'returnButton';
    newNode.onclick = () => {
      openTab({
        title: '',
        key: `/hmes/operation-platform`,
        path: `/hmes/operation-platform`,
        closable: true,
      });
    };
    body.appendChild(newNode);
  };

  const handleCommonButton = item => {
    switch (item.meaning) {
      case '安灯平台':
        // 退出全屏状态
        if (isFullFlag) {
          exitFullscreen();
          setIsFullFlag(false);
        }
        handleAddReturn();
        // 点击按钮，跳转到异常处理平台功能，如果没有该功能则置灰
        openTab({
          title: '异常处理平台',
          key: `/hmes/exception-handling-platform/menu`,
          path: `/hmes/exception-handling-platform/menu`,
          closable: true,
          // search: {
          //   siteId: company[0],
          //   versionNumber: getVersion,
          //   demension: 'M',
          // },
        });
        break;
      case '在制品工序组件':
        console.error(
          '当界面卡片扫描了在制品后可点击，否则置灰。点击按钮后出现弹窗，显示对应的工序组件。工序组件的查询逻辑同对应卡片的逻辑',
        );
        break;
      case '物料批查询':
        // 退出全屏状态
        if (isFullFlag) {
          exitFullscreen();
          setIsFullFlag(false);
        }
        handleAddReturn();
        openTab({
          title: '物料批管理平台',
          key: `/hmes/product/material-lot-traceability/list`,
          path: `/hmes/product/material-lot-traceability/list`,
          closable: true,
          // search: {
          //   siteId: company[0],
          //   versionNumber: getVersion,
          //   demension: 'M',
          // },
        });
        break;
      default:
        break;
    }
  };

  const headersProps = {
    screenFull,
    editing,
    handleEditLayout,
    handleSaveLayout,
    handleCancelLayout,
    loading,
    handleCardSetting,
    cardShow,
  }

  const tileModeProps = {
    width,
    editing,
    cards,
    spin,
    priorityLayout,
    nextPriority,
    handleCommonButton,
  }

  const independentModeProps = {
    editing,
    cards,
    spin,
    priorityLayout,
    nextPriority,
    cardsExpand,
    selectCardsList,
    setCardsExpand,
    handleCommonButton,
  }
  return (
    <div id="operationPlatform" style={{ width: '100%', height: '100%' }}>
      <ModalProvider location={location} getContainer={false}>
        {!logined ? (
          <EnterModal />
        ) : (
          <div className={styles.operationPlatformEnter} style={{ overflow: 'auto', flex: 'auto' }}>
            <Header title={intl.get(`${modelPrompt}.plantform.title`).d('工序作业平台')}>
              <Headers {...headersProps} />
            </Header>
            <Content noCard style={pageContentStyle}>
              {cardShow && <CardSetting isFullFlag={isFullFlag} setCardShow={() => { setCardShow(false) }} cardTypes={cardTypes} />}
              <Spin spinning={loading}>
                {cardMode === 'Tile' && <TileMode {...tileModeProps} />}
                {cardMode === 'Independent' && <IndependentMode {...independentModeProps} />}
              </Spin>
            </Content>
          </div>
        )}
      </ModalProvider>
    </div>
  );
});

const RoutePage = props => {
  return (
    <OperationPlatformProvider>
      <CommonDraggable {...props} />
    </OperationPlatformProvider>
  );
};

// export default RoutePage;
export default formatterCollections({
  code: ['tarzan.operationPlatform', 'tarzan.common'],
})(RoutePage)
