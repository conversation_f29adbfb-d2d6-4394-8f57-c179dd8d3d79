/* eslint-disable jsx-a11y/alt-text */
// 容器
import React, { useState, useEffect, useMemo, useRef } from 'react';
import {
  Form,
  Button,
  DataSet,
  TextField,
  Modal,
  Output,
  Progress,
  Tooltip,
  SelectBox,
  Icon as C7nIcon,
} from 'choerodon-ui/pro';
import { Icon } from 'hzero-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import { observer } from 'mobx-react';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import cardSvg from '@/assets/icons/operation.svg';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import { useOperationPlatform, DispatchType } from '../../contextsStore';
import { useResizeObserver } from '../../useResizeObserver';
import TemplatePrintButton from '../../TemplatePrintButton';
import { detailDS, createDS, searchDS, queryDS } from './stores/ContainerDS';
import CreateContainerModal from './CreateContainerModal';
import QueryContainerModal from './QueryContainerModal';
import { CardLayout, ONotification } from '../commonComponents';
import styles from './index.modules.less';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.operationPlatform.container';

const tenantId = getCurrentOrganizationId();
const { Option } = SelectBox;
let containerEditModal;
let queryContainerModal;
const Container = observer(props => {
  const { dispatch, containerDetail, enterInfo, workOrderData, cardMode } = useOperationPlatform();

  const searchDs = useMemo(
    () =>
      new DataSet({
        ...searchDS(),
      }),
    [],
  );
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  const createDs = useMemo(
    () =>
      new DataSet({
        ...createDS(enterInfo),
      }),
    [],
  );
  const queryDs = useMemo(
    () =>
      new DataSet({
        ...queryDS(enterInfo),
      }),
    [],
  );

  const [loading, setLoading] = useState(false);
  // const [cacheEoId, setCacheEoId] = useState(null); // 工单数据
  const [containerTypeList, setContainerTypeList] = useState([]);
  const [packingLevelList, setPackingLevelList] = useState([]);
  const [stateContainerCode, setStateContainerCode] = useState('');
  const [printParams, setPrintParams] = useState({ containerIdList: 0 });
  const [createFlag, setCreateFlag] = useState(false);
  const printRef = useRef<any>(null);
  const [formColumns, setFormColumns] = useState(1); // 工单信息列数
  // // 字体大小控制
  // useEffect(() => {
  //   document.getElementById('machineMaterialsTitle').style.fontSize = `${10 +
  //     props.newLayout?.filter(item => item.i === '2')[0]?.w}px`;
  // }, [props.newLayout]);

  useResizeObserver((element, size) => {
    // console.log('Element size:', size, element);
    if (size.width <= 480) {
      setFormColumns(1);
    } else if (size.width > 480 && size.width <= 780) {
      setFormColumns(2);
    } else if (size.width > 780 && size.width <= 1090) {
      setFormColumns(3);
    } else {
      setFormColumns(4);
    }
    // 在这里执行你需要的操作，例如重新布局、更新状态等。
  }, document.querySelector('.ContainerForm'));

  useEffect(() => {
    if (printParams.containerIdList) {
      printRef.current.print();
    }
  }, [printParams]);

  useEffect(() => {
    detailDs.loadData([containerDetail]);
  }, [containerDetail]);

  useEffect(() => {
    if (containerEditModal) {
      const createContainerProps = {
        createDs,
        selectBoxListRender,
        selectPackingListRender,
        changeContainerCode: handleChangeContainerCode,
        flag: false,
        enterInfo,
      };
      containerEditModal.update({
        header: (
          <div className={styles.c7nProModalheader}>
            <img src={cardSvg} alt="" />
            <div className={styles.c7nProModalTitle}>
              {createFlag ? intl.get(`${modelPrompt}.modal.createContainer`).d('创建容器') : intl.get(`${modelPrompt}.modal.configureContainer`).d('配置容器')}
            </div>
            <div
              className={styles.c7nProModalTitle}
              style={{ position: 'absolute', right: 15, cursor: 'pointer' }}
            >
              <Icon
                onClick={() => {
                  containerEditModal.close();
                }}
                type="close"
              />
            </div>
          </div>
        ),
        key: Modal.key(),
        destroyOnClose: true,
        closable: true,
        mask: true,
        style: { width: '50%' },
        contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
        className: styles.numberModal,
        children: <CreateContainerModal {...createContainerProps} />,
        footer: (
          <div style={{ marginBottom: '30px' }}>
            {/* <Radio onChange={handleChange}>
            总是保持以上选择，不再询问
          </Radio> */}
            <div style={{ float: 'left' }}>
              <SelectBox
                disabled={!!createDs.current?.get('containerCode')}
                multiple
                value={createDs.current?.get('cacheFlag')}
                onChange={handleChange}
              >
                <SelectBox.Option value="true">
                  {intl.get(`${modelPrompt}.always.keep.above.selection`).d('总是保持以上选择，不再询问')}
                </SelectBox.Option>
              </SelectBox>
            </div>
            <div style={{ float: 'right' }}>
              <Button color={ButtonColor.primary} onClick={() => handleCreate()}>
                {intl.get(`${modelPrompt}.created`).d('创建')}
              </Button>
            </div>
          </div>
        ),
      });
    }
  }, [stateContainerCode]);

  useEffect(() => {
    queryContainerType();
    queryPackingLevel();
  }, []);

  useEffect(() => {
    if (workOrderData.containerRefreshFlag === 'Y') {
      dispatch({
        type: DispatchType.update,
        payload: {
          workOrderData: { ...workOrderData, containerRefreshFlag: 'P' },
        },
      });
      queryContainerDetail(detailDs.current?.get('containerCode'));
    }
  }, [workOrderData]);

  useEffect(() => {
    setLoading(props.spin);
  }, [props.spin]);

  const format = () => {
    if (detailDs.current?.get('sumLoadQty') && detailDs.current?.get('capacityQty')) {
      return (
        <span style={{ color: '#fff' }}>
          {detailDs.current?.get('sumLoadQty')}/{detailDs.current?.get('capacityQty')}
        </span>
      );
    }
    return '';
  };
  const handleChange = value => {
    createDs.current?.set('cacheFlag', value);
  };

  const queryContainerType = () => {
    const requestUrl = `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/mt-container-type/lov/ui?size=-1&page=0`;
    request(requestUrl, {
      method: 'GET',
    }).then(res => {
      if (res && !res.failed) {
        setContainerTypeList(res.content);
      } else {
        ONotification.error({
          message: res?.message,
        });
      }
    });
  };

  const queryPackingLevel = () => {
    const requestUrl = `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?typeGroup=PACKING_LEVEL`;
    request(requestUrl, {
      method: 'GET',
    }).then(res => {
      if (res && !res.failed) {
        setPackingLevelList(res.rows);
      } else {
        ONotification.error({
          message: res?.message,
        });
      }
    });
  };

  // 容器卡片查询容器信息
  const queryContainerDetail = containerCode => {
    if (!containerCode) {
      return false;
    }
    const params = {
      containerCode,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-encasement/container/scan/ui`, {
      method: 'GET',
      query: params,
    }).then(async res => {
      if (res && res.success) {
        if (res.rows.containerId) {
          dispatch({
            type: DispatchType.update,
            payload: {
              containerDetail: {
                ...res.rows,
                capacityQty: res.rows.capacityQty || 0,
                sumLoadQty: res.rows.sumLoadQty || 0,
                sumLoadBarcodeQty: res.rows.sumLoadBarcodeQty || 0,
                printTimes: res.rows.printTimes || 0,
              },
              workOrderData: { ...workOrderData, containerRefreshFlag: 'N' },
            },
          });
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'SUCCESS',
            message: intl.get(`${modelPrompt}.process.scanContainer.success`).d(`扫描容器${res.rows.containerCode}成功`)
          });
          ONotification.success({
            message: intl.get(`${modelPrompt}.process.scanContainer.success`).d(`扫描容器${res.rows.containerCode}成功`)
          });
        } else {
          dispatch({
            type: DispatchType.update,
            payload: {
              containerDetail: {},
              workOrderData: { ...workOrderData, containerRefreshFlag: 'N' },
            },
          });
        }
      } else {
        ONotification.error({
          message: intl.get(`${modelPrompt}.process.scanContainer.failure`).d(`扫描容器${containerCode}失败，原因为:${res.message}`)
        });
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'FAIL',
          message: intl.get(`${modelPrompt}.process.scanContainer.failure`).d(`扫描容器${containerCode}失败，原因为:${res.message}`)
        });
      }
    });
  };

  // 容器创建
  const handleCreate = async () => {
    if (searchDs.current?.get('scanCode') && !detailDs.current?.get('printTimes')) {
      return ONotification.warning({
        message: intl.get(`${modelPrompt}.notification.container.barcode.notPrinted`).d('容器条码未打印，不可重置或新增容器，请检查'),
      });
    }
    const printFlag = createDs.current?.get('printFlag');
    const validate = await createDs.validate();
    const data = createDs.toData()[0];
    if (validate) {
      const requestUrl = `${BASIC.HMES_BASIC
        }/v1/${getCurrentOrganizationId()}/hme-container-encasement/container/creation/ui`;
      const res = await request(requestUrl, {
        method: 'POST',
        body: {
          ...data,
          printFlag: null,
          siteId: enterInfo?.siteId,
          workcellId: enterInfo?.workStationId,
          shiftCode: enterInfo?.shiftCode,
          shiftDate: enterInfo?.shiftDate,
          containerId: null,
          cacheFlag: data?.cacheFlag?.length === 1 ? 'Y' : 'N',
        },
      });
      if (res && res.success) {
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'SUCCESS',
          message: intl.get(`${modelPrompt}.process.addContainer.success`).d(`新增容器${res.rows.containerCode}成功`)
        });
        ONotification.success({
          message: intl.get(`${modelPrompt}.process.addContainer.success`).d(`新增容器${res.rows.containerCode}成功`)
        });
        createDs.current?.set('containerId', res.rows.containerId);
        if (printFlag === 'Y') {
          setPrintParams({ containerIdList: res.rows.containerId });
        }
        queryContainerDetail(res.rows.containerCode);
        searchDs.current?.set('scanCode', res.rows.containerCode);
        containerEditModal.close();
      } else {
        ONotification.error({
          message: res?.message,
        });
        return false;
      }
    }
  };

  const selectBoxListRender = () => {
    return containerTypeList.map((item: any) => {
      // @ts-ignore
      return <Option value={item.containerTypeId}>{item.containerTypeDescription}</Option>;
    });
  };

  const selectPackingListRender = () => {
    return packingLevelList.map((item: any) => {
      return <Option value={item.typeCode}>{item.description}</Option>;
    });
  };

  const handleChangeContainerCode = value => {
    setStateContainerCode(value);
  };

  const createContainer = flag => {
    if (flag) {
      if (searchDs.current?.get('scanCode') && !detailDs.current?.get('printTimes')) {
        return ONotification.warning({
          message: intl.get(`${modelPrompt}.notification.container.barcode.notPrinted`).d('容器条码未打印，不可重置或新增容器，请检查'),
        });
      }
    }
    if (createDs.current?.get('cacheFlag')?.length === 1 && createDs.current?.get('containerId')) {
      handleCreate();
    } else {
      setCreateFlag(flag);
      const createContainerProps = {
        createDs,
        selectBoxListRender,
        selectPackingListRender,
        changeContainerCode: handleChangeContainerCode,
        flag,
        enterInfo,
      };
      containerEditModal = Modal.open({
        header: (
          <div className={styles.c7nProModalheader}>
            <img src={cardSvg} alt="" />
            <div className={styles.c7nProModalTitle}>
              {flag ? intl.get(`${modelPrompt}.modal.createContainer`).d('创建容器') : intl.get(`${modelPrompt}.modal.newContainerConfig`).d('新建容器配置')}
            </div>
            <div
              className={styles.c7nProModalTitle}
              style={{ position: 'absolute', right: 15, cursor: 'pointer' }}
            >
              <Icon
                onClick={() => {
                  containerEditModal.close();
                }}
                type="close"
              />
            </div>
          </div>
        ),
        key: Modal.key(),
        destroyOnClose: true,
        closable: true,
        mask: true,
        style: { width: '50%' },
        contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
        className: styles.numberModal,
        children: <CreateContainerModal {...createContainerProps} />,
        footer: (
          <div style={{ marginBottom: '30px' }}>
            {/* <Radio onChange={handleChange}>
            总是保持以上选择，不再询问
          </Radio> */}
            <div style={{ float: 'left' }}>
              <SelectBox
                disabled={!!createDs.current?.get('containerCode')}
                multiple
                value={createDs.current?.get('cacheFlag')}
                onChange={handleChange}
              >
                <SelectBox.Option value="true">
                  {intl.get(`${modelPrompt}.always.keep.above.selection`).d('总是保持以上选择，不再询问')}
                </SelectBox.Option>
              </SelectBox>
            </div>
            <div style={{ float: 'right' }}>
              <Button color={ButtonColor.primary} onClick={() => handleCreate()}>
                {intl.get(`${modelPrompt}.created`).d('创建')}
              </Button>
            </div>
          </div>
        ),
      });
    }
  };

  const handlePrint = () => {
    setPrintParams({ containerIdList: detailDs.current?.get('containerId') });
  };

  const printCallback = () => {
    detailDs.current?.set('printTimes', detailDs.current?.get('printTimes') + 1);
    props.handleAddRecords({
      cardCode: props.cardCode,
      messageType: 'SUCCESS',
      message: intl.get(`${modelPrompt}.process.printContainer.success`).d(`打印容器${detailDs.current?.get('containerCode')}成功`)
    });
  };

  const handleDoubleClick = record => {
    searchDs.current?.set('scanCode', record.get('containerCode'));
    queryContainerDetail(record.get('containerCode'));
    queryContainerModal.close();
  };

  const queryContainer = () => {
    queryDs.query();
    const queryContainerModalProps = {
      queryDs,
      enterInfo,
      containerTypeList,
      onConfirm: handleDoubleClick,
    };
    queryContainerModal = Modal.open({
      header: (
        <div className={styles.c7nProModalheader}>
          <img src={cardSvg} alt="" />
          <div className={styles.c7nProModalTitle}>
            {intl.get(`${modelPrompt}.container.query`).d('容器查询')}
          </div>
          <div
            className={styles.c7nProModalTitle}
            style={{ position: 'absolute', right: 30, cursor: 'pointer' }}
          >
            <Icon
              onClick={() => {
                queryContainerModal.close();
              }}
              type="close"
            />
          </div>
        </div>
      ),
      key: Modal.key(),
      destroyOnClose: true,
      closable: true,
      mask: true,
      style: { width: '80%' },
      contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
      className: styles.numberModal,
      children: <QueryContainerModal {...queryContainerModalProps} />,
      footer: null,
    });
  };
  return (
    <CardLayout.Layout spinning={loading} className={styles.container}>
      <CardLayout.Header
        className="ContainerHead"
        title={intl.get(`${modelPrompt}.container`).d('容器')}
        content={
          <TextField
            placeholder={intl.get(`${modelPrompt}.scanBarcode`).d('请扫描条码')}
            id={`operationPlatformInput${props.priorityLayout?.filter(item => item.i === '31')[0]?.priority
              }`}
            name="scanCode"
            dataSet={searchDs}
            onEnterDown={e => queryContainerDetail(e.target.value)}
            prefix={<img src={scanIcon} alt="" style={{ height: '19px' }} />}
          />
        }
        addonAfter={
          <div>
            <Button
              color={ButtonColor.primary}
              onClick={() => {
                if (searchDs.current?.get('scanCode') && !detailDs.current?.get('printTimes')) {
                  return ONotification.warning({
                    message: intl.get(`${modelPrompt}.notification.containerBarcodeNotPrinted`).d('容器条码未打印，不可重置或新增容器，请检查'),
                  });
                }
                searchDs.reset();
                dispatch({
                  type: DispatchType.update,
                  payload: {
                    containerDetail: {
                      containerId: null,
                      containerDetail: [],
                    },
                  },
                });
              }}
            >
              {intl.get(`${modelPrompt}.button.reset`).d('重置')}
            </Button>
            {/* <Button color={ButtonColor.primary} onClick={() => createContainer(false)}>
              容器配置
            </Button> */}
            <Button color={ButtonColor.primary} onClick={queryContainer}>
              {intl.get(`${modelPrompt}.button.sarch.container`).d('查询容器')}
            </Button>
            <Button color={ButtonColor.primary} onClick={() => createContainer(true)}>
              {intl.get(`${modelPrompt}.button.add`).d('新建')}
            </Button>
            <C7nIcon
              type="settings"
              className={styles.settingsIcon}
              onClick={() => createContainer(false)}
            />
          </div>
        }
      />
      <CardLayout.Content className="ContainerForm" style={{ position: 'relative' }}>
        <Form
          columns={cardMode === 'Tile' ? formColumns : 4}
          dataSet={detailDs}
          labelWidth={130}
          className={styles.modalForm}
        >
          <Output name="containerCode" />
          <Output name="containerTypeDescription" />
          <Output
            name="capacitySumLoadQty"
            renderer={({ record }) => {
              if (!record?.get('sumLoadQty') || !record?.get('capacityQty')) {
                return '';
              }
              return `${record?.get('sumLoadQty') || ''}/${record?.get('capacityQty') || ''}`;
            }}
          />
          <Output name="locatorCode" />
          <Output name="sumLoadBarcodeQty" />
          <Output name="printTimes" />
          <Output name="packingLevelDesc" />
        </Form>
        <div
          style={{
            display: 'flex',
            bottom: 0,
            position: 'absolute',
            width: '100%',
            height: '50px',
            lineHeight: '50px',
            padding: '0 16px',
          }}
        >
          <div style={{ width: '130px', color: '#1cdbef' }}>
            {intl.get(`${modelPrompt}.load.progress`).d('装载进度：')}
          </div>
          <div style={{ width: '90%' }}>
            <Tooltip
              title={
                detailDs.current?.get('sumLoadQty') && detailDs.current?.get('capacityQty')
                  ? `${(
                    (detailDs.current?.get('sumLoadQty') / detailDs.current?.get('capacityQty')) *
                    100
                  ).toFixed(2)}%`
                  : null
              }
            >
              <Progress
                value={
                  (detailDs.current?.get('sumLoadQty') / detailDs.current?.get('capacityQty')) * 100
                }
                format={format}
                strokeWidth={20}
                strokeColor="#1cdbef"
              />
            </Tooltip>
          </div>
          <div style={{ width: '100px' }}>
            <Button
              // funcType="link"
              icon="print"
              onClick={handlePrint}
              style={{ color: '#1cdbef', background: '#38708f' }}
            >
              {intl.get(`${modelPrompt}.button.barcode.print`).d('条码打印')}
            </Button>
            <TemplatePrintButton
              ref={printRef}
              printButtonCode="HME.CONTAINER"
              disabled={false}
              style={{ display: 'none' }}
              printParams={printParams}
              printCallback={printCallback}
              printRecordParams={{
                printObjectIds: [printParams.containerIdList],
                containerId: printParams.containerIdList,
                printObjectType: 'CONTAINER',
              }}
            />
          </div>
        </div>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
});


export default formatterCollections({
  code: ['tarzan.operationPlatform', 'tarzan.common', 'tarzan.hmes']
})(Container);
