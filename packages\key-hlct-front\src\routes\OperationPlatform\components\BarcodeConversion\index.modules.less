.BarcodeConversion {
  .BarcodeConversionLine {
    background: rgba(56, 112, 143, 1);
    position: absolute;
    top: 50%;
    left: 50%;
    width: 90%;
    height: 30%;
    transform: translate(-50%, -50%);

    .c7n-result-title {
      color: white !important;
    }

    :global {
      .c7n-pro-field-label {
        color: #70bbf3;
      }

      .c7n-pro-textarea {
        border: none !important;
        // background: #38708F !important;
        color: white !important;
      }

      .c7n-pro-input-wrapper {
        background: #38708F !important;
      }

      .c7n-pro-input-rendered-value {
        background: #38708F !important;
      }

      .c7n-pro-input {
        // background: #38708F !important;
        border: none !important;
        color: white !important;
      }

      .c7n-pro-field-label.c7n-pro-field-label.c7n-pro-field-label {
        font-size: 27px !important;
        font-weight: bold !important;
        padding: 10px 0 !important;
      }
      .c7n-pro-field-label.c7n-pro-field-label.c7n-pro-field-label + td .c7n-pro-field-wrapper{
        padding: none !important;
      }
      .BarcodeConversionInputDisabled {

        // background: #38708F !important;
        .c7n-pro-input {
          // background: #38708F !important;
          border: none !important;
          color: yellow !important;
        }

      }

      .c7n-pro-textarea-wrapper {
        background: #38708F;
      }

      .c7n-pro-input-disabled {
        background: #32617f;
      }
    }
  }
}

#BarcodeConversionLineInput{
  :global{
    .c7n-pro-input-wrapper.c7n-pro-input-wrapper label .c7n-pro-input{
      border: none !important;
      background-color: transparent !important;
    }
  }
}
