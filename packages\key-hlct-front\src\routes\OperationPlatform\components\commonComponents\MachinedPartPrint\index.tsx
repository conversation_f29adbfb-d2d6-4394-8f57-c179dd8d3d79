import React, { useMemo, useEffect } from 'react';
import leftTopLog from '@/assets/operationPlatformCard/leftTopLog.svg';
import close from '@/assets/operationPlatformCard/close.png';
import { Table, DataSet } from 'choerodon-ui/pro';
import intl from 'utils/intl';
// import { TemplatePrintButton } from '../../../../../components/tarzan-ui';
import TemplatePrintButton from '../../../TemplatePrintButton';
import { printDS } from './stores';
import styles from './index.module.less';

export interface MachinedPartPrintProps {
  workOrderData: object; // 工单信息
  enterInfo: object; // 工位信息
  handleCloseStepModal: () => boolean;
  printModalShow: boolean;
  contentClassName: string;
}

const modelPrompt = 'tarzan.operationPlatform';

const MachinedPartPrint = ({workOrderData = {}, enterInfo = {}, handleCloseStepModal, printModalShow, contentClassName}: MachinedPartPrintProps) => {
  const printDs = useMemo(() => new DataSet(printDS()), []);

  useEffect(() => {
    // console.log('printModalShow', printModalShow, workOrderData, enterInfo);
    if(printModalShow){
      const container = document.querySelector(`.${styles.doneStepFlagModals}`);
      // console.log('container', container, contentClassName);
      if(container){
        document.getElementById('operationPlatform')?.appendChild(container);
        handleFilterPrint();
      }
    }
  }, [printModalShow, document.querySelector(`.${styles.doneStepFlagModals}`)]);

  const column = [
    {
      name: 'identification',
    },
    {
      name: 'eoNum',
    },
    {
      name: 'wipQty',
    },
    {
      name: 'creationDate',
      align: 'center',
    },
    {
      name: 'operationName',
    },
    {
      name: 'wipStatusDesc',
    },
    {
      name: 'printTimes',
    },
  ]

  const handleFilterPrint = () => {
    // @ts-ignore
    printDs.setQueryParameter('workOrderId', workOrderData?.workOrderId);
    // @ts-ignore
    printDs.setQueryParameter('operationId', enterInfo?.selectOperation?.operationId);
    printDs.query();
  }

  const printCallback = () => {
    handleFilterPrint();
  }

  const onHandleCloseStepModal = () => {
    const container = document.querySelector(`.${styles.doneStepFlagModals}`);
    if(container){
      document.querySelector(`.${contentClassName}`)?.appendChild(container);
      handleCloseStepModal();
    }
  };

  return (
    <div className={`${styles.doneStepFlagModals}`} style={{display: `${printModalShow ? 'block' : 'none'}`}}>
      <div className={styles.modalBox}>
        <div className={styles.modalTitle}>
          <div>
            <img src={leftTopLog} alt="" />
            {/* <span>条码打印</span> */}
            <span>{intl.get(`${modelPrompt}.codePrint`).d('条码打印')}</span>
          </div>
          <div className={styles.modalRightFooter} id={styles.modalRightFooter}>
            <TemplatePrintButton
              icon=''
              name="打印"
              disabled={printDs.selected.length === 0}
              printButtonCode="OP_EO"
              printParams={{
                eoIds: printDs.selected.map(item => item.get('eoId')).join(','),
              }}
              printCallback={printCallback}
              printRecordParams={{
                printObjectIds: printDs.selected.map(item => item.get('eoId')),
                printObjectType: 'EO',
              }}
            />
            <div
              onClick={() => {
                onHandleCloseStepModal();
              }}
            >
              <img src={close} alt="" />
            </div>
          </div>
        </div>
        <div className={styles.modalContent}>
          <Table
            dataSet={printDs}
            // @ts-ignore
            columns={column}
            customizedCode="ProcessReportingRecords"
            showSelectionTips
            style={{
              maxHeight: 400,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default MachinedPartPrint;
