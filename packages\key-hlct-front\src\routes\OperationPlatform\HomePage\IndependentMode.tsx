import React, { cloneElement } from 'react';
import { Button } from 'choerodon-ui/pro';
import { openTab } from 'utils/menuTab';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { useOperationPlatform, DispatchType } from '../contextsStore';
import styles from '../index.module.less';

const IndependentMode = (props) => {
  const {
    cards,
    editing,
    spin,
    priorityLayout,
    nextPriority,
    cardsExpand,
    selectCardsList,
    setCardsExpand,
    handleCommonButton,
  } = props;

  const {
    dispatch,
    enabledButtonKeys,
    itemLayout,
    selectCardsMainId,
    commonButton = [],
  } = useOperationPlatform();

  const handleChangeMain = record => {
    dispatch({
      type: DispatchType.update,
      payload: {
        selectCardsMainId: `${record.cardCode}`,
      },
    });
  };

  return (
    <div
      className={styles.independentLayout}
      style={{ paddingBottom: enabledButtonKeys.length ? '58px' : 0 }}
    >
      {cards.map(({ key, element }) => (
        <div
          key={key}
          className={`${styles['card-container']} ${editing ? styles['card-container-editing'] : '' }`}
          style={{ display: key === selectCardsMainId ? 'flex' : 'none' }}
          id="cardisId"
        >
          <div className={styles['card-lalala']}>
            <div
              className={`${styles['card-content']} hzero-draggable-card-content ${editing ? styles['editing-pointer-none'] : ''
              }`}
            >
              {cloneElement(element, {
                newLayout: itemLayout,
                // eoData,
                // orderData,
                spin,
                // recordsValue,
                priorityLayout,
                // setSelectPriority,
                nextPriority,
                // loginWkcInfo: enterInfo,
              })}
            </div>
          </div>
        </div>
      ))}
      {/* 按钮-下方 */}
      <div
        className={styles.independentButtons}
        style={{ display: enabledButtonKeys.length ? 'flex' : 'none' }}
      >
        {commonButton.map(item => {
          if (enabledButtonKeys.includes(item.value)) {
            return (
              <Button
                color={ButtonColor.primary}
                onClick={() => {
                  handleCommonButton(item);
                }}
              >
                {item.meaning}
              </Button>
            );
          }
          return null;
        })}
      </div>
      {/* 右侧-操作 */}
      <div
        className={styles.independentOperation}
        style={{ bottom: enabledButtonKeys.length ? '145px' : '55px' }}
      >
        {cardsExpand &&
          selectCardsList.length > 0 &&
          selectCardsList.map(item => (
            <div
              className={`${styles.independentCards} ${item.cardCode === Number(selectCardsMainId) ? styles.independentSelect : ''}`}
              onClick={() => {
                handleChangeMain(item);
              }}
            >
              {item.cardName}
            </div>
          ))}
        {selectCardsList.length > 0 && (
          <div
            className={styles.cardSelect}
            onClick={() => {
              setCardsExpand(!cardsExpand);
            }}
          >
            <div className={styles.squareOne}>
              <div className={styles.squareTwo}></div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default IndependentMode;
