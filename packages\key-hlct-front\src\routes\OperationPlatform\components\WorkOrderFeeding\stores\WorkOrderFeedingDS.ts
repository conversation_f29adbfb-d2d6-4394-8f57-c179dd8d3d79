// 工单投料-ds
import intl from 'utils/intl';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

const modelPrompt = 'tarzan.operationPlatform';

const scanDetailDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  fields: [
    {
      name: 'materialLotId',
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.barcode`).d('条码'),
    },
    {
      name: 'scanQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.scanQty`).d('数量'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('materialLotId');
        },
      },
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qtyUnit`).d('数量/单位'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locator`).d('库位'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quality.status`).d('质量状态'),
    },
    {
      name: 'substituteMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.substituteMaterialCode`).d('物料批编码'),
    },
  ],
});

const tableDS = (): DataSetProps => ({
  paging: false,
  selection: false,
  expandField: 'expand',
  fields: [
    { name: 'expand', type: FieldType.boolean },
    {
      name: 'lineNumber',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lineNumber`).d('行号'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCodeInfo`).d('物料/版本'),
    },
    {
      name: 'materialDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialDesc`).d('物料描述'),
    },
    {
      name: 'requiredQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.requiredQty`).d('执行/需求'),
    },
    {
      name: 'removeQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.removeQty`).d('移除数量'),
      min: 0,
    },
    {
      name: 'replace',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.replace`).d('替代'),
    },
    {
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.bomComponentType`).d('组件类型'),
      name: 'bomComponentType',
      lookupUrl: `${BASIC.TARZAN_COMMON}/v1/${tenantId}/mt-gen-type/combo-box/ui?module=BOM&typeGroup=BOM_COMPONENT_TYPE`,
      textField: 'description',
      valueField: 'typeCode',
      lookupAxiosConfig: {
        transformResponse(data) {
          if (Array.isArray(data)) {
            return data;
          }
          const datas = JSON.parse(data);
          // 处理接口报错
          if (datas && !datas.success) {
            return {
              rows: [],
            };
          }
          return datas.rows;
        },
      },
    },
    {
      name: 'revokeQtySum',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.revokeQtySum`).d('撤回数量'),
    },
  ],
});

const subTableDS = (): DataSetProps => ({
  paging: false,
  selection: false,
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.barcode`).d('条码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'assembleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.assembleQty`).d('执行数量'),
    },
    {
      name: 'lotCreateFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lotCreateFlag`).d('是否生成新条码'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'revokeQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.revokeQty`).d('本次撤回'),
      min: 0,
      max: 'assembleQty',
    },
  ],
});

const modalTableDS = (): DataSetProps => ({
  paging: false,
  selection: false,
  fields: [
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCodeInfo`).d('物料编码/版本'),
    },
    {
      name: 'substituteUsage',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.substituteUsage`).d('替代用量'),
    },
    {
      name: 'assembleQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.feedingQty`).d('投料数量'),
    },
  ],
});

export { scanDetailDS, tableDS, subTableDS, modalTableDS };
