.MessagePromptResult {
  .MessagePromptResultContentSuccess {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-content: center;
    align-items: center;
    flex-wrap: wrap;
    padding: 0 !important;
    :global {
      .c7n-result-icon{
        margin-bottom: 0;
        .icon{
          font-size: 70px;
        }
      }
      .c7n-result-title {
        font-size: 70px;
        color: rgb(17, 217, 84) !important;
        margin-left: 20px;
        line-height: 1;
        font-weight: 600;
      }
      .c7n-result-subtitle{
        width: 100%;
        font-size: 26px;
        color: white !important;
        margin-top: 20px;
        line-height: 1.5;
      }
    }
  }
  .MessagePromptResultContentError{
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-content: center;
    align-items: center;
    flex-wrap: wrap;
    padding: 0 !important;
    :global {
      .c7n-result-icon{
        margin-bottom: 0;
        .icon{
          font-size: 70px;
        }
      }
      .c7n-result-title {
        font-size: 70px;
        color: rgb(242, 58, 80) !important;
        margin-left: 20px;
        line-height: 1;
        font-weight: 600;
      }
      .c7n-result-subtitle{
        width: 100%;
        font-size: 26px;
        color: white !important;
        margin-top: 20px;
        line-height: 1.5;
      }
    }
  }
}

#MessagePromptResultContent{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  :global{
    .icon{
      font-size: 70px !important;
    }
  }
}
