
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();


/**
 *  批量加工-执行进站
 * @function ScanMaterialLot
 * @returns {object} fetch Promise
 */
export function ConfirmInSite() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-batch-processing/confirm-in-site/for-ui`,
    method: 'POST',
  };
}

/**
 *  批量加工-执行出站
 * @function ScanMaterialLot
 * @returns {object} fetch Promise
 */
export function DischargeFromFurnace() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-batch-processing/out-site/for-ui`,
    method: 'POST',
  };
}
