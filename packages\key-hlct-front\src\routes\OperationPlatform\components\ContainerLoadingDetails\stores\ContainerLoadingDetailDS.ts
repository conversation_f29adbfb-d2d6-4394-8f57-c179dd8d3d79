// 加工件（工单）DS
import intl from 'utils/intl';
import { FieldType, DataSetSelection } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.hmes.ProcessWorkorderMachinedPart';
const tenantId = getCurrentOrganizationId();

const tableDs: () => DataSetProps = () => ({
  primaryKey: 'serialNumber',
  selection: DataSetSelection.multiple,
  paging: false,
  fields: [
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.identification`).d('条码'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderNum`).d('工单编码'),
    },
    {
      name: 'loadQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.loadQty`).d('数量'),
    },
    {
      name: 'qualityStatusDesc',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qualityStatus`).d('质量'),
    },
    {
      name: 'loadTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadTime`).d('装载时间'),
    },
  ],
});

const failedFormDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  autoQueryAfterSubmit: false,
  paging: false,
  fields: [
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('当前箱码'),
    },
    {
      name: 'containerTypeDescription',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerType`).d('容器类型'),
    },
    {
      name: 'stepCompleteFlag',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.stepCompleteFlag`).d('是否末道序'),
      trueValue: 'Y',
      falseValue: 'N',
      defaultValue: 'N',
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('剩余容量'),
    },
  ],
});

const failedTableDS: () => DataSetProps = () => ({
  // primaryKey: 'serialNumber',
  selection: false,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'loadObjectCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadObjectCode`).d('条码'),
    },
    {
      name: 'material',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
    },
    {
      name: 'loadWorkOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.loadWorkOrderNum`).d('工单号'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locatorName`).d('容器库位'),
    },
    {
      name: 'trxLoadQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.qty`).d('条码数量'),
    },
    {
      name: 'operation',
      label: intl.get(`${modelPrompt}.operation`).d('操作'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-container-encasement/pre-load/lose/query/ui`,
        method: 'GET',
      };
    },
  },
});

export { tableDs, failedFormDS, failedTableDS, };
