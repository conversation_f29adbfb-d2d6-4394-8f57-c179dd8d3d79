import { Request } from 'express';

const namespace = '_Template80';

let defaultEnable: string[] = [
  // 'test',
  '#card-0',
  '#card-1',
];

module.exports = {
  name: namespace,
  apis: [
    {
      name: 'fetch layout',
      method: 'get',
      url: `/_api/${namespace}/layout`,
      handle() {
        return {
          status: 200,
          data: {
            content: [
              {
                w: 12,
                h: 1,
                x: 0,
                y: 0,
                i: '#card-0',
                moved: false,
                static: false,
              },
              {
                w: 12,
                h: 3,
                x: 12,
                y: 0,
                i: '#card-1',
                moved: false,
                static: false,
              },
            ],
          },
        };
      },
    },
    {
      name: 'save layout',
      method: 'post',
      url: `/_api/${namespace}/layout`,
      handle(req: Request) {
        return {
          delay: 2,
          status: 200,
          data: req.body,
        };
      },
    },
    {
      name: 'Get enabled cards',
      method: 'get',
      url: `/_api/${namespace}/enabled`,
      handle() {
        return {
          status: 200,
          data: defaultEnable,
        };
      },
    },
    {
      name: 'Update enabled',
      method: 'post',
      url: `/_api/${namespace}/enabled`,
      handle(req: Request) {
        const { enabled } = req.body;
        defaultEnable = enabled;
        return {
          status: 200,
          data: enabled,
        };
      },
    },
  ],
};
