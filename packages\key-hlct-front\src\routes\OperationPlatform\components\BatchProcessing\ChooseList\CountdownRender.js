import React, {useState, useEffect, useMemo} from 'react';
import styles from "../index.module.less";

function Countdown({item, item:{countDown, countDownSymbol}, styleClass, selectedIds, handleSelectCard, queryContainerDetail }) {
  const [seconds, setSeconds] = useState(countDown);
  const [countDownSymbols, setCountDownSymbols] = useState(countDownSymbol);
  const [defaultColor, setDefaultColor] = useState('')

  useEffect(()=>{
    const timer = parseTimeString(countDown)
    setSeconds(timer)
  },[countDown])

  useEffect(() => {
    const interval = setInterval(() => {
      setSeconds((prevSeconds) => {
        if(countDownSymbols === '+'){
          return prevSeconds + 1000
        } else {
          return prevSeconds - 1000
        }
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [countDownSymbols]);

  function parseTimeString(timeString) {
    if(!timeString){
      return ''
    }
    const [hoursStr, minutesStr, secondsStr] = timeString?.split(':');
    const hours = parseInt(hoursStr, 10);
    const minutes = parseInt(minutesStr, 10);
    const seconds = parseInt(secondsStr, 10);
    return (hours * 3600 + minutes * 60 + seconds) * 1000;
  }

  const formatTime = useMemo(() => {
    const totalSeconds = Math.floor(seconds / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const second = totalSeconds % 60;
    if(hours === 0 && minutes === 0 && second === 0 ){
      setCountDownSymbols('+')
      setDefaultColor('#A85D71')
    }
    if(countDownSymbols === '+') {
      setDefaultColor('#A85D71')
    }
    if(hours === 0 && minutes < 5 && countDownSymbols === '-'){
      setDefaultColor('#B8A352')
    }
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${second.toString().padStart(2, '0')}`;
  },[seconds, countDownSymbols]);

  return (
    <div
      className={styles.content}
      style={{height: '200px', width:340}}
    >
      {
        selectedIds.includes(item.containerId) ? (
          <div className={styles.cardSelect}>
            <div className={styles.squareOne}>
              <div className={styles.squareTwo}></div>
            </div>
          </div>
        ): null
      }
      <div className={styles.top} style={{backgroundColor: defaultColor}} onClick={()=>{
        handleSelectCard(item.containerId)
      }}>
        <div className={styles.topRight}>
          <div>{item.containerCode}</div>
          <div style={{fontSize: 14}}>
            <span>{`种类: ${item.materialQty}`}</span>
            <span style={{marginLeft: 20}}>{`总数: ${item.detailQty}`}</span>
          </div>
        </div>
        <div className={styleClass}>
          {countDownSymbols +  formatTime}
        </div>
      </div>
      <div className={styles.down} onClick={()=>{
        queryContainerDetail(item.containerCode)
      }}>
        {
          item?.cardInfo?.length ? item.cardInfo.map(i=>(
            <div className={styles.downList}>
              <div>{i.materialCode}</div>
              <div>{i.materialEoQty}</div>
              <div>{i.materialName}</div>
            </div>
          )) : <></>
        }
      </div>
    </div>

  );
}

export default Countdown;
