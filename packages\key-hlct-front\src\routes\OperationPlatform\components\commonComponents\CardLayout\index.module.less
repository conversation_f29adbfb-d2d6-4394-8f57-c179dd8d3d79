.cardLayout {
  display: flex;
  height: 100%;
  overflow: hidden;
  background: #38708f;

  :global {
    .c7n-spin-nested-loading {
      flex: 1;
      width: 100%;

      .c7n-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .cardHeader {
    display: inline-flex;
    align-items: center;
    height: 48px;
    overflow: hidden;
    width: 100%;
    padding: 0 16px;
    background: linear-gradient(172.09deg, rgba(99, 242, 255, 0.74) 0%, rgba(80, 234, 242, 0.55) 23.43%, rgba(75, 214, 239, 0.47) 34.52%, rgba(48, 97, 219, 0.01) 100%);

    .headerTitle {
      display: inline-flex;
      align-items: center;
      color: white;

      .titleIcon {
        margin-right: 8px;
      }

      .helpIcon {
        margin: 2px;
      }
    }

    .headerContent {
      flex: 1;
      padding: 0 10px;
      display: inline-flex;
      justify-content: flex-end;

      :global {
        .c7n-pro-field-label {
          color: white;
        }

        .c7n-pro-input-wrapper {
          background: #5a9ebd;
        }
        .c7n-pro-input-number-wrapper {
          background: #5a9ebd;
        }

        .c7n-pro-input {
          color: white !important;
        }
        .c7n-pro-input-number {
          color: white !important;
        }

        .c7n-pro-select-wrapper {
          background: #5a9ebd;
        }

        .c7n-pro-select {
          color: white !important;
        }

        .c7n-pro-select-wrapper.c7n-pro-select-wrapper label .c7n-pro-select-suffix .icon {
          color: white !important;
        }
      }
    }

    .headerAction {
      line-height: 1;
    }
  }

  .cardContent {
    flex: 1 1;
    overflow: auto;
    padding: 10px 0px;

    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
      background-color: transparent;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 14px;
      background: #88a9bc;
    }
    :global {
      .c7n-pro-field-label {
        color: white;
      }

      .c7n-pro-textarea {
        border: none !important;
        // background: #38708F !important;
        color: white !important;
      }

      .c7n-pro-input-wrapper {
        background: #38708F !important;
      }
      .c7n-pro-input-number-wrapper {
        background: #38708F !important;
      }

      // .c7n-pro-input-rendered-value {
      //   background: #38708F !important;
      // }

      // .c7n-pro-input {
      //   // background: #38708F !important;
      //   border: none !important;
      //   color: white !important;
      // }

      .c7n-pro-textarea-wrapper {
        background: #38708F;
      }

      // .c7n-pro-input-disabled {
      //   background: #32617f;
      // }
    }
  }
}
