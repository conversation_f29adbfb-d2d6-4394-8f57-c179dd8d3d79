// 机台物料
import React, { useState, useEffect, useMemo } from 'react';
import {
  Form,
  Button,
  DataSet,
  Row,
  Col,
  TextField,
  Modal,
  Table,
  NumberField,
  Output,
  Switch,
} from 'choerodon-ui/pro';
import { isArray } from 'lodash';
import { Icon } from 'hzero-ui';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import intl from 'utils/intl';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import drag from '@/assets/operationPlatformCard/drag.svg';
import { DndProvider } from 'react-dnd-9.3.4';
import HTMLBackend from 'react-dnd-html5-backend-9.3.4';
import { useOperationPlatform } from '../../contextsStore';
import { detailDS, tableDs, saveTableDs } from './stores/MachineMaterialsDS';
import { CardLayout, ONotification } from '../commonComponents';
import styles from './index.modules.less';

import DragComponentsRow from '../commonComponents/DragComponentsRow';

const modelPrompt = 'tarzan.operationPlatform';

const tenantId = getCurrentOrganizationId();

const MachineMaterials = props => {
  const detailDs = useMemo(
    () =>
      new DataSet({
        ...detailDS(),
      }),
    [],
  );
  const tableDataSet = useMemo(
    () =>
      new DataSet({
        ...tableDs(),
      }),
    [],
  );

  const saveTableDataSet = useMemo(
    () =>
      new DataSet({
        ...saveTableDs(),
      }),
    [],
  );
  const { enterInfo, workOrderData } = useOperationPlatform();
  const [materialData, setMaterialData] = useState([]); // 工单数据
  const [selected, setSelected] = useState([]); // 工单数据
  const [expandForm, setExpandForm] = useState(true); // 展开收起标识
  const [loading, setLoading] = useState(false);
  const [dragFlag, setDragFlag] = useState(false);
  const [autoSelectFlag, setAutoSelectFlag] = useState(false);
  // const [cacheEoId, setCacheEoId] = useState(null); // 工单数据
  let numberModal;
  let saveModal;
  // // 字体大小控制
  // useEffect(() => {
  //   document.getElementById('machineMaterialsTitle').style.fontSize = `${10 +
  //     props.newLayout?.filter(item => item.i === '2')[0]?.w}px`;
  // }, [props.newLayout]);

  useEffect(() => {
    // if (workOrderData?.eoId && cacheEoId !== workOrderData?.eoId) {
    if (workOrderData?.eoId) {
      // setCacheEoId(workOrderData?.eoId);
      queryMaterial();
    }
  }, [workOrderData]);

  useEffect(() => {
    setLoading(props.spin);
  }, [props.spin]);

  // 查询物料
  const queryMaterial = () => {
    setLoading(true);
    const params = {
      workcellId: enterInfo?.workStationId,
      operationId: enterInfo?.selectOperation?.operationId,
      routerStepId: workOrderData?.routerStepId,
      woId: workOrderData?.workOrderId,
      eoId: workOrderData?.eoId,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-machine-material/lot-card/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        const list = [];
        if (res.length > 0) {
          res.forEach(item => {
            // eslint-disable-next-line no-unused-expressions
            item.lotVO3List &&
              item.lotVO3List.forEach(children => {
                if (children.list?.length > 0) {
                  children.list?.forEach(childrens => {
                    if (childrens.inputQty > 0 && childrens.materialType !== 'B') {
                      list.push(childrens);
                    }
                  });
                }
              });
            if (item.substituteList && item.substituteList[0]) {
              item.substituteList.forEach(children => {
                if (children.lotVO3List?.length > 0) {
                  children.lotVO3List?.forEach(childrens => {
                    if (childrens.list && childrens.list[0]) {
                      childrens.list?.forEach(childrenss => {
                        if (childrenss.inputQty > 0 && childrenss.materialType !== 'B') {
                          list.push(childrenss);
                        }
                      });
                    }
                  });
                }
              });
            }
          });
          setSelected([...list]);
        }
        setMaterialData(res || []);
        setLoading(false);
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'SUCCESS',
          message: `查询物料${workOrderData?.identification}成功`,
          recordType: 'query',
        });
      } else {
        ONotification.error({ message: res.message });
        setLoading(false);
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'FAIL',
          message: `查询物料${workOrderData?.identification}失败`,
          recordType: 'query',
        });
      }
    });
  };

  const handleConfirm = (value, flag) => {
    if (tableDataSet.selected.length === 0) {
      ONotification.error({ message: intl.get(`${modelPrompt}.notification.siteRequired`).d('请选择装配点') });
      return false;
    }
    onScanMaterial(value, flag);
    tableDataSet.unSelectAll();
    return true;
  };

  // 扫描物料
  const onScanMaterial = (value, flag) => {
    const params = {
      scanCode: value,
      workcellId: enterInfo?.workStationId,
      workcellCode: enterInfo?.workStationCode,
      operationId: enterInfo?.selectOperation?.operationId,
      operationName: enterInfo?.selectOperation?.operationName,
      routerStepId: workOrderData?.routerStepId,
      assignFlag: flag,
      assembleGroupAssign: tableDataSet.selected.length > 0 ? tableDataSet.selected[0].data : null,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-machine-material/scan/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        if (res.length > 0) {
          tableDataSet.loadData(res);
          const columns = [
            {
              name: 'serialNumber',
            },
            {
              name: 'assemblePointCode',
            },
          ];
          Modal.open({
            title: intl.get(`${modelPrompt}.assemblePointSelect.title`).d('装配点选择'),
            destroyOnClose: true,
            // closable: true,
            children: <Table rowHeight={40} dataSet={tableDataSet} columns={columns} />,
            onOk: () => handleConfirm(value, 'Y'),
            onCancel: () => {
              tableDataSet.unSelectAll();
            },
          });
        } else {
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'SUCCESS',
            message: `扫描物料${value}成功`,
          });
          queryMaterial();
          props.nextPriority();
        }
      } else {
        setTimeout(() => {
          document.querySelector(`#operationPlatformInput${props.priorityLayout?.filter(item => item.i === '2')[0]?.priority}`).focus();
          document.querySelector(`#operationPlatformInput${props.priorityLayout?.filter(item => item.i === '2')[0]?.priority}`).select();
        }, 100);
        ONotification.error({ message: res.message });
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'FAIL',
          message: `扫描物料${value}失败`,
        });
      }
    });
  };

  // 选择卡片
  const handleSelectCard = listItem => {
    if (dragFlag) {
      return;
    }
    if (!listItem.inputQty && !listItem.expectQty && !listItem.calculateQty) {
      return;
    }
    const list = selected;
    let flag = true;
    list.forEach(item => {
      if (
        item.materialLotId === listItem.materialLotId &&
        item.materialId === listItem.materialId &&
        item.assemblePointCode === listItem.assemblePointCode
      ) {
        flag = false;
      }
    });
    if (flag) {
      list.push(listItem);
      setSelected([...list]);
    } else {
      const newList = list.filter(
        item =>
          !(
            item.materialLotId === listItem.materialLotId &&
            item.materialId === listItem.materialId &&
            item.assemblePointCode === listItem.assemblePointCode
          ),
      );
      setSelected([...newList]);
    }
  };

  const handleUnbind = (listItem, flag) => {
    const qty = detailDs.toData()[0].qty;
    const list = selected.filter(
      item =>
        item.materialLotId === listItem.materialLotId &&
        item.materialId === listItem.materialId &&
        item.assemblePointCode === listItem.assemblePointCode,
    );
    const params = {
      workcellId: enterInfo?.workStationId,
      eoId: workOrderData?.eoId,
      materialLotId: listItem?.materialLotId,
      qty,
      checkOrNot: list.length === 1 ? 'Y' : '',
      detachTheMachineFlag: flag,
    };
    return request(
      `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-machine-material/detach-the-machine/ui`,
      {
        method: 'POST',
        body: params,
      },
    ).then(res => {
      if (res && !res.failed) {
        if (!res.success) {
          Modal.open({
            title: <span style={{ color: 'white', wordWrap: 'break-word', wordBreak: 'break-all', whiteSpace: 'pre-wrap' }}>{res.message}</span>,
            destroyOnClose: true,
            contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
            // closable: true,
            onOk: () => handleUnbind(listItem, 'Y'),
          });
        } else {
          ONotification.success({
            message: intl.get(`${modelPrompt}.unbind.success`).d('解绑成功'),
          });
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'SUCCESS',
            message: `eo解绑成功`,
          });
          queryMaterial();
          numberModal.close();
        }
      } else {
        ONotification.error({ message: res.message });
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'FAIL',
          message: `eo解绑失败`,
        });
        return false;
      }
    });
  };

  // 打开数字弹框
  const openNumberModal = (listItem, item, flag) => {
    if (dragFlag) {
      return;
    }
    detailDs.current.set(
      'qty',
      listItem.calculateQty === 0
        ? 0
        : listItem.calculateQty || listItem.inputQty || listItem.expectQty || 0,
    );
    detailDs.getField('qty').set('max', listItem.primaryUomQty || 0);
    numberModal = Modal.open({
      title: intl.get(`${modelPrompt}.primaryQty.title`).d('操作数量'),
      key: Modal.key(),
      destroyOnClose: true,
      closable: true,
      mask: true,
      style: { width: '350px' },
      contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
      className: styles.numberModal,
      children: (
        <Form
          columns={1}
          dataSet={detailDs}
          className={styles.modalForm}
          labelLayout="none"
          labelWidth={80}
        >
          <Row>
            <Col style={{ textAlign: 'center' }} span={16}>
              <Output value={listItem.materialLotCode} />
            </Col>
            <Col span={8}>
              <Button
                style={{ background: '#50819c', color: 'white' }}
                onClick={() => handleUnbind(listItem)}
              >
                {intl.get(`${modelPrompt}.unbind`).d('解绑')}
              </Button>
            </Col>
          </Row>
          <Row>
            <Col style={{ textAlign: 'center' }} span={5}>
              <Icon
                type="minus-circle"
                style={{ fontSize: 16, color: '#7da2b6', marginTop: '11px' }}
                onClick={() => detailDs.current.set('qty', detailDs.toData()[0].qty - 1)}
              />
            </Col>
            <Col style={{ textAlign: 'center' }} span={6}>
              <NumberField name="qty" style={{ width: '100%' }} />
            </Col>
            <Col style={{ textAlign: 'center' }} span={5}>
              <Icon
                type="plus-circle"
                style={{ fontSize: 16, color: '#00d4cd', marginTop: '11px' }}
                onClick={() => detailDs.current.set('qty', detailDs.toData()[0].qty + 1)}
              />
            </Col>
            <Col span={8}>
              <Button
                style={{ background: '#1b7efc', color: 'white' }}
                onClick={async () => {
                  const validate = await detailDs.validate();
                  if (validate) {
                    const qty = detailDs.toData()[0].qty;
                    // 修改为0取消勾选
                    if (qty === 0) {
                      const list = selected;
                      const newList = list.filter(
                        item =>
                          !(
                            item.materialLotId === listItem.materialLotId &&
                            item.materialId === listItem.materialId &&
                            item.assemblePointCode === listItem.assemblePointCode
                          ),
                      );
                      setSelected([...newList]);
                    }
                    if (flag === 'substituteList') {
                      // 替代料重新赋值
                      materialData.forEach(data => {
                        data?.substituteList?.forEach(dataItem => {
                          dataItem?.lotVO3List?.forEach(dataItems => {
                            dataItems?.list?.forEach(dataItemss => {
                              if (dataItemss.materialLotId === listItem.materialLotId) {
                                dataItemss.expectQty = qty - dataItemss.inputQty;
                                dataItemss.calculateQty = qty;
                              }
                            });
                          });
                        });
                      });
                      setMaterialData([...materialData]);
                      numberModal.close();
                    } else {
                      // 组件料重新赋值
                      materialData.forEach(data => {
                        if (data.materialCode === item.materialCode) {
                          data?.lotVO3List?.forEach(dataItem => {
                            dataItem?.list?.forEach(dataItems => {
                              if (dataItems.materialLotId === listItem.materialLotId) {
                                dataItems.expectQty = qty - dataItems.inputQty;
                                dataItems.calculateQty = qty;
                              }
                            });
                          });
                        }
                      });
                      setMaterialData([...materialData]);
                      numberModal.close();
                    }
                  }
                }}
              >

                {intl.get(`${modelPrompt}.add.primary`).d('追投数量')}
              </Button>
            </Col>
          </Row>
        </Form>
      ),
      footer: null,
    });
  };

  // 确认
  const onConfirmMaterial = (flag, unbindLotEoList) => {
    const params = {
      workcellId: enterInfo?.workStationId,
      operationId: enterInfo?.selectOperation?.operationId,
      routerStepId: workOrderData?.routerStepId,
      eoId: workOrderData?.eoId,
      // 如果选择的数据中inputQty不存在，则将unitQty赋值给inputQty
      list: selected.map(v => {
        return {
          ...v,
          inputQty: v.calculateQty === 0 ? 0 : v.calculateQty || v.inputQty || v.expectQty || 0,
        };
      }),
      unbindLotEoList: flag ? unbindLotEoList : null,
    };
    return request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-machine-material/confirm/ui`, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res && !res.failed) {
        if (!res.success) {
          saveTableDataSet.loadData(res.unbindLotEoList);
          const colums = [
            {
              name: 'materialLotCode',
              style: {
                fontSize: 17,
              },
              headerStyle: {
                fontSize: 17,
              },
            },
            {
              name: 'identification',
              style: {
                fontSize: 17,
              },
              headerStyle: {
                fontSize: 17,
              },
            },
          ];
          saveModal = Modal.open({
            title: <span style={{ color: 'white' }}>{res.message}</span>,
            contentStyle: { backgroundColor: 'rgba(56, 112, 143, 1)' },
            destroyOnClose: true,
            className: styles.MachineMaterialsModal,
            children: <Table rowHeight={40} dataSet={saveTableDataSet} columns={colums} />,
            closable: true,
            footer: (
              <>
                <Button
                  color="primary"
                  onClick={() => {
                    if (saveTableDataSet.selected.length === 0) {
                      ONotification.warning({
                        message: intl.get(`${modelPrompt}.notification.selectedLineRequired`).d('追投数量'),
                      });
                      return;
                    }
                    const selectData = saveTableDataSet.selected.map(item => item.toData());
                    onConfirmMaterial(true, selectData);
                    saveModal.close();
                  }}
                >
                  {intl.get('tarzan.common.button.confirm').d('确定')}
                </Button>
                <Button
                  onClick={() => {
                    saveModal.close();
                  }}
                >
                  {intl.get('tarzan.common.button.back').d('返回')}
                </Button>
              </>
            ),
          });
        } else {
          ONotification.success({
            message: intl.get(`${modelPrompt}.operation.success`).d('操作成功'),
          });
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'SUCCESS',
            message: `物料确认成功`,
          });
          queryMaterial();
          // props.changeEoData({ ...workOrderData });
        }
        // if(props.confirmMaterial){
        //   props.changeEoData({...workOrderData});
        // }
      } else {
        ONotification.error({ message: res.message });
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'FAIL',
          message: `物料确认失败`,
        });
      }
    });
  };

  // 计算数量
  const countQuantityExpectQty = item => {
    let num = 0;
    item.lotVO3List?.forEach(v => {
      v.list?.forEach(ele => {
        num += ele.expectQty;
      });
    });
    // 替代料
    item.substituteList?.forEach(v => {
      v.lotVO3List?.forEach(ele => {
        ele.list?.forEach(items => {
          num += (items.expectQty || 0) / items.substituteUsage;
        });
      });
    });
    return num;
  };

  // 计算数量
  const countQuantity = item => {
    let num = 0;
    item.lotVO3List?.forEach(v => {
      v.list?.forEach(ele => {
        num += ele.inputQty;
      });
    });
    // 替代料
    item.substituteList?.forEach(v => {
      v.lotVO3List?.forEach(ele => {
        ele.list?.forEach(items => {
          num += (items.inputQty || 0) / items.substituteUsage;
        });
      });
    });
    return num;
  };

  // 计算数量相加
  const countQuantityADD = item => {
    return countQuantityExpectQty(item) + countQuantity(item);
  };

  const onConfirmDrag = () => {
    const _componentList = [];
    if (isArray(materialData)) {
      materialData.forEach((item) => {
        let _materialLotSeqList = [];
        // eslint-disable-next-line no-unused-expressions
        item.lotVO3List &&
          item.lotVO3List.forEach((children) => {
            if (children.list?.length > 0) {
              children.list?.forEach((childrens) => {
                _materialLotSeqList.push({
                  materialId: childrens.materialId,
                  materialLotId: childrens.materialLotId,
                });
              });
            }
          });
        if (item.substituteList && item.substituteList[0]) {
          item.substituteList.forEach(children => {
            if (children.lotVO3List?.length > 0) {
              children.lotVO3List?.forEach(childrens => {
                if (childrens.list && childrens.list[0]) {
                  childrens.list?.forEach(childrenss => {
                    _materialLotSeqList.push({
                      materialId: childrenss.materialId,
                      materialLotId: childrenss.materialLotId,
                    });
                  });
                }
              });
            }
          });
        }
        _materialLotSeqList = _materialLotSeqList.map((item, index) => {
          return {
            ...item,
            sequence: index + 1,
          }
        })
        _componentList.push({
          materialLotSeqList: _materialLotSeqList,
        })
      })
      request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-machine-material/seq-change/ui`, {
        method: 'POST',
        body: {
          componentList: _componentList,
          workcellId: enterInfo?.workStationId,
        },
      }).then(res => {
        if (res && res.success) {
          ONotification.success({})
          setDragFlag(false)
        } else if (res.message) {
          ONotification.error({
            message: res.message,
          })
        }
      });
    }
  }

  const handleResetList = (index, lotIndex, list) => {
    const newList = materialData;
    newList[index].lotVO3List[lotIndex].list = list;
    setMaterialData([...newList]);
  }

  useEffect(() => {
    if (autoSelectFlag) {
      const list = [];
      materialData.forEach(item => {
        // eslint-disable-next-line no-unused-expressions
        item.lotVO3List &&
          item.lotVO3List.forEach(children => {
            if (children.list?.length > 0) {
              children.list?.forEach(childrens => {
                if (childrens.primaryUomQty > 0 && childrens.materialType !== 'B') {
                  list.push(childrens);
                }
              });
            }
          });
        if (item.substituteList && item.substituteList[0]) {
          item.substituteList.forEach(children => {
            if (children.lotVO3List?.length > 0) {
              children.lotVO3List?.forEach(childrens => {
                if (childrens.list && childrens.list[0]) {
                  childrens.list?.forEach(childrenss => {
                    if (childrenss.primaryUomQty > 0 && childrenss.materialType !== 'B') {
                      list.push(childrenss);
                    }
                  });
                }
              });
            }
          });
        }
      });
      setSelected([...list]);
    } else {
      setSelected([]);
    }
  }, [autoSelectFlag])

  const handleAutoSelect = (value) => {
    setAutoSelectFlag(value);
  }

  const handleChangeDrag = () => {
    if (!workOrderData?.eoId) {
      return;
    }
    setDragFlag(true);
  }

  const afterRenders = () => {
    return (
      !dragFlag ? (
        <>
          <Button
            color="primary"
            disabled
          >
            {intl.get(`${modelPrompt}.btn.callMaterial`).d('叫料')}
          </Button>
          <Button
            color="primary"
            disabled={!selected[0]}
            onClick={onConfirmMaterial}
          >
            {intl.get('tarzan.common.button.confirm').d('确定')}
          </Button>
        </>
      ) : (
        <>
          <Button
            color="primary"
            onClick={() => setDragFlag(false)}
          >
            {intl.get('tarzan.common.button.cancel').d('取消')}
          </Button>
          <Button
            color="primary"
            onClick={onConfirmDrag}
          >
            {intl.get('tarzan.common.button.confirm').d('确认')}
          </Button>
        </>
      )
    )
  };

  const dragComponentsRowProps = {
    canEdit: dragFlag,
    countQuantityADD,
    handleSelectCard,
    openNumberModal,
    selected,
  }

  return (
    <CardLayout.Layout spinning={loading} className={styles.machineMaterials}>
      <CardLayout.Header
        className='MachineMaterialsHead'
        title={
          materialData?.length > 0 ? `${intl.get(`${modelPrompt}.title.before`).d('机台物料')} ${intl.get(`${modelPrompt}.title.sum`).d('共')}${materialData.length}${intl.get(`${modelPrompt}.title.after`).d('行')}` : intl.get(`${modelPrompt}.machineMaterialsHead.title`).d('机台物料')
        }
        help={props?.cardUsage?.remark}
        content={
          !dragFlag &&
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {/* <div style={{ color: '#fff', whiteSpace: 'nowrap' }}>自动选择:</div> */}
            <div style={{ color: '#fff', whiteSpace: 'nowrap' }}>{intl.get(`${modelPrompt}.autoSelect`).d('自动选择:')}</div>
            <Switch
              name="autoSelect"
              style={{ margin: '.1rem', marginLeft: '0', marginRight: '.2rem' }}
              unCheckedChildren={intl.get(`${modelPrompt}.switch.close`).d('关')}
              defaultChecked={false}
              disabled={!workOrderData?.eoId}
              onChange={value => { handleAutoSelect(value) }}
            >
              {intl.get(`${modelPrompt}.switch.open`).d('开')}
            </Switch>
            <div onClick={handleChangeDrag} style={{ cursor: 'pointer' }}>
              <img src={drag} alt='' style={{ height: '26px', marginRight: '10px', cursor: 'pointer' }} />
            </div>
            <TextField
              placeholder={intl.get(`${modelPrompt}.scanBarcodeRequired`).d('请扫描条码')}
              id={`operationPlatformInput${props.priorityLayout?.filter(item => item.i === '2')[0]?.priority}`}
              name="scanCode"
              onEnterDown={e => onScanMaterial(e.target.value)}
              prefix={<img src={scanIcon} alt='' style={{ height: '19px' }} />}
            />
          </div>
        }
        addonAfter={afterRenders()}
      />
      <CardLayout.Content className='MachineMaterialsForm'>
        {isArray(materialData) &&
          materialData.map((item, index) => {
            return (
              <>
                <div
                  style={{
                    background: item?.lotVO3List[0]?.list &&
                      item?.lotVO3List[0]?.list[0]?.materialType === 'A'
                      ? '#2f6481'
                      : '#397698',
                    display: 'block',
                    margin: '0 8px 8px 8px',
                    opacity: item?.lotVO3List[0]?.list &&
                      item?.lotVO3List[0]?.list[0]?.materialType === 'A'
                      ? '0.5'
                      : '1',
                  }}
                >
                  <div className={styles.laneTitle}>
                    {item.substituteList && item.substituteList[0] && (
                      <a
                        style={{
                          marginLeft: '6px',
                          fontSize: '0.9vw',
                          color: 'white',
                        }}
                        onClick={() => setExpandForm(!expandForm)}
                      >
                        <Icon type={expandForm ? 'caret-right' : 'caret-down'} />
                      </a>
                    )}
                    <div className={styles.materialInfo}>
                      <span style={{ marginRight: '8px' }}>{item.sequence}</span>{item.materialCode}/{item.materialName}
                      {
                        item?.lotVO3List[0]?.list && item?.lotVO3List[0]?.list[0]?.revisionCode ?
                          (
                            <span style={{ color: 'white', marginLeft: '4px' }}>
                              {item.lotVO3List[0].list[0].revisionCode}
                            </span>
                          ) : ''
                      }
                      {
                        item?.lotVO3List[0]?.list && item?.lotVO3List[0]?.list[0]?.uomName ?
                          (
                            <span style={{ color: 'white' }}>
                              {`(${item.lotVO3List[0].list[0].uomName})`}
                            </span>
                          ) : ''
                      }
                    </div>
                    {item?.lotVO3List[0]?.list && item?.lotVO3List[0]?.list[0]?.materialType !== 'A' && (
                      <div className={styles.numberInfo}>
                        {intl.get(`${modelPrompt}.numberTitle`).d('需求用量/预投数量/追投数量:')}
                        {item?.lotVO3List[0]?.list[0]?.unitQty}/
                        {countQuantity(item)}/
                        {countQuantityExpectQty(item)}
                      </div>
                    )}
                  </div>
                  {item.lotVO3List &&
                    item.lotVO3List.map((lotItem, lotIndex) => {
                      return (
                        <div style={{ display: 'flex' }}>
                          <div className={styles.titleMater}>
                            <span style={{ fontSize: 13 }}>{lotItem.assemblePointCode}</span>
                            {lotItem.substituteFlag === 'Y' && (
                              // <span className={styles.Alternative}>替代料</span>
                              <span
                                style={{
                                  marginLeft: '6px',
                                  borderRadius: '2px 4px',
                                  padding: '2px 4px',
                                  backgroundColor: 'rgba(224, 190, 94, 1)',
                                  color: '#3c87ad',
                                  fontWeight: 'bolder',
                                }}
                              >
                                T
                              </span>
                            )}
                          </div>
                          <div style={{
                            flex: 1,
                            display: 'flex',
                            flexWrap: 'wrap',
                            paddingLeft: '8px',
                            // background: countQuantity(item) === 0 ? null : countQuantity(item) < item?.lotVO3List[0]?.list[0]?.unitQty
                            //   ? '#338fe980'
                            //   : '#3cbf9680',
                          }}>
                            {lotItem.list && (
                              <DndProvider backend={HTMLBackend}>
                                <DragComponentsRow list={lotItem.list} currentItem={item} currentIndex={index} currentLotIndex={lotIndex} {...dragComponentsRowProps} handleResetList={handleResetList} />
                              </DndProvider>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  {!expandForm &&
                    item.substituteList &&
                    item.substituteList[0] &&
                    item.substituteList.map(items => {
                      return (
                        <>
                          {/* <br /> */}
                          <span
                            style={{
                              background: items?.lotVO3List[0]?.list &&
                                items?.lotVO3List[0]?.list[0]?.materialType === 'A'
                                ? '#2f6481'
                                : '#397698',
                              display: 'flex',
                              opacity: items?.lotVO3List[0]?.list &&
                                items?.lotVO3List[0]?.list[0]?.materialType === 'A'
                                ? '0.5'
                                : '1',
                              fontSize: '0.9vw',
                            }}
                          >
                            <span style={{ color: 'white', marginLeft: '10px' }}>
                              {items.materialCode}/{items.materialName}
                            </span>
                            <span style={{ color: 'white', marginLeft: '10px' }}>
                              {items?.lotVO3List[0]?.list &&
                                items?.lotVO3List[0]?.list[0]?.revisionCode}
                            </span>
                            {/* {items?.lotVO3List[0]?.list[0]?.materialType !== 'A' && (
                                        <>
                                          <span style={{ color: 'white', marginLeft: '10px' }}>
                                            预投数量/单位用量: {countQuantity(items)}
                                            {items?.lotVO3List[0]?.list[0]?.inputUomCode}/
                                            {items?.lotVO3List[0]?.list[0]?.unitQty}
                                            {items?.lotVO3List[0]?.list[0]?.uomCode}
                                          </span>
                                          <span style={{ color: 'white', marginLeft: '10px' }}>
                                            {countQuantity(items) === 0
                                              ? '未投料'
                                              : countQuantity(items) < items?.lotVO3List[0]?.list[0]?.unitQty
                                                ? '投料中'
                                                : '投料完成'}
                                          </span>
                                        </>
                                      )} */}
                            {/* <br /> */}
                            {items.lotVO3List &&
                              items.lotVO3List.map(lotItem => {
                                return (
                                  <div style={{ display: 'flex' }}>
                                    <div className={styles.titleMater}>
                                      <span style={{ fontSize: 13 }}>
                                        {lotItem.assemblePointCode}
                                      </span>
                                      {lotItem.substituteFlag === 'Y' && (
                                        // <span className={styles.Alternative}>替代料</span>
                                        <span
                                          style={{
                                            marginLeft: '6px',
                                            borderRadius: '2px 4px',
                                            padding: '2px 4px',
                                            backgroundColor: 'rgba(224, 190, 94, 1)',
                                            color: '#3c87ad',
                                            fontWeight: 'bolder',
                                          }}
                                        >
                                          T
                                        </span>
                                      )}
                                    </div>
                                    {lotItem.list && (
                                      <DndProvider backend={HTMLBackend}>
                                        <DragComponentsRow list={lotItem.list} currentItem={items}
                                          {...dragComponentsRowProps} type="substituteList" />
                                      </DndProvider>
                                    )}
                                  </div>
                                );
                              })}
                          </span>
                        </>
                      );
                    })}
                </div>
              </>
            );
          })}
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default MachineMaterials;
