/**
 * @Description: 客制化表单显示
 * @Author: <<EMAIL>>
 * @Date: 2023-07-06 17:28:51
 * @LastEditTime: 2023-07-27 20:02:46
 * @LastEditors: <<EMAIL>>
 */
import React, { useRef, useCallback, useEffect, useState } from 'react';
import { Tooltip } from 'choerodon-ui/pro';
import { Row, Col } from 'choerodon-ui';
import styles from './index.module.less';

// const _responseData = {
//   materialInfo: 'materialInfo',
//   customerInfo: 'customerInfo',
//   woRemark: 'woRemark',
//   standardBeat: 'standardBeat',
//   eoNum: 'eoNum',
//   arriveTime: 'arriveTime',
//   currentProcess: 'currentProcess',
//   nextProcess: 'nextProcess',
//   dispatchRemark: 'dispatchRemark',
//   processedTime: 'processedTime',
// }

// 可用字体
type FontFamily = 'cursive' | 'serif' | 'fantasy' | 'monospace' | 'auto';

// 字段属性
export type FieldObject = {
  field: string; // 对应接口返回字段
  index: number; // 排序
  colSpan: number; // 占几列  1 || 2
  label: string; // 显示标签label描述
  labelWidth: number | string, // label宽度  121 || '30%'
  labelFontSize: number; // label字体大小
  labelFontFamily: FontFamily; // label字体
  labelVisible: boolean; // label是否可见
  labelColor: string; // label的颜色
  valueType: 'value' | 'timer', // 返回值value的类型  value-显示返回值 || timer-显示计时器
  valueFontSize: number; // value字体大小
  valueFontFamily: FontFamily; // value字体
  valueVisible: boolean; // value是否可见
  valueColor: string; // value的颜色
}

// 字段配置
// const _renderProps: {
//   [key: string]: FieldObject
// } = {
//   arriveTime: {
//     field: 'arriveTime',
//     index: 0,
//     colSpan: 1,
//     label: '到达时间',
//     labelWidth: '30%',
//     labelFontSize: 12,
//     labelFontFamily: 'cursive',
//     labelVisible: true,
//     labelColor: 'orange',
//     valueType: 'value',
//     valueFontSize: 34,
//     valueFontFamily: 'fantasy',
//     valueVisible: true,
//     valueColor: 'black',
//   },
//   materialCode: {
//     field: 'materialCode',
//     index: 1, // 顺序
//     colSpan: 2, // 跨行
//     label: '物料信息',
//     labelWidth: '30%',
//     labelFontSize: 24,
//     labelFontFamily: 'cursive',
//     labelVisible: true,
//     labelColor: 'blue',
//     valueType: 'value',
//     valueFontSize: 24,
//     valueFontFamily: 'monospace',
//     valueVisible: true,
//     valueColor: 'blue',
//   },
//   customerDesc: {
//     field: 'customerDesc',
//     index: 2,
//     colSpan: 1,
//     label: '客户信息',
//     labelWidth: '30%',
//     labelFontSize: 24,
//     labelFontFamily: 'serif',
//     labelVisible: true,
//     labelColor: 'red',
//     valueType: 'value',
//     valueFontSize: 24,
//     valueFontFamily: 'auto',
//     valueVisible: true,
//     valueColor: 'blue',
//   },
//   remark: {
//     field: 'remark',
//     index: 3,
//     colSpan: 1,
//     label: '工单备注',
//     labelWidth: '30%',
//     labelFontSize: 24,
//     labelFontFamily: 'auto',
//     labelVisible: true,
//     labelColor: 'blue',
//     valueType: 'value',
//     valueFontSize: 24,
//     valueFontFamily: 'auto',
//     valueVisible: true,
//     valueColor: 'blue',
//   },
//   identificationField: {
//     field: 'identificationField',
//     index: 4,
//     colSpan: 1,
//     label: '在制标识',
//     labelWidth: '30%',
//     labelFontSize: 12,
//     labelFontFamily: 'cursive',
//     labelVisible: true,
//     labelColor: 'orange',
//     valueType: 'value',
//     valueFontSize: 34,
//     valueFontFamily: 'auto',
//     valueVisible: true,
//     valueColor: 'black',
//   },
//   processedTime: {
//     field: 'processedTime',
//     index: 5,
//     colSpan: 2,
//     label: '操作时间',
//     labelWidth: '30%',
//     labelFontSize: 12,
//     labelFontFamily: 'cursive',
//     labelVisible: true,
//     labelColor: 'orange',
//     valueType: 'timer',
//     valueFontSize: 34,
//     valueFontFamily: 'auto',
//     valueVisible: true,
//     valueColor: 'black',
//   },
// }

const getTimes = t => {
  if (!t) {
    return;
  }
  let h: number | string = parseInt(String((t / 60 / 60) % 24), 10);
  let m: number | string = parseInt(String((t / 60) % 60), 10);
  let s: number | string = parseInt(String(t % 60), 10);
  // 三元表达式 补零 如果小于10 则在前边进行补零 如果大于10 则不需要补零
  if (t < 60) {
    s = s < 10 ? `0${s}` : s;
    return `${s}秒`;
  }
  if (t >= 60 && t < 3600) {
    m = m < 10 ? `0${m}` : m;
    s = s < 10 ? `0${s}` : s;
    return `${m}分${s}秒`;
  }
  h = h < 10 ? `0${h}` : h;
  m = m < 10 ? `0${m}` : m;
  s = s < 10 ? `0${s}` : s;
  return `${h}时${m}分${s}秒`;
};

const RenderForm = ({ renderProps, responseData, computingTime = false, style = {} }) => {
  // eslint-disable-next-line no-undef
  const timer = useRef<NodeJS.Timer | null>(null);

  const [time, setTime] = useState(0);
  const [rowList, setRowList] = useState<any>([]);

  useEffect(() => {
    const formMap: Map<string, FieldObject> = new Map(Object.entries(renderProps))
    const dataList = [...formMap.values()].sort((a, b) => a.index - b.index);
    const _rowList: any[] = [];
    let tempRow: any[] = [];

    dataList.forEach((item, index) => {
      if (item.colSpan === 2) {
        // 占据一行
        if (tempRow.length) {
          // 已有行
          _rowList.push(renderHalfRow(tempRow))
          tempRow = []
        }
        _rowList.push(
          <Row className={styles.cardRow}>
            {renderCol(item)}
          </Row>,
        )
      } else if (item.colSpan === 1) {
        if (tempRow.length === 2) {
          _rowList.push(renderHalfRow(tempRow))
          tempRow = [];
        }
        tempRow.push(item)
      }
      if ((index + 1) === dataList.length) {
        _rowList.push(renderHalfRow(tempRow))
      }
    })
    setRowList(_rowList)
  }, [renderProps, responseData, time])

  useEffect(() => {
    setTime(responseData.processedTime)
  }, [responseData.processedTime])

  useEffect(() => {
    if (computingTime) {
      timer.current = setInterval(() => {
        setTime((prev) => prev + 1);
      }, 1000)
    }
    return () => {
      if (timer.current) {
        clearInterval(timer.current);
      }
    }
  }, [computingTime])

  const renderCol = useCallback(
    (col: FieldObject) => (
      <Col span={col.colSpan * 12} className={styles.cardCol} >
        <div
          className={styles.label}
          style={{
            fontSize: col.labelFontSize,
            fontFamily: col.labelFontFamily,
            color: col.labelColor,
            width: col.labelWidth,
          }}
        >
          <Tooltip title={col.label} theme="light">
            {col.labelVisible ? `${col.label}` : ''}
          </Tooltip>
        </div>
        <div
          className={styles.content}
          style={{
            fontSize: col.valueFontSize,
            fontFamily: col.valueFontFamily,
            color: col.valueColor,
          }}
        >
          {col.valueType === 'timer' ?
            getTimes(time)
            : col.field === 'materialCode' ?
              <Tooltip title={responseData[col.field]} theme="light">
                {col.valueVisible && responseData[col.field] ? `${responseData[col.field]}/${responseData.materialName}` : ''}
              </Tooltip>
              : <Tooltip title={responseData[col.field]} theme="light">
                {col.valueVisible ? responseData[col.field] : ''}
              </Tooltip>
          }
        </div>
      </Col>
    ),
    [time, responseData],
  )

  const renderHalfRow = useCallback(
    (_tempRow) => (
      <Row className={styles.cardRow}>
        {
          _tempRow.map(col => renderCol(col))
        }
      </Row>
    ),
    [time, responseData],
  )

  return (
    <div style={style} className={styles.cardCustomizeForm}>{rowList.map(item => item)}</div>
  )
}

export default RenderForm;
