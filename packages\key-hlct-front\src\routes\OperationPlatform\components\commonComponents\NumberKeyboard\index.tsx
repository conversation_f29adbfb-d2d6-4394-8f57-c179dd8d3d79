/**
 * @Description: 数字键盘
 * @Author: <<EMAIL>>
 * @Date: 2023-07-27 19:57:23
 * @LastEditTime: 2023-08-04 10:49:04
 * @LastEditors: <<EMAIL>>
 */
import React, { useState, useCallback, useEffect } from 'react';
import { Icon } from 'choerodon-ui/pro';
import styles from './index.module.less';

export interface NumberKeyBoardProps {
  keyboardWidth: number; // 键盘宽度
  defaultValue?: string; // 初始值
  onOk: (value: string) => boolean; // 确定按钮事件，value: 当前数量，boolean 是否执行成功，成功清空数量
  onReturn: (value: string) => boolean;
  numberKeyboardProps?: RecordsProps; // 工序作业平台传递的参数
}

type RecordsProps = {
  materialLotTop?: boolean;
  checkedMaterialLot?: boolean;
  workOrderData?: Object;
  uomName?: string;
}
const keyCode2Number = {
  48: '0', 49: '1', 50: '2', 51: '3', 52: '4', 53: '5', 54: '6', 55: '7',
  56: '8', 57: '9', 96: '0', 97: '1', 98: '2', 99: '3', 100: '4', 101: '5',
  102: '6', 103: '7', 104: '8', 105: '9',
}

const NumberKeyboard = ({ defaultValue = '', keyboardWidth, onOk, onReturn, numberKeyboardProps = {} }: NumberKeyBoardProps) => {
  const {
    materialLotTop,
    checkedMaterialLot,
    workOrderData,
    uomName,
  } = numberKeyboardProps;

  const [value, setValue] = useState(defaultValue);
  const [locked, setLocked] = useState(false);
  const [numberInputFocus, setNumberInputFocus] = useState(false);

  useEffect(() => {
    if (numberInputFocus && !locked) {
      window.addEventListener('keydown', onKeyDown);
    } else {
      window.removeEventListener('keydown', onKeyDown);
    }
    return () => {
      window.removeEventListener('keydown', onKeyDown);
    }
  }, [numberInputFocus, locked])

  const onKeyDown = useCallback(
    (e) => {
      const str = keyCode2Number[e.keyCode];
      switch (e.keyCode) {
        case 8:
          sub();
          break;
        case 110:
        case 190:
          handleAddPoint();
          break;
        default:
          if (str) {
            add(str)
          }
          break;
      }
    },
    [],
  )

  const focusChange = useCallback(
    (val: boolean) => {
      setNumberInputFocus(val);
    },
    [],
  )

  const add = useCallback(
    (val: string) => {
      if (locked) {
        return;
      }
      setValue(prev => `${prev}${val}`)
    },
    [locked],
  );

  const clear = useCallback(
    () => {
      if (locked) {
        return;
      }
      setValue('')
    },
    [locked],
  )


  const sub = useCallback(
    () => {
      if (locked) {
        return;
      }
      setValue(prev => prev.length ? prev.slice(0, prev.length - 1) : '')
    },
    [locked],
  )

  const handleAddPoint = useCallback(
    () => {
      if (locked) {
        return;
      }
      setValue((prev) => {
        if (!prev) {
          return '0.'
        }
        if (prev.split('.').length > 1) {
          return prev;
        }
        return `${prev}.`;
      })
    },
    [locked],
  )

  const handleClickOk = () => {
    if(!!materialLotTop && !checkedMaterialLot){
      return;
    }
    if (onOk(value) && !locked) {
      // 没锁就清空
      setValue('')
    };
  }

  const handleClickReturn = () => {
    if(workOrderData && (!value || !workOrderData?.workOrderId) && workOrderData?.doneStepFlag !== 'Y'){
      return;
    }

    if(onReturn(value) && !locked) {
      // 没锁就清空
      setValue('')
    };
  }

  return (
    <div className={`${styles.keyboard} ${locked ? styles.fontDisabled : ''}`} style={{ width: keyboardWidth, height: keyboardWidth + 10 }}>
      <div className={styles.keyboardRow}>
        <div onFocus={() => focusChange(true)} onBlur={() => focusChange(false)} tabIndex={-999} className={styles.inputArea}>
          {value}
          {
            locked ? <Icon type="lock_outline" onClick={() => { setLocked(false) }} /> :
              <Icon type="lock_open" onClick={() => { setLocked(true) }} />
          }
        </div>
        <div className={styles.uom}>
          {uomName || '个'}
        </div>
      </div>
      <div className={styles.keyboardRow}>
        <div className={styles.normalBtn} onClick={() => add('7')}>7</div>
        <div className={styles.normalBtn} onClick={() => add('8')}>8</div>
        <div className={styles.normalBtn} onClick={() => add('9')}>9</div>
        <div className={`${styles.normalBtn} ${workOrderData && (!value || !workOrderData?.workOrderId) && workOrderData?.doneStepFlag !== 'Y' ? styles.backBtn : styles.backDisabled}`} style={{ fontSize: '12px' }} onClick={handleClickReturn}>报工退回</div>
      </div>
      <div className={styles.keyboardRow}>
        <div className={styles.normalBtn} onClick={() => add('4')}>4</div>
        <div className={styles.normalBtn} onClick={() => add('5')}>5</div>
        <div className={styles.normalBtn} onClick={() => add('6')}>6</div>
        <div className={styles.normalBtn} style={{ fontSize: '14px' }} onClick={clear}>清除</div>
      </div>
      <div className={styles.keyboardRow}>
        <div className={styles.normalBtn} onClick={() => add('1')}>1</div>
        <div className={styles.normalBtn} onClick={() => add('2')}>2</div>
        <div className={styles.normalBtn} onClick={() => add('3')}>3</div>
        <div className={styles.normalBtn} style={{ fontSize: '14px' }} onClick={sub}>退格</div>
      </div>
      <div className={styles.keyboardRow}>
        <div className={styles.normalBtn} onClick={handleAddPoint}>.</div>
        <div className={styles.normalBtn} onClick={() => add('0')}>0</div>
        <div className={`${styles.normalBtn} ${(!!materialLotTop && !checkedMaterialLot) ? styles.btnDisabled : styles.submitBtn}`} onClick={handleClickOk}>确定</div>
      </div>
    </div>
  )
};

export default NumberKeyboard;
