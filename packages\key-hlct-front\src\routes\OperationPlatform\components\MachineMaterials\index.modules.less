.machineMaterials {
  :global {
    .c7n-divider.c7n-divider-horizontal {
      margin: 0 0 !important;
    }

    .c7n-card-body {
      padding: 0 !important;
      background: #395470;
    }

    .c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper .c7n-pro-switch:checked + .c7n-pro-switch-label{
      color: rgba(51, 241, 255, 1);
      background: rgba(51, 241, 255, 0.2);
    }
    .c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper .c7n-pro-switch:checked + .c7n-pro-switch-label::after{
      background: rgba(51, 241, 255, 1);
    }
    .c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper .c7n-pro-switch-label{
      color: rgba(255, 255, 255, 0.5);
      background: rgba(255, 255, 255, 0.2);
    }
    .c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper.c7n-pro-switch-wrapper .c7n-pro-switch-label::after{
      background: rgba(255, 255, 255, 0.5);
    }
  }
}

.titleMater {
  flex-grow: 0;
  flex-shrink: 0;
  color: white;
  // background: #45acf140;
  // margin: 0.08rem;
  // width: 60px;
  /* text-align: center; */
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  flex-direction: column;

  .Alternative {
    background: #ffffff40;
    border-radius: 10px;
    padding: 3px;
  }
}

.hzero-draggable-card-content {
  background: #38708f !important;
}

.numberModal {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }

    .icon-close {
      color: white !important;
      top: 0 !important;
    }
  }
}

.modalForm {
  :global {
    .c7n-pro-field-label {
      color: white !important;
      font-size: 18px;
    }

    .c7n-pro-output {
      color: white !important;
    }

    .c7n-pro-select-wrapper {
      background: #50819c !important;
      color: white !important;
    }
  }
}

.MachineMaterialsModal {
  :global {
    .c7n-pro-modal-header {
      background-color: #3c87ad !important;
      padding: 8px 16px 8px !important;
    }

    .c7n-pro-modal-title {
      color: white !important;
    }
  }

  .icon-refresh {
    background-color: rgb(91, 136, 160) !important;
    color: white !important;
  }

  .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-thead .c7n-pro-table-cell {
    background-color: #3c87ad !important;
    color: white !important;
    border: none !important;
  }

  .c7n-pro-modal-content .c7n-pro-modal-body .c7n-spin-nested-loading .c7n-spin-container .c7n-pro-table-content .c7n-pro-table-tbody .c7n-pro-table-cell {
    background-color: #3c87ad !important;
    color: white !important;
  }

  .c7n-pro-pagination-wrapper .c7n-pro-select-wrapper {
    background-color: #3c87ad !important;
    color: white !important;
  }

  .c7n-pro-btn-wrapper {
    background-color: #3c87ad !important;
    color: white !important;
  }

  .icon {
    color: white !important;
  }

  .c7n-pro-pagination-perpage {
    color: white !important;
  }

  .c7n-pro-pagination-page-info {
    color: white !important;
  }
}

.laneTitle {
  padding-right: 4px;
  display: inline-flex;
  width: 100%;
  font-size: 0.9vw;

  .materialInfo {
    color: white;
    margin-left: 10px;
    flex-grow: 1;
    width: 50%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .numberInfo {
    .materialInfo;
    text-align: right;
    margin-left: 0;
  }
}
