import React, { useMemo, useEffect } from 'react';
import leftTopLog from '@/assets/operationPlatformCard/leftTopLog.svg';
import close from '@/assets/operationPlatformCard/close.png';
import intl from 'utils/intl';
import { Table, DataSet } from 'choerodon-ui/pro';
import { siteDS } from './stores';
import styles from './index.module.less';

export interface MachinedPartPrintProps {
  catdType: string; // 卡片类型
  enterInfo: object; // 工位信息
  handleCloseSiteModal: () => boolean;
  siteModalShow: boolean;
  contentClassName: string;
  onFetchProcessed: (arg0: string) => void;
  handleFetchLovData: (arg0: string, arg1?: string, arg2?: string) => void;
}

const modelPrompt = 'tarzan.operationPlatform';

const OnSiteProduction = ({catdType, enterInfo = {}, handleCloseSiteModal, siteModalShow, contentClassName, onFetchProcessed, handleFetchLovData}: MachinedPartPrintProps) => {
  const siteDs = useMemo(() => new DataSet(siteDS()), []);

  useEffect(() => {
    if(siteModalShow){
      const container = document.querySelector(`.${styles.doneSiteFlagModals}`);
      if(container){
        document.getElementById('operationPlatform')?.appendChild(container);
        handleFilterPrint();
      }
    }
  }, [siteModalShow, document.querySelector(`.${styles.doneSiteFlagModals}`)]);

  useEffect(() => {
    listener(true);
    return function clean() {
      listener(false);
    };
  });

  const listener = flag => {
    // 列表交互监听
    if (siteDs) {
      const header = flag ? siteDs.addEventListener : siteDs.removeEventListener;
      // 头选中和撤销监听
      header.call(siteDs, 'select', handleHeaderTableDsSelect);
    }
  };

  // 处理头单选按钮选中事件
  const handleHeaderTableDsSelect = async({ record }) => {
    const identification = record.get('identification');
    const workOrderNum = record.get('workOrderNum');
    switch (catdType) {
      case 'NewProcessMachinedPart':
        await onFetchProcessed(identification);
        break;
      case 'WorkorderMachinedPart':
        await handleFetchLovData(workOrderNum, 'onSite', identification);
        // await onFetchProcessed(identification);
        break;
      case 'ProcessWorkorderMachinedPart':
        await handleFetchLovData(workOrderNum, 'onSite', identification)
        // await onFetchProcessed(identification);
        break;
      default:
        break;
    }
    await onHandleCloseStepModal();
  };

  const column = [
    {
      name: 'identification',
    },
    {
      name: 'creationDate',
      align: 'center',
    },
    {
      name: 'materialRevision',
    },
    {
      name: 'workOrderNum',
    },
    {
      name: 'workOrderStatusDesc',
    },
  ]

  const handleFilterPrint = () => {
    // @ts-ignore
    siteDs.setQueryParameter('operationId', enterInfo?.selectOperation?.operationId);
    // @ts-ignore
    siteDs.setQueryParameter('workcellId', enterInfo?.workStationId);
    siteDs.query();
  }

  const onHandleCloseStepModal = () => {
    const container = document.querySelector(`.${styles.doneSiteFlagModals}`);
    if(container){
      document.querySelector(`.${contentClassName}`)?.appendChild(container);
      handleCloseSiteModal();
    }
  };

  return (
    <div className={`${styles.doneSiteFlagModals}`} style={{display: `${siteModalShow ? 'block' : 'none'}`}}>
      <div className={styles.modalBox}>
        <div className={styles.modalTitle}>
          <div>
            <img src={leftTopLog} alt="" />
            {/* <span>站内在制</span> */}
            <span>{intl.get(`${modelPrompt}.onSiteProduction`).d('站内在制')}</span>
          </div>
          <div className={styles.modalRightFooter} id={styles.modalRightFooter}>
            <div
              onClick={() => {
                onHandleCloseStepModal();
              }}
            >
              <img src={close} alt="" />
            </div>
          </div>
        </div>
        <div className={styles.modalContent}>
          <Table
            dataSet={siteDs}
            // @ts-ignore
            columns={column}
            customizedCode="ProcessReportingRecords"
            showSelectionTips
            style={{
              maxHeight: 400,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default OnSiteProduction;
