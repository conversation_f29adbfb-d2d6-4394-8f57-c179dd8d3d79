/* eslint-disable jsx-a11y/alt-text */
import React, { useState, useEffect, useMemo } from 'react';
import { DataSet, Table, Form, Output } from 'choerodon-ui/pro';
import intl from 'utils/intl';
import { getCurrentOrganizationId } from 'utils/utils';
import request from 'utils/request';
import { BASIC } from '@utils/config';
import formatterCollections from 'utils/intl/formatterCollections';
import { useResizeObserver } from '../../useResizeObserver';
import { tableDS, formDS } from './stores/AssemblyProgressDS';
import { useOperationPlatform } from '../../contextsStore';
import { CardLayout, ONotification } from '../commonComponents';
import styles from './index.modules.less';

const tenantId = getCurrentOrganizationId();
const modelPrompt = 'tarzan.operationPlatform.assemblyProgress';

const AssemblyProgress = props => {
  const tableDs = useMemo(() => new DataSet(tableDS()), []);
  const formDs = useMemo(() => new DataSet(formDS()), []);
  const { workOrderData, cardMode } = useOperationPlatform();
  const [loading, setLoading] = useState(false);
  const [formColumns, setFormColumns] = useState(1); // 表单信息列数

  useResizeObserver((element, size) => {
    // console.log('Element size:', size, element);
    if (size.width <= 480) {
      setFormColumns(1);
    } else if (size.width > 480 && size.width <= 780) {
      setFormColumns(2);
    } else if (size.width > 780 && size.width <= 1090) {
      setFormColumns(3);
    } else {
      setFormColumns(4);
    }
    // 在这里执行你需要的操作，例如重新布局、更新状态等。
  }, document.querySelector('.assemblyProgressForm'));

  useEffect(() => {
    setLoading(props.spin);
  }, [props.spin]);

  // 查询
  useEffect(() => {
    if (workOrderData?.workOrderId) {
      handleQuery();
    }
  }, [workOrderData]);

  /**
   * table查询
   */
  const handleQuery = () => {
    setLoading(true);
    const params = {
      workOrderId: workOrderData?.workOrderId,
    };
    request(`${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-process-actuals/list/ui`, {
      method: 'GET',
      query: params,
    }).then(res => {
      if (res && res.success) {
        tableDs.loadData(res.rows.componentActualList || []);
        formDs.loadData([res.rows]);
        setLoading(false);
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'SUCCESS',
          message: intl.get(`${modelPrompt}.schedulingTaskQuerySuccess`).d('调度任务数据查询成功'),
          recordType: 'query',
        });
      } else {
        setLoading(false);
        ONotification.error({ message: res.message });
        props.handleAddRecords({
          cardCode: props.cardCode,
          messageType: 'FAIL',
          message: intl.get(`${modelPrompt}.schedulingTaskQueryFailure`).d('调度任务数据查询失败'),
          recordType: 'query',
        });
      }
    });
  };

  const columns = [
    {
      name: 'assembleQty',
      align: 'right',
    },
    {
      name: 'scrappedQty',
      align: 'right',
      renderer: ({ value }) => {
        return (
          <span style={{ color: 'rgba(244, 80, 99, 1)' }}>    {value}
          </span>
        );
      }
    },
    {
      name: 'assembleMaterialShowStr',
      width: 200,
      align: 'left',
      renderer: ({ record }) => {
        return (record.get('assembleMaterialShowStr'))
        // if(!record.get('assembleMaterialCode') && !record.get('assembleMaterialRevisionCode')){
        //   return '';
        // }
        // return (
        //   <span>
        //     {record.get('assembleMaterialCode')}/{record.get('assembleMaterialRevisionCode')}
        //   </span>
        // );
      },
    },
    {
      name: 'assembleMaterialName',
      width: 200,
      align: 'left',
    },
  ];

  const groups = [
    {
      name: 'bomComponentId',
      type: 'column',
      columnProps: {
        width: 80,
        align: 'left',
        title: intl.get(`${modelPrompt}.rownum`).d('行号'),
        lock: false,
        renderer: ({ record }) => <span>{record.get('lineNumber')}</span>,
      },
    },
    {
      name: 'bomComponentId',
      type: 'column',
      columnProps: {
        width: 260,
        align: 'left',
        title: intl.get(`${modelPrompt}.requiredMaterialCodeVersion`).d('需求物料编码/版本'),
        lock: false,
        renderer: ({ record }) => {
          return (record.get('materialShowStr'))
          // if(!record.get('materialCode') && !record.get('revisionCode')){
          //   return '';
          // }
          // return (
          //   <span>
          //     {record.get('materialCode')}/{record.get('revisionCode')}
          //   </span>
          // );
        },
      },
    },
    {
      name: 'bomComponentId',
      type: 'column',
      columnProps: {
        width: 230,
        align: 'left',
        title: intl.get(`${modelPrompt}.needMaterialName`).d('需求物料描述'),
        lock: false,
        renderer: ({ record }) => <span>{record.get('materialName')}</span>,
      },
    },
    {
      name: 'bomComponentId',
      type: 'column',
      columnProps: {
        align: 'right',
        title: intl.get(`${modelPrompt}.needQty`).d('需求数量'),
        lock: false,
        renderer: ({ record }) => <div style={{ textAlign: 'right' }}>{record.get('demandQty')}</div>,
      },
    },
    {
      name: 'bomComponentId',
      type: 'column',
      columnProps: {
        align: 'right',
        title: intl.get(`${modelPrompt}.dosageUnit`).d('单位用量'),
        lock: false,
        renderer: ({ record }) => <div style={{ textAlign: 'right' }}>{record.get('perQty')}</div>,
      },
    },
    {
      name: 'bomComponentId',
      type: 'column',
      columnProps: {
        width: 150,
        align: 'center',
        title: intl.get(`${modelPrompt}.assemblySchedule`).d('装配进度'),
        lock: false,
        renderer: ({ record }) => {
          return (
            <div className={styles['qty-box']}>
              <div className="qty-bg">
                <div className="qty-bg-box">
                  <div
                    className="qty-bg-inner"
                    style={{
                      width: `${parseInt(record.get('assembleProgress') * 100)}%`,
                    }}
                  ></div>
                </div>
              </div>
              <div className="qty-front">
                {`${parseInt(record.get('assembleProgress') * 100)}%`}
              </div>
            </div>
          );
        },
      },
    },
  ];

  return (
    <CardLayout.Layout className={styles.assemblyProgress} spinning={loading}>
      <CardLayout.Header
        title={intl.get(`${modelPrompt}.title`).d('装配进度')}
        help={props?.cardUsage?.remark}
      />
      <CardLayout.Content className='assemblyProgressForm'>
        <Form dataSet={formDs} labelWidth={150} columns={cardMode === 'Tile' ? formColumns : 4} style={{ flex: 1, background: 'rgba(42, 99, 130, 1)' }}>
          <Output name="workOrderNum" />
          <Output
            name="woMaterialShowStr"
          // renderer={({ record }) => {
          //   if(!record?.get(' woMaterialCode') && !record?.get('woRevisionCode')){
          //     return '';
          //   }
          //   return (
          //     <span>
          //       {record?.get(' woMaterialCode')}/{record?.get('woRevisionCode')}
          //     </span>
          //   );
          // }}
          />
          <Output name="qty" />
          <Output name="kitQty" />
        </Form>
        <div id="assemblyProgressTable">
          <Table
            dataSet={tableDs}
            columns={columns}
            groups={groups}
          // customizedCode='AssemblyProgress'
          />
        </div>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(AssemblyProgress);
