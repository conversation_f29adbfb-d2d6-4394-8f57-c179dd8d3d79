/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-08-15 16:19:54
 * @LastEditTime: 2023-08-30 16:24:53
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { HZERO_FILE } from 'utils/config';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

export type SopEnclosureType = {
  /**
   * 主键ID
   */
  sopFileId: number,
  /**
   * SOP头ID
   */
  sopHeaderId: number,
  /**
   * 附件URL
   */
  fileUrl: string,
  /**
   * 附件名称
   */
  fileName: string,
  /**
   * 所在文件夹uuid
   */
  attachmentUuid: string,
  /**
   * 创建人名称
   */
  createdByName: string,
  /**
   * 序号
   */
  lineNumber: number,
}

/**
 * 获取作业指导书文件List
 */
export function GetSopEnclosure() {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-sop-card/sop-enclosure/ui`,
    method: 'GET',
  };
}

export interface FetchAttachmentFilesPayload {
  bucketName?: string;
}

// 获取指定附件ID的文件列表
export function FetchAttachmentFiles(attachmentUUID: string) {
  return {
    url: `${HZERO_FILE}/v1/${tenantId}/files/${attachmentUUID}/file`,
    method: 'GET',
  };
}

export interface PreviewFilePayload {
  bucketName: 'mes'; // 文件保存的桶名
  storageCode?: string; // 存储配置编码，不指定时使用默认配置
  url: string; // 文件url
}

// 预览指定文件
export function PreviewFile() {
  return {
    url: `${HZERO_FILE}/v1/${tenantId}/file-preview/by-url`,
    method: 'GET',
  };
}

// 根据fileUrl获取文件签证地址
export function GetFileSignedUrl() {
  return {
    // `/hfle/v1/${getCurrentOrganizationId()}/files/signedUrl?bucketName=${bucketName}&url=${url}&download=0`
    url: `${HZERO_FILE}/v1/${tenantId}/files/signedUrl`,
    method: 'GET',
  };
}
