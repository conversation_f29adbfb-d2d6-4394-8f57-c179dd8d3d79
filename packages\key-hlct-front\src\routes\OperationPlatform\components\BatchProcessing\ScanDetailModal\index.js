import React, {useMemo} from 'react';
import { Table } from 'choerodon-ui/pro'

const Index = ({scanModalDs, detailInfo}) => {

  const columns = useMemo(()=>{
    return [
      {
        name: 'identification',
        width: 250,
      },
      {
        name: 'materialCodeVersion',
      },
      {
        name: 'materialName',
      },
      {
        name: 'workOrderNum',
        width: 200,
      },
      {
        name: 'eoQty',
        width: 80
      },
    ]
  }, [])

  return (
    <Table dataSet={scanModalDs} columns={columns} footer={
      <>
        <span>种类：</span>
        <span>{detailInfo.materialQty}</span>
        <span style={{marginLeft: 30}}>总数：</span>
        <span>{detailInfo.detailQty }</span>
      </>
    } />
  );
};

export default Index;
