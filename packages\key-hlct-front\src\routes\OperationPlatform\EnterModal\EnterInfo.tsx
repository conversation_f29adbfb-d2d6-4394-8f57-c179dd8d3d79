/* eslint-disable no-unused-expressions */
// @ts-nocheck
/**
 * @Description: 工序作业平台登录Modal操作页面
 * @Author: <<EMAIL>>
 * @Date: 2023-2-26 18:50:17
 */
import React from 'react';
import intl from 'utils/intl';
import scanIcon from '@/assets/operationPlatformCard/scanIcon.svg';
import lovChoose from '@/assets/operationPlatformCard/lovChoose.svg';
import enterBackground from '@/assets/operationPlatformCard/enterBackground.svg';
import cardSvg from '@/assets/icons/operation.svg';
import { DataSet, Form, Spin, TextField, Table } from 'choerodon-ui/pro';
import C7nModal from '../C7nModal';
import { workStationLovDS } from './stores';
import './index.module.less';
import TabCardsRender from './TabCardsRender';
import { ONotification } from '../components/commonComponents';

const modelPrompt = 'tarzan.operationPlatform';
const primaryWorkcellKey = 'workcellId';
const workcellCardCode = 'workcellCode';
const workcellCardName = 'workcellName';
const primaryOperationKey = 'operationId';
const operationCardName = 'description';

interface FailedModalProps {
  fetchLoading: boolean;
  enterModalDs: DataSet;
  onFetchWorkStation: (value: string, loginType: string) => void;
  currentWorlCellList: Array;
  workcellSelectedId: number;
  handleSelectWorkcell: (item: object) => void;
  workCellList: Array;
  operationList: Array;
  operationSelectedId: number;
  handleSelectOperation: (item: object) => void;
  currentWorkcellSelectedId: number;
  handleSelectCurrentWorkcell: (item: object) => void;
}

let workCellModal;
// 工序作业平台
const EnterInfo = (props: FailedModalProps) => {
  const Modal = C7nModal;
  const {
    fetchLoading,
    enterModalDs,
    onFetchWorkStation,
    currentWorlCellList,
    workcellSelectedId,
    handleSelectWorkcell,
    workCellList,
    operationList,
    operationSelectedId,
    handleSelectOperation,
    currentWorkcellSelectedId,
    handleSelectCurrentWorkcell,
  } = props;

  const workStationLovDs = new DataSet({
    ...workStationLovDS(),
  });

  const columnWo = [
    {
      name: 'workcellCode',
    },
    {
      name: 'workcellName',
    },
  ];

  const onRow = ({ record }) => {
    return {
      onClick: () => {
        workStationLovDs.data.forEach(item => {
          item.isSelected = false;
        });
        record.isSelected = true;
      },
      onDoubleClick: () => {
        workCellModal.close();
        onFetchWorkStation(record.data?.workcellCode, 'lov')
      },
    };
  };

  const handleFetchLovData = (value?) => {
    workStationLovDs.queryDataSet?.current?.reset();
    if (value === 'click') {
      workStationLovDs.setQueryParameter('workcellCode', null);
    } else if (value) {
      workStationLovDs.setQueryParameter('workcellCode', value);
    } else {
      workStationLovDs.setQueryParameter('workcellCode', null);
      return;
    }
    workStationLovDs.query().then(res => {
      if (res && !res.failed) {
        if (res.content.length === 1) {
          onFetchWorkStation(res.content[0]?.workcellCode, 'lov')
          return
        }
        if (res.content.length === 0) {
          return ONotification.error({ message: intl.get(`${modelPrompt}.notification.workcellNull`).d('未查询到工位') });
        }
        workCellModal = Modal.open({
          header: (
            <div style={{ display: 'flex' }}>
              <img src={cardSvg} alt="" className="titleIcon" />
              <div className="c7n-pro-modal-title">{intl.get(`${modelPrompt}.workcellCode`).d('工位编码')}</div>
            </div>
          ),
          destroyOnClose: true,
          closable: false,
          style: {
            width: '70%',
          },
          mask: true,
          contentStyle: {
            background: '#38708F',
          },
          className: 'workOrderModals',
          children: <Table dataSet={workStationLovDs} columns={columnWo} onRow={onRow} />,
          okProps: {
            style: {
              background: '#00D4CD',
              color: 'white',
              borderColor: '#00d4cd',
            },
          },
          onOk: () => {
            onFetchWorkStation(workStationLovDs.selected[0].data?.workcellCode, 'lov')
          },
        })
      }
    });
  }

  return (
    <div id="acceptedPuted" className="operationPlatformEnter">
      <img src={enterBackground} alt='' className='cardBackground' />
      <Spin spinning={fetchLoading || false}>
        <Form
          className="enterModalForm"
          dataSet={enterModalDs}
          labelLayout="placeholder"
          labelWidth={80}
        >
          <TextField
            name="workStationCode"
            style={{ width: '421px', height: '52px' }}
            onChange={(value) => { handleFetchLovData(value) }}
            prefix={<img src={scanIcon} alt="" />}
            suffix={
              <img
                src={lovChoose}
                alt=''
                onClick={() => { handleFetchLovData('click') }}
              />
            }
          />
        </Form>
        <div style={{ color: 'white', marginTop: '10px', fontSize: '16px' }}>
          {intl.get(`${modelPrompt}.workcell.select`).d('工位选择：')}
        </div>
        <div style={{ marginTop: '10px', color: 'rgba(255, 255, 255, 0.85)', fontSize: '14px' }}>
          {intl.get(`${modelPrompt}.workcell.recently`).d('最近登录工位：')}
        </div>
        <div style={{ marginTop: '10px' }}>
          <TabCardsRender
            primaryCardId={primaryWorkcellKey}
            cardCode={workcellCardCode}
            cardName={workcellCardName}
            cardsList={currentWorlCellList}
            selectedCardId={currentWorkcellSelectedId}
            handleSelectCard={handleSelectCurrentWorkcell}
          />
        </div>
        <div style={{ marginTop: '10px', color: 'rgba(255, 255, 255, 0.85)', fontSize: '14px' }}>
          {intl.get(`${modelPrompt}.workcell.whole`).d('全部工位：')}
        </div>
        <div style={{ marginTop: '10px' }}>
          <TabCardsRender
            primaryCardId={primaryWorkcellKey}
            cardCode={workcellCardCode}
            cardName={workcellCardName}
            cardsList={workCellList}
            selectedCardId={workcellSelectedId}
            handleSelectCard={handleSelectWorkcell}
          />
        </div>
        <div style={{ color: 'white', marginTop: '10px', fontSize: '16px' }}>
          {intl.get(`${modelPrompt}.operation.select`).d('工艺：')}
        </div>
        <TabCardsRender
          primaryCardId={primaryOperationKey}
          cardCode=""
          cardName={operationCardName}
          cardsList={operationList}
          selectedCardId={operationSelectedId}
          handleSelectCard={handleSelectOperation}
        />
      </Spin>
    </div>
  );
};

export default EnterInfo;
