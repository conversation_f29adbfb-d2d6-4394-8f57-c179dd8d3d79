// 机台物料
import React, { useState, useMemo } from 'react';
import { Form, Button, DataSet, TextField, Table, Output, Switch } from 'choerodon-ui/pro';
import { ButtonColor } from 'choerodon-ui/pro/lib/button/enum';
import { ColumnAlign } from 'choerodon-ui/pro/lib/table/enum';
import { ColumnProps } from 'choerodon-ui/pro/lib/table/Column';
import { Record } from 'choerodon-ui/dataset';
import formatterCollections from 'utils/intl/formatterCollections';
import { CardLayout } from '../commonComponents';
import styles from './index.modules.less';
import intl from 'utils/intl';

const modelPrompt = 'tarzan.operationPlatform.failed.modal';

interface FailedModalProps {
  handleExecute: (record: Record, type: string) => void;
  failedFormDs: DataSet;
  failedTableDs: DataSet;
  enterInfo: object;
  containerDetail: object;
  handleChangeStep: (stepCompleteFlag: string) => void;
}
const FailedModal = (props: FailedModalProps) => {
  const { handleExecute, failedFormDs, failedTableDs, handleChangeStep } = props;
  const [loading, setLoading] = useState(false);

  const columns: ColumnProps[] = useMemo(() => {
    return [
      {
        name: 'operation',
        align: ColumnAlign.center,
        width: 160,
        renderer: ({ record }) => {
          return (
            <span className="action-link">
              {record?.get('containerCode') ? (
                <>
                  <Button
                    color={ButtonColor.primary}
                    onClick={() => handleExecute(record, 'REASSEMBLY')}
                  >
                    {intl.get(`${modelPrompt}.button.reload`).d('重装')}
                  </Button>
                  <Button
                    className={styles.orangeButton}
                    onClick={() => handleExecute(record, 'UNLOAD')}
                  >
                    {intl.get(`${modelPrompt}.button.clear`).d('清除')}
                  </Button>
                </>
              ) : (
                <Button
                  color={ButtonColor.primary}
                  onClick={() => handleExecute(record, 'STOWAGE')}
                  disabled={!failedFormDs.current?.get('containerId')}
                >
                  {intl.get(`${modelPrompt}.button.loadCurrentContainer`).d('装载当前容器')}
                </Button>
              )}
            </span>
          );
        },
      },
      {
        name: 'loadObjectCode',
        align: ColumnAlign.center,
        width: 240,
      },
      {
        name: 'material',
        align: ColumnAlign.center,
        renderer: ({record}) => `${record?.get('materialCode') }/${record?.get('materialCode')}`,
        width: 320,
      },
      {
        name: 'loadWorkOrderNum',
        align: ColumnAlign.center,
        width: 180,
      },
      // {
      //   name: 'locatorCode',
      //   align: ColumnAlign.center,
      // },
      {
        name: 'trxLoadQty',
        align: ColumnAlign.center,
        width: 100,
      },
    ];
  }, [failedTableDs]);

  return (
    <CardLayout.Layout spinning={loading} className={styles.container}>
      <CardLayout.Content style={{ position: 'relative' }}>
        <Form columns={4} dataSet={failedFormDs} labelWidth={130} className={styles.modalForm}>
          <TextField name="containerCode" disabled />
          <TextField name="containerTypeDescription" disabled />
          <Switch name="stepCompleteFlag" onChange={handleChangeStep} />
          <Output name="qty" />
        </Form>
        <Table id={styles.failedTable} dataSet={failedTableDs} columns={columns} highLightRow />
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({
  code: ['tarzan.operationPlatform', 'tarzan.common']
})(FailedModal);
