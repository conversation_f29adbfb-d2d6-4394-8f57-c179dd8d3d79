/**
 * @Description: 工单报工卡片-services
 * @Author: <<EMAIL>>
 * @Date: 2023-07-27 19:20:19
 * @LastEditTime: 2023-08-02 18:59:40
 * @LastEditors: <<EMAIL>>
 */

import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const tenantId = getCurrentOrganizationId();

/**
 * 获取工单展示信息
 * @function FetchWoDetail
 * @returns {object} fetch Promise
 */
export function FetchWoDetail(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/order/info/new/get`,
    method: 'POST',
  };
}

/**
 * 扫描物料批
 * @function ScanMaterialLot
 * @returns {object} fetch Promise
 */
export function ScanMaterialLot(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/scan/lot/verify`,
    method: 'POST',
  };
}

/**
 * 工单完工
 * @function CompleteWo
 * @returns {object} fetch Promise
 */
export function CompleteWo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/report/new/execute`,
    method: 'POST',
  };
}

/**
 * 报工退回
 * @function ReturnWo
 * @returns {object} fetch Promise
 */
export function ReturnWo(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/report/execute/back`,
    method: 'POST',
  };
}


/**
 * 物料批查询
 * @function FetchMaterialLotQty
 * @returns {object} fetch Promise
 */
export function FetchMaterialLotQty(): object {
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/return/lot/verify`,
    method: 'POST',
  };
}

/**
 * 加工开始
 */
export function ProgressStart(): object{
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/progress/start-new`,
    method: 'POST',
  };
}

/**
 * 加工结束
 */
export function ProgressEnd(): object{
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/progress/end-new`,
    method: 'POST',
  };
}

/**
 * 修改托盘重量
 */
export function ContainerUpdate(): object{
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/container/update/ui`,
    method: 'POST',
  };
}

/**
 * 扫描容器
 */
export function ContainerScan(): object{
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/container/scan/ui`,
    method: 'GET',
  };
}

/**
 * 退回
 */

export function ExectueBack(): object{
  return {
    url: `${BASIC.HMES_BASIC}/v1/${tenantId}/hme-wo-report/report/execute/new/back`,
    method: 'POST',
  };
}
