/* eslint-disable jsx-a11y/alt-text */
// 在制品工序组件
import React, { useEffect, useMemo, useState } from 'react';
import { DataSet, Row, Col, Tree, Progress, Table, Icon } from 'choerodon-ui/pro';
import { Tag } from 'choerodon-ui';
import formatterCollections from 'utils/intl/formatterCollections';
import { treeDS, tableDS } from './stores/ProcessComponentsDS';
import { useOperationPlatform } from '../../contextsStore';
import { CardLayout } from '../commonComponents';
import styles from './index.modules.less';

const ProcessComponents = props => {
  const tableDs = useMemo(
    () =>
      new DataSet({
        ...tableDS(),
      }),
    [],
  );
  const treeDs = useMemo(
    () =>
      new DataSet({
        ...treeDS(),
      }),
    [],
  );

  const { workOrderData } = useOperationPlatform();
  const [loading, setLoading] = useState(false);

  // 查询树
  useEffect(() => {
    if (workOrderData?.eoId) {
      tableDs.setQueryParameter('routerStepId', workOrderData?.routerStepId);
      tableDs.setQueryParameter('eoId', workOrderData?.eoId);
      tableDs.query().then(res => {
        console.log(res);
        if (res && !res.failed) {
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'SUCCESS',
            recordType: 'query',
            message: `表单数据查询成功`,
          });
        } else {
          props.handleAddRecords({
            cardCode: props.cardCode,
            messageType: 'FAIL',
            recordType: 'query',
            message: `表单数据查询失败`,
          });
        }
      });
    }
  }, [workOrderData]);

  useEffect(() => {
    setLoading(props.spin);
  }, [props.spin]);

  // 树节点
  const nodeRenderer = ({ record }) => {
    // 进度条取值逻辑：(原逻辑现已经废弃)
    // 1 有父级 取suMaterial的substituteUsage * 父级的vo3的eoQty
    // 2 没有父级 先取unitQty * vo3里的eoQty 没有unitQty就是判断vo3中的qty，如果有为100% 没有则为未投料 进度为0
    // 是否投料标签逻辑 (原逻辑现已经废弃)
    // 有父级 取父级的vo3?.qty，如果为0，则是未投料，如果父级的vo3?.qty大于等于子级的suMaterial?.substituteUsage，则为投料完成，其余为投料中
    // 没有父级 取当前级的vo3?.qty，如果vo3?.qty=0 则是未投料，如果vo3?.qty大于等于自己的unitQty，则是投料完成，其余为投料中
    const recordData = record.toData() || {};
    // 子节点
    if (recordData.parentId) {
      // 单位用量 unitQty
      // 已投数量 suInputQty
      return (
        <Row>
          <Col span={8}>
            <span>
              {recordData.suMaterial?.suRevisionCode}/{recordData.suMaterial?.suMaterialName}
            </span>
          </Col>
          <Col style={{ marginLeft: '-16px' }} span={4}>
            <span>
              {/* {recordData.suMaterial?.substituteUsage} */}
              {recordData.unitQty}
              {recordData.unitQty ? recordData.unitUomCode : ''}
            </span>
          </Col>
          <Col style={{ marginLeft: '3px' }} span={4}>
            {recordData.suInputQty}
            {recordData.suInputQty ? recordData.inputUomCode : ''}
          </Col>
          <Col span={4}>
            {/* <Progress
              value={recordData.inputProgress}
            /> */}
          </Col>
          <Col span={4}>
            {/* {recordData.vo3?.qty === 0 ? (
              <Tag color="gray-inverse">未投料</Tag>
            ) : recordData.vo3?.qty >
              recordData.suMaterial?.substituteUsage * recordData.vo3?.eoQty ? (
                <Tag color="green-inverse">投料完成</Tag>
              ) : (
                <Tag color="#108ee9">投料中</Tag>
              )} */}
          </Col>
        </Row>
      );
    }
    // 父节点
    // 已投数量 bomInputQty
    return (
      <Row className={styles.parentNodes}>
        <Col span={8}>
          <span>
            {record.get('bomMaterialCode')}/{record.get('bomMaterialName')}
          </span>
        </Col>
        <Col span={4}>
          <span>
            {recordData.unitQty}
            {recordData.unitQty ? recordData.unitUomCode : ''}
          </span>
        </Col>
        <Col span={4}>
          {recordData.bomInputQty}
          {recordData.bomInputQty ? recordData.inputUomCode : ''}
        </Col>
        <Col span={4}>
          <Progress value={recordData.inputProgress * 100} />
        </Col>
        <Col span={4}>
          {recordData.inputStatus === '未投料' ? (
            <Tag color="gray-inverse">未投料</Tag>
          ) : recordData.inputStatus === '投料完成' ? (
            <Tag color="green-inverse">投料完成</Tag>
          ) : (
            <Tag color="#108ee9">投料中</Tag>
          )}
        </Col>
      </Row>
    );
  };

  const widthFormat = record => {
    if (!record || !record?.get('assembleQty') || !record?.get('demandQty')) {
      return `0%`;
    }
    // let width = (record.get('assembleQty') / record.get('demandQty')) * 100;
    // if (width > 100) {
    //   width = 100;
    // }
    let width = 0;
    if(record.get('assembleQty') >= record.get('demandQty')){
      width = 100;
    }
    return `${width}%`;
  };

  const expandIcon = ({ record }) => {
    if (!record.get('parentId')) {
      return (
        <Icon
          onClick={() => {
            record.set('expand', !record.get('expand'));
          }}
          style={{
            fontSize: 16,
            color: `${record.get('expand') ? 'rgba(51, 241, 255, 1)' : 'rgba(255, 255, 255, 0.7)'}`,
          }}
          type={record.get('expand') ? 'baseline-arrow_drop_down' : 'baseline-arrow_right'}
        />
      );
    }
    return null;
  };

  const columns = [
    {
      name: 'lineNumber',
      width: 100,
      align: 'right',
      renderer: ({ value, record }) => {
        if (!record?.get('parentId')) {
          return value;
        }
        return '';
      },

    },
    {
      name: 'materialCode',
      width: 150,
      renderer: ({ value, record }) => {
        const showRender = [];
        if (record?.get('revisionCode')) {
          showRender.push(<span>{`${value}/${record?.get('revisionCode')}`}</span>);
        } else {
          showRender.push(<span>{value}</span>);
        }
        return showRender;
      },
    },
    {
      name: 'materialName',
      width: 200,
    },
    {
      name: 'qtyProcess',
      width: 200,
      renderer: ({ record }) => {
        if (record?.get('parentId')) {
          return record?.get('assembleQty');
        }
        return (
          <div className={styles['qty-content']}>
            <div className={styles['qty-box']}>
              <div className="qty-bg">
                <div className="qty-bg-box">
                  <div
                    className="qty-bg-inner"
                    style={{
                      width: widthFormat(record),
                    }}
                  ></div>
                </div>
              </div>
              <div className="qty-front">
                {`${record?.get('assembleQty')}/${record?.get('demandQty')}`}
              </div>
            </div>
            <Icon type="check_circle" />
          </div>
        );
      },
      align: 'center',
    },
    {
      name: 'assembleMaterialRevisionCode',
      align: 'center',
      width: 200,
      renderer: ({ record }) => {
        const showRender = [];
        if (record?.get('assembleMaterialRevisionCode')) {
          showRender.push(<span>{`${record?.get('assembleMaterialCode')}/${record?.get('assembleMaterialRevisionCode')}`}</span>);
        } else {
          showRender.push(<span>{record?.get('assembleMaterialCode')}</span>);
        }
        return showRender;
      },
    },
    {
      name: 'assembleMaterialName',
      align: 'center',
      width: 200,
    },
    {
      name: 'ecnFlag',
      align: 'center',
      width: 100,
    },
    {
      name: 'ecnRemark',
      align: 'center',
      width: 100,
    },
    {
      name: 'scrappedQty',
      align: 'center',
      width: 100,
      renderder: ({ value }) => <span style={{ color: value > 0 ? 'red' : '#fff' }}>value</span>,
    },
  ];

  return (
    <CardLayout.Layout spinning={loading} className={styles.processComponents}>
      <CardLayout.Header className='ProcessComponentsHead' title="在制品工序组件" help={props?.cardUsage?.remark} />
      <CardLayout.Content className='ProcessComponentsForm'>
        {/* {workOrderData?.eoId && (
          <>
            <Row style={{ color: 'white' }}>
              <Col span={8}></Col>
              <Col span={4}>&nbsp;&nbsp;&nbsp;单位用量</Col>
              <Col span={4}>&nbsp;&nbsp;&nbsp;已投数量</Col>
              <Col span={4}>&nbsp;&nbsp;&nbsp;投料进度</Col>
              <Col span={4}>&nbsp;&nbsp;&nbsp;状态</Col>
            </Row>
            <Row style={{ marginTop: '5px' }}>
              <Col span={24}>
                <Tree
                  defaultExpandAll
                  dataSet={treeDs}
                  renderer={nodeRenderer}
                  selectable={false}
                />
              </Col>
            </Row>
          </>
        )} */}
        <Table dataSet={tableDs} columns={columns} mode='tree' expandIcon={expandIcon} id={styles.ProcessComponents} customizedCode="ProcessComponents"/>
      </CardLayout.Content>
    </CardLayout.Layout>
  );
};

export default formatterCollections({ code: ['model.org.monitor'] })(ProcessComponents);
