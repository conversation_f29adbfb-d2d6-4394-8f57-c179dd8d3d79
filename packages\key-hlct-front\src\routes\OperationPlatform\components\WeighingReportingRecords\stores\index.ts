/**
 * @Description:
 * @Author: <<EMAIL>>
 * @Date: 2023-07-26 15:35:22
 * @LastEditTime: 2023-08-02 18:18:48
 * @LastEditors: <<EMAIL>>
 */
import intl from 'utils/intl';
import { DataSetSelection, FieldType } from 'choerodon-ui/pro/lib/data-set/enum';
import { DataSetProps } from 'choerodon-ui/pro/lib/data-set/DataSet';
import { getCurrentOrganizationId } from 'utils/utils';
import { BASIC } from '@utils/config';

const modelPrompt = 'tarzan.operationPlatform.weighingReport';
const tenantId = getCurrentOrganizationId();

const detailDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrder.product`).d('生产工单'),
    },
    {
      name: 'materialLot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLot`).d('物料批'),
      dynamicProps: {
        disabled({ record }) {
          return !record.get('workOrderId');
        },
      },
    },
    {
      name: 'barCode',
    },
    {
      name: 'weighingWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.weighingWeight`).d('称重重量'),
      dynamicProps: {
        disabled({ record }) {
          return !record.get('workOrderId');
        },
      },
    },
    {
      name: 'batch',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.batch`).d('生产批次'),
    },
    {
      name: 'bagWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.bagWeight`).d('吨袋重量'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'palletBarCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.palletBarCode`).d('托盘条码'),
      dynamicProps: {
        disabled({ record, dataSet }) {
          return !record.get('workOrderId') || dataSet.getState('disabled');
        },
      },
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'palletWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.palletWeight`).d('托盘重量'),
    },
    {
      name: 'woQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.totalNumber`).d('订单总数'),
    },
    {
      name: 'netWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.netWeight`).d('净重量'),
    },
    {
      name: 'woCompletedQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.computedQty`).d('已完成数'),
    },
    {
      name: 'transformNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.transformNumber`).d('转换个数'),
    },
    {
      name: 'packedNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.packedNumber`).d('已装袋数'),
    },
    {
      name: 'weighingWeight2',
      type: FieldType.number,
    },
    {
      name: 'weighingNum',
      type: FieldType.number,
    },
    {
      name: 'containerId',
      type: FieldType.string,
    },
    {
      name: 'unitWeight',
      type: FieldType.number,
    },
  ],
  events: {
    update: ({ record, name }) => {
      if (
        (name === 'weighingWeight' || name === 'palletWeight' || name === 'bagWeight') &&
        record.get('weighingWeight')
      ) {
        const _palletWeight = record.get('palletWeight') || 0;
        const _bagWeight = record.get('bagWeight') || 0;
        const _netWeight = record.get('weighingWeight') - (_palletWeight + _bagWeight);
        record.set('netWeight', _netWeight.toFixed(2));
      }
      if (
        name === 'netWeight' &&
        record.get('weighingWeight2') &&
        record.get('weighingNum') &&
        record.get('netWeight')
      ) {
        const _weighingWeight = record.get('weighingWeight2') || 0;
        const _weighingNum = record.get('weighingNum') || 1;
        const _netWeight = record.get('netWeight') || 0;
        const coefficient = _weighingWeight / _weighingNum;
        const _transformNumber = (_netWeight / coefficient || 1).toFixed(2);
        record.set('transformNumber', Math.ceil(Number(_transformNumber)));
      }
    },
  },
});

const tableDS: () => DataSetProps = () => ({
  selection: DataSetSelection.single,
  autoQuery: false,
  autoCreate: false,
  paging: false,
  dataKey: 'content',
  totalKey: 'totalElements',
  fields: [
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'productionDate',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.completionDate`).d('完工时间'),
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCode`).d('物料编码'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'primaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('重量'),
    },
    {
      name: 'secondaryUomQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.secondaryUomQty`).d('数量'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'containerCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.containerCode`).d('托盘编码'),
    },
    {
      name: 'createdByName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.createdByName`).d('操作者'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-wo-report/order/lov`,
        method: 'POST',
      };
    },
  },
});

const processDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'materialLotId',
    },
    {
      name: 'requestTypeCode',
      label: intl.get(`${modelPrompt}.scan.barcode`).d('扫描条码'),
    },
    {
      name: 'barCode',
      label: intl.get(`${modelPrompt}.barCode`).d('条码'),
      required: true,
    },
    {
      name: 'qty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qty`).d('数量'),
      dynamicProps: {
        disabled({ record }) {
          return !record.get('barCode');
        },
        max: ({ record }) => {
          return record.get('maxQty');
        },
      },
      min: 1,
      step: 1,
    },
    {
      name: 'maxQty',
      label: intl.get(`${modelPrompt}.barCode.qty`).d('条码数量'),
    },
  ],
});

const stepDS: () => DataSetProps = () => ({
  selection: false,
  autoQuery: false,
  autoCreate: true,
  paging: false,
  forceValidate: true,
  dataKey: 'rows',
  fields: [
    {
      name: 'routerStep',
      label: intl.get(`${modelPrompt}.routerStep`).d('工序'),
    },
  ],
});

const printDS: () => DataSetProps = () => ({
  selection: DataSetSelection.multiple,
  autoQuery: false,
  autoCreate: true,
  paging: true,
  primaryKey: 'materialLotId',
  // cacheSelection: true,
  // cacheModified: true,
  dataKey: 'rows.content',
  totalKey: 'rows.totalElements',
  fields: [
    {
      name: 'materialLotId',
    },
    {
      name: 'identification',
      label: intl.get(`${modelPrompt}.barCode`).d('条码'),
    },
    {
      name: 'primaryUomQty',
      label: intl.get(`${modelPrompt}.primaryUomQty`).d('数量'),
    },
    {
      name: 'status',
      label: intl.get(`${modelPrompt}.status`).d('状态'),
    },
    {
      name: 'qualityStatusDesc',
      label: intl.get(`${modelPrompt}.qualityStatusDesc`).d('质量状态'),
    },
    {
      name: 'printTimes',
      label: intl.get(`${modelPrompt}.printTimes`).d('打印次数'),
    },
    {
      name: 'printStatus',
      label: intl.get(`${modelPrompt}.printStatus`).d('打印状态'),
    },
    {
      name: 'productionDate',
      label: intl.get(`${modelPrompt}.productionDate`).d('更新日期'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-wo-report/print/list/get`,
        method: 'GET',
      };
    },
  },
});

const materialDS = (): DataSetProps => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  fields: [
    {
      name: 'materialLotId',
    },
    {
      name: 'identification',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.barCode`).d('条码'),
    },
    {
      name: 'scanQty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.scanQty`).d('数量'),
      min: 0,
      dynamicProps: {
        disabled: ({ record }) => {
          return !record.get('materialLotId');
        },
      },
    },
    {
      name: 'materialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.material`).d('物料'),
    },
    {
      name: 'materialCodeInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialCodeInfo`).d('物料/版本'),
    },
    {
      name: 'lot',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.lot`).d('批次'),
    },
    {
      name: 'qtyInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.qtyInfo`).d('数量'),
    },
    {
      name: 'locatorCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.locator`).d('货位'),
    },
    {
      name: 'qualityStatus',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.quality.status`).d('质量状态'),
    },
    {
      name: 'substituteMaterialCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.substituteMaterialCode`).d('物料批编码'),
    },
  ],
});

const workOrderLovDS: () => DataSetProps = () => ({
  selection: DataSetSelection.single,
  autoQuery: false,
  autoCreate: false,
  paging: true,
  dataKey: 'content',
  queryFields: [
    {
      name: 'workOrderNum',
      labelWidth: '100',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderCode`).d('工单编码'),
    },
    {
      name: 'materialInfo',
      labelWidth: '150',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialInfo`).d('物料编码/版本'),
    },
  ],
  fields: [
    {
      name: 'materialId',
    },
    {
      name: 'materialInfo',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialInfo`).d('物料编码/版本'),
    },
    {
      name: 'materialName',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialName`).d('物料描述'),
    },
    {
      name: 'planTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planTime`).d('计划开始时间-结束时间'),
    },
    {
      name: 'planStartTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planStartTime`).d('计划开始时间'),
    },
    {
      name: 'planEndTime',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.planEndTime`).d('计划结束时间'),
    },
    {
      name: 'revisionCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.revisionCode`).d('版本'),
    },
    {
      name: 'woRenderQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woRenderQty`).d('完工/工单数量'),
    },
    {
      name: 'woCompletedQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woCompletedQty`).d('工单完工数量'),
    },
    {
      name: 'woQty',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.woQty`).d('工单数量'),
    },
    {
      name: 'workOrderId',
    },
    {
      name: 'workOrderNum',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.workOrderCode`).d('工单编码'),
    },
  ],
  transport: {
    read: () => {
      return {
        url: `${BASIC.HMES_BASIC}/v1/${getCurrentOrganizationId()}/hme-wo-report/order/lov/new`,
        method: 'POST',
      };
    },
  },
});

const editModalDetailDS: () => DataSetProps = () => ({
  autoQuery: false,
  autoCreate: true,
  paging: false,
  selection: false,
  fields: [
    {
      name: 'atPresentBagWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.atPresentBagWeight`).d('吨袋当前重量'),
    },
    {
      name: 'editBagWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.editWeight`).d('修改重量'),
      min: 0,
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('type') === 'bagWeight',
      },
    },
    {
      name: 'atPresentPalletWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.atPresentPalletWeight`).d('托盘当前重量'),
    },
    {
      name: 'editPalletWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.editWeight`).d('修改重量'),
      min: 0,
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('type') === 'palletWeight',
      },
    },
    {
      name: 'weighingNum',
      type: FieldType.number,
      min: 0,
      label: intl.get(`${modelPrompt}.weighingNum`).d('称重个数'),
    },
    {
      name: 'weighingWeight',
      type: FieldType.number,
      min: 0,
      label: intl.get(`${modelPrompt}.weighingWeight`).d('称重重量'),
    },
    {
      name: 'unitWeight',
      type: FieldType.number,
      min: 0,
      label: intl.get(`${modelPrompt}.unitWeight`).d('单位重量'),
    },
    {
      name: 'transformNumber',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.transformNumber`).d('转换个数'),
    },
    {
      name: 'netWeight',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.netWeight`).d('净重量'),
    },
    {
      name: 'materialLotCode',
      type: FieldType.string,
      label: intl.get(`${modelPrompt}.materialLotCode`).d('物料批编码'),
    },
    {
      name: 'qty',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.materialLotQty`).d('物料批数量'),
    },
    {
      name: 'returnNum',
      type: FieldType.number,
      label: intl.get(`${modelPrompt}.returnNum`).d('退回数量'),
      min: 0,
      max: 'qty',
      dynamicProps: {
        required: ({ dataSet }) => dataSet.getState('type') === 'doBack',
      },
    },
  ],
  events: {
    update: ({ record, name }) => {
      if (
        (name === 'weighingWeight' || name === 'weighingNum' || name === 'netWeight') &&
        record.get('weighingWeight') &&
        record.get('weighingNum') &&
        record.get('netWeight')
      ) {
        const _weighingWeight = record.get('weighingWeight') || 0;
        const _weighingNum = record.get('weighingNum') || 1;
        const _netWeight = record.get('netWeight') || 0;
        const coefficient = _weighingWeight / _weighingNum;
        const _transformNumber = (_netWeight / coefficient || 1).toFixed(2);
        record.set('unitWeight', coefficient.toFixed(2));
        record.set('transformNumber', Math.ceil(Number(_transformNumber)));
      }
    },
  },
});

export {
  detailDS,
  tableDS,
  processDS,
  printDS,
  stepDS,
  materialDS,
  workOrderLovDS,
  editModalDetailDS,
};
